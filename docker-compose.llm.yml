# XCerberus Local LLM Stack
# Docker Compose configuration for deploying open-source LLMs
# with Ollama and custom reasoning models

version: '3.8'

services:
  # Ollama Service - Main LLM Runtime
  ollama:
    image: ollama/ollama:latest
    container_name: xcerberus-ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
      - ./ollama-models:/models
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_ORIGINS=*
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    networks:
      - llm-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3

  # LLM API Gateway - Custom API layer for XCerberus
  llm-gateway:
    build:
      context: ./llm-services
      dockerfile: Dockerfile.gateway
    container_name: xcerberus-llm-gateway
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - OLLAMA_URL=http://ollama:11434
      - REDIS_URL=redis://redis:6379
      - NODE_ENV=production
      - API_KEY=${LLM_API_KEY}
      - RATE_LIMIT_REQUESTS=1000
      - RATE_LIMIT_WINDOW=3600
    depends_on:
      - ollama
      - redis
    volumes:
      - ./llm-services/config:/app/config
      - ./logs:/app/logs
    networks:
      - llm-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: xcerberus-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    networks:
      - llm-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Vector Database for RAG (Retrieval Augmented Generation)
  qdrant:
    image: qdrant/qdrant:latest
    container_name: xcerberus-qdrant
    restart: unless-stopped
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    networks:
      - llm-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Embedding Service for vector generation
  embedding-service:
    build:
      context: ./llm-services
      dockerfile: Dockerfile.embeddings
    container_name: xcerberus-embeddings
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - QDRANT_URL=http://qdrant:6333
      - MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
      - BATCH_SIZE=32
      - MAX_SEQUENCE_LENGTH=512
    depends_on:
      - qdrant
    volumes:
      - embedding_models:/app/models
    networks:
      - llm-network
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G

  # Reasoning Engine - Advanced reasoning capabilities
  reasoning-engine:
    build:
      context: ./llm-services
      dockerfile: Dockerfile.reasoning
    container_name: xcerberus-reasoning
    restart: unless-stopped
    ports:
      - "8082:8082"
    environment:
      - OLLAMA_URL=http://ollama:11434
      - REDIS_URL=redis://redis:6379
      - REASONING_MODEL=llama3.1:70b-instruct
      - THINKING_MODEL=qwen2.5:32b-instruct
      - MAX_REASONING_STEPS=10
      - TEMPERATURE=0.1
    depends_on:
      - ollama
      - redis
    volumes:
      - ./llm-services/reasoning:/app/reasoning
    networks:
      - llm-network

  # Model Manager - Automatic model downloading and management
  model-manager:
    build:
      context: ./llm-services
      dockerfile: Dockerfile.manager
    container_name: xcerberus-model-manager
    restart: unless-stopped
    environment:
      - OLLAMA_URL=http://ollama:11434
      - AUTO_DOWNLOAD=true
      - MODELS_TO_DOWNLOAD=llama3.1:8b,llama3.1:70b,qwen2.5:32b,codellama:13b,mistral:7b
      - STORAGE_LIMIT=500GB
    depends_on:
      - ollama
    volumes:
      - ollama_data:/root/.ollama
      - ./scripts:/app/scripts
    networks:
      - llm-network

  # Monitoring and Metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: xcerberus-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - llm-network

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: xcerberus-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - llm-network

  # Load Balancer for LLM requests
  nginx:
    image: nginx:alpine
    container_name: xcerberus-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - llm-gateway
      - embedding-service
      - reasoning-engine
    networks:
      - llm-network

volumes:
  ollama_data:
    driver: local
  redis_data:
    driver: local
  qdrant_data:
    driver: local
  embedding_models:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  llm-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
