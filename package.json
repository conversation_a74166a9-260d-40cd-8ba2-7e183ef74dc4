{"name": "cyber-learning-platform", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "train": "node src/lib/trainModel.js", "supabase:setup": "bash scripts/setup-local-supabase.sh", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:status": "supabase status", "supabase:reset": "supabase db reset", "db:migrate": "supabase db push", "dev:local": "cp .env.local .env && npm run dev", "test:supabase": "node scripts/test-supabase-connection.js", "migrate": "node scripts/run-migrations.js", "setup": "npm run migrate && npm run dev"}, "dependencies": {"@google/generative-ai": "^0.2.1", "@openzeppelin/contracts": "^4.9.3", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.39.3", "@tailwindcss/forms": "^0.5.7", "@walletconnect/web3-provider": "^1.8.0", "autoprefixer": "^10.4.17", "axios": "^1.8.4", "d3": "^7.8.5", "d3-geo": "^3.1.1", "date-fns": "^3.3.1", "ethers": "^5.8.0", "framer-motion": "^10.18.0", "howler": "^2.2.4", "matter-js": "^0.19.0", "postcss": "^8.4.33", "react": "^18.2.0", "react-countup": "^6.5.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-icons": "^5.0.1", "react-intersection-observer": "^9.5.3", "react-particles": "^2.12.2", "react-router-dom": "^6.21.3", "react-simple-maps": "^3.0.0", "recharts": "^2.12.1", "socket.io-client": "^4.7.4", "tailwindcss": "^3.4.1", "three": "^0.155.0", "three-globe": "^2.42.3", "tone": "^14.7.77", "topojson-client": "^3.1.0", "tsparticles": "^2.12.0", "typed.js": "^2.1.0", "web3modal": "^1.9.12", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-search": "^0.13.0", "xterm-addon-unicode11": "^0.6.0", "xterm-addon-web-links": "^0.9.0", "zustand": "^4.5.1"}, "devDependencies": {"@nomiclabs/hardhat-ethers": "^2.2.3", "@nomiclabs/hardhat-waffle": "^2.0.6", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "chai": "^4.3.8", "ethereum-waffle": "^4.0.10", "hardhat": "^2.17.1", "rollup-plugin-visualizer": "^5.14.0", "vite": "^5.0.8", "vite-plugin-compression2": "^1.3.3"}}