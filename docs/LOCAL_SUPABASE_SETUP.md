# Local Supabase Setup Guide

This guide explains how to set up and use Supabase (Open Source Community Edition) locally for development.

## Prerequisites

1. **Docker Desktop** - Must be installed and running
   - Download from: https://www.docker.com/products/docker-desktop/
   - Ensure Docker is running before proceeding

2. **Supabase CLI** - Already installed via Homebrew
   ```bash
   brew install supabase/tap/supabase
   ```

3. **Node.js** - Version 14 or higher

## Quick Setup

### Option 1: Automated Setup (Recommended)

Run the automated setup script:

```bash
npm run supabase:setup
```

This script will:
- Initialize Supabase if not already done
- Start the local Supabase stack
- Generate `.env.local` with correct local credentials
- Display access information

### Option 2: Manual Setup

1. **Initialize Supabase** (if not already done):
   ```bash
   supabase init
   ```

2. **Start Supabase**:
   ```bash
   supabase start
   ```

3. **Get credentials**:
   ```bash
   supabase status
   ```

4. **Update environment variables**:
   Copy the API URL and anon key from `supabase status` to `.env.local`

## Environment Configuration

### Local Development (.env.local)

```env
# Local Supabase Configuration
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=<your-local-anon-key>
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_KEY=<your-local-anon-key>

# Keep existing API keys
VITE_GEMINI_API_KEY=AIzaSyCjrRX2vRmhFk1OQxHLleXMq0pGJC6xXrM
VITE_ABUSEIPDB_API_KEY=********************************************************************************
VITE_OTX_API_KEY=437e960dfa3557cd103b887b8948554f86a97f7ab562a13e546311a1cc5cebe0
```

### Switching Between Local and Remote

- **Use Local Supabase**: `cp .env.local .env`
- **Use Remote Supabase**: Keep original `.env` file

## Available Commands

```bash
# Setup and start local Supabase
npm run supabase:setup

# Start Supabase stack
npm run supabase:start

# Stop Supabase stack
npm run supabase:stop

# Check status
npm run supabase:status

# Reset database (careful!)
npm run supabase:reset

# Push migrations to local DB
npm run db:migrate

# Start dev server with local env
npm run dev:local
```

## Access Points

When Supabase is running locally:

- **Supabase Studio**: http://localhost:54323
- **API**: http://127.0.0.1:54321
- **Database**: postgresql://postgres:postgres@localhost:54322/postgres
- **Inbucket (Email testing)**: http://localhost:54324

## Database Schema

The existing schema files in `/supabase/` will be automatically applied:

- `schema.sql` - Main database schema
- `migrations/` - All migration files
- Various schema files for specific features

## Troubleshooting

### Docker Issues

If you encounter Docker-related errors:

1. **Restart Docker Desktop**
2. **Clean Docker system**:
   ```bash
   docker system prune -f
   ```
3. **Check Docker is running**:
   ```bash
   docker info
   ```

### Port Conflicts

Default ports used by Supabase:
- API: 54321
- Database: 54322
- Studio: 54323
- Inbucket: 54324

If these ports are in use, modify `supabase/config.toml`

### Migration Issues

If database migrations fail:

1. **Reset database**:
   ```bash
   npm run supabase:reset
   ```

2. **Check migration files** in `supabase/migrations/`

3. **Apply migrations manually**:
   ```bash
   npm run db:migrate
   ```

## Development Workflow

1. **Start local Supabase**:
   ```bash
   npm run supabase:start
   ```

2. **Switch to local environment**:
   ```bash
   cp .env.local .env
   ```

3. **Start development server**:
   ```bash
   npm run dev
   ```

4. **Access Supabase Studio** for database management:
   http://localhost:54323

## Benefits of Local Development

- **Faster development** - No network latency
- **Offline development** - Work without internet
- **Safe testing** - No risk to production data
- **Cost effective** - No API usage charges
- **Full control** - Complete access to all features

## Migration from Remote to Local

Your existing database schema and migrations are preserved. The local setup uses the same schema files, ensuring consistency between local and remote environments.
