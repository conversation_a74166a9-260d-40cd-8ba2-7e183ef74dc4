#!/bin/bash

# XCerberus Enhanced Platform Setup Script
# This script sets up the complete enhanced platform with analytics, AI, and monitoring

set -e

echo "🚀 Setting up XCerberus Enhanced Platform..."
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_status "Docker and Docker Compose are installed ✓"
}

# Check if Node.js is installed
check_nodejs() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_status "Node.js $(node --version) is installed ✓"
}

# Install Node.js dependencies
install_dependencies() {
    print_header "📦 Installing Node.js dependencies..."
    
    if [ ! -f "package.json" ]; then
        print_error "package.json not found. Are you in the correct directory?"
        exit 1
    fi
    
    npm install
    print_status "Dependencies installed ✓"
}

# Setup environment variables
setup_environment() {
    print_header "⚙️ Setting up environment variables..."
    
    # Create enhanced environment file
    cat > .env.enhanced << EOF
# XCerberus Enhanced Platform Configuration

# Basic Configuration
VITE_APP_NAME=XCerberus Enhanced
VITE_APP_VERSION=2.0.0
VITE_NODE_ENV=development

# API Configuration
VITE_API_URL=http://localhost:3001
VITE_WS_URL=ws://localhost:3001

# Analytics Configuration
VITE_ANALYTICS_ENABLED=true
VITE_ANALYTICS_API_URL=http://localhost:3001/api/analytics
VITE_ANALYTICS_WS_URL=ws://localhost:3001/ws/analytics

# LLM Configuration
VITE_LLM_ENABLED=true
VITE_LLM_API_URL=http://localhost:8080
VITE_OLLAMA_URL=http://localhost:11434
VITE_REASONING_URL=http://localhost:8082
VITE_EMBEDDING_URL=http://localhost:8081

# Web3 Configuration
VITE_XCYBER_TOKEN_ADDRESS=0x1234567890123456789012345678901234567890
VITE_CERTIFICATES_ADDRESS=0x2345678901234567890123456789012345678901
VITE_MARKETPLACE_ADDRESS=0x3456789012345678901234567890123456789012
VITE_DAO_ADDRESS=0x4567890123456789012345678901234567890123
VITE_CHAIN_ID=1337
VITE_NETWORK_NAME=localhost
VITE_ENABLE_MOCK_WEB3=true

# Monitoring Configuration
VITE_MONITORING_ENABLED=true
VITE_PERFORMANCE_TRACKING=true
VITE_ERROR_TRACKING=true

# Feature Flags
VITE_ENABLE_AI_CHATBOT=true
VITE_ENABLE_3D_VISUALIZATION=true
VITE_ENABLE_VR_FEATURES=true
VITE_ENABLE_ADVANCED_ANALYTICS=true
VITE_ENABLE_SUPER_ADMIN=true

# Security
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS=false

# Development
VITE_DEBUG_MODE=true
VITE_SHOW_PERFORMANCE_METRICS=true
EOF

    # Copy to .env.local if it doesn't exist
    if [ ! -f ".env.local" ]; then
        cp .env.enhanced .env.local
        print_status "Environment file created ✓"
    else
        print_warning "Environment file already exists. Enhanced config saved as .env.enhanced"
    fi
}

# Create necessary directories
create_directories() {
    print_header "📁 Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p data/analytics
    mkdir -p data/llm
    mkdir -p data/monitoring
    mkdir -p scripts
    mkdir -p monitoring/prometheus
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    mkdir -p nginx
    mkdir -p llm-services
    
    print_status "Directories created ✓"
}

# Setup LLM services
setup_llm_services() {
    print_header "🤖 Setting up LLM services..."
    
    # Create LLM service directories and basic files
    mkdir -p llm-services/gateway
    mkdir -p llm-services/reasoning
    mkdir -p llm-services/embeddings
    mkdir -p llm-services/manager
    
    # Create basic Dockerfile for LLM gateway
    cat > llm-services/Dockerfile.gateway << EOF
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .

EXPOSE 8080

CMD ["npm", "start"]
EOF

    # Create basic package.json for LLM services
    cat > llm-services/package.json << EOF
{
  "name": "xcerberus-llm-services",
  "version": "1.0.0",
  "description": "XCerberus LLM Services",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "axios": "^1.5.0",
    "redis": "^4.6.0",
    "ws": "^8.14.0"
  }
}
EOF

    print_status "LLM services structure created ✓"
}

# Setup monitoring configuration
setup_monitoring() {
    print_header "📊 Setting up monitoring configuration..."
    
    # Create Prometheus configuration
    cat > monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'xcerberus-platform'
    static_configs:
      - targets: ['localhost:5174']
  
  - job_name: 'llm-gateway'
    static_configs:
      - targets: ['llm-gateway:8080']
  
  - job_name: 'ollama'
    static_configs:
      - targets: ['ollama:11434']
EOF

    # Create Grafana datasource configuration
    cat > monitoring/grafana/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

    print_status "Monitoring configuration created ✓"
}

# Setup Nginx configuration
setup_nginx() {
    print_header "🌐 Setting up Nginx configuration..."
    
    cat > nginx/nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    upstream llm_backend {
        server llm-gateway:8080;
        server reasoning-engine:8082;
    }
    
    upstream embedding_backend {
        server embedding-service:8081;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location /api/llm/ {
            proxy_pass http://llm_backend/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
        }
        
        location /api/embed/ {
            proxy_pass http://embedding_backend/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
        }
        
        location / {
            proxy_pass http://host.docker.internal:5174;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
        }
    }
}
EOF

    print_status "Nginx configuration created ✓"
}

# Create startup scripts
create_scripts() {
    print_header "📝 Creating startup scripts..."
    
    # Create LLM startup script
    cat > scripts/start-llm.sh << 'EOF'
#!/bin/bash

echo "🤖 Starting LLM services..."

# Start Docker Compose for LLM services
docker-compose -f docker-compose.llm.yml up -d

echo "⏳ Waiting for services to start..."
sleep 30

# Download models
echo "📥 Downloading AI models..."
docker exec xcerberus-ollama ollama pull llama3.1:8b
docker exec xcerberus-ollama ollama pull qwen2.5:32b

echo "✅ LLM services started successfully!"
EOF

    # Create platform startup script
    cat > scripts/start-platform.sh << 'EOF'
#!/bin/bash

echo "🚀 Starting XCerberus Enhanced Platform..."

# Start LLM services first
./scripts/start-llm.sh

# Start the main application
echo "🌐 Starting main application..."
npm run dev

echo "✅ Platform started successfully!"
echo "🌐 Access the platform at: http://localhost:5174"
echo "🤖 AI services at: http://localhost:8080"
echo "📊 Monitoring at: http://localhost:3000"
EOF

    # Create stop script
    cat > scripts/stop-all.sh << 'EOF'
#!/bin/bash

echo "🛑 Stopping all services..."

# Stop Docker services
docker-compose -f docker-compose.llm.yml down

# Kill any remaining processes
pkill -f "npm run dev" || true
pkill -f "vite" || true

echo "✅ All services stopped!"
EOF

    # Make scripts executable
    chmod +x scripts/*.sh
    
    print_status "Startup scripts created ✓"
}

# Test the setup
test_setup() {
    print_header "🧪 Testing the setup..."
    
    # Test if we can build the project
    print_status "Testing build process..."
    npm run build > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        print_status "Build test passed ✓"
    else
        print_warning "Build test failed - check for any missing dependencies"
    fi
    
    # Test Docker Compose file
    if [ -f "docker-compose.llm.yml" ]; then
        docker-compose -f docker-compose.llm.yml config > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            print_status "Docker Compose configuration valid ✓"
        else
            print_warning "Docker Compose configuration has issues"
        fi
    fi
}

# Main setup function
main() {
    print_header "🚀 XCerberus Enhanced Platform Setup"
    echo "======================================"
    
    # Run setup steps
    check_docker
    check_nodejs
    install_dependencies
    setup_environment
    create_directories
    setup_llm_services
    setup_monitoring
    setup_nginx
    create_scripts
    test_setup
    
    print_header "🎉 Setup Complete!"
    echo "=================="
    print_status "Your XCerberus Enhanced Platform is ready!"
    echo ""
    print_status "Next steps:"
    echo "  1. Start LLM services: ./scripts/start-llm.sh"
    echo "  2. Start the platform: npm run dev"
    echo "  3. Access the platform: http://localhost:5174"
    echo "  4. Access Ultimate Admin: http://localhost:5174/admin/ultimate"
    echo ""
    print_status "Or start everything at once: ./scripts/start-platform.sh"
    echo ""
    print_warning "Note: First-time LLM model download may take 10-30 minutes"
    print_warning "depending on your internet connection."
}

# Run main function
main "$@"
