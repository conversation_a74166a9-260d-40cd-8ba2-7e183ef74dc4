// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Counters.sol";

/**
 * XCerberus Web3 Ecosystem Smart Contracts
 * 
 * Complete decentralized cybersecurity education platform with:
 * - NFT Certificates and Achievements
 * - Native Token Economy (XCYBER)
 * - Decentralized Content Marketplace
 * - Skill-based NFTs and Reputation System
 * - DAO Governance for Platform Evolution
 */

// Native Platform Token
contract XCyberToken is ERC20, Ownable {
    uint256 public constant MAX_SUPPLY = ********** * 10**18; // 1 billion tokens
    uint256 public constant INITIAL_SUPPLY = 100000000 * 10**18; // 100 million initial
    
    mapping(address => bool) public minters;
    mapping(address => uint256) public stakingRewards;
    mapping(address => uint256) public lastStakeTime;
    
    event TokensEarned(address indexed user, uint256 amount, string reason);
    event TokensStaked(address indexed user, uint256 amount);
    event RewardsClaimed(address indexed user, uint256 amount);
    
    constructor() ERC20("XCyber Token", "XCYBER") {
        _mint(msg.sender, INITIAL_SUPPLY);
    }
    
    function addMinter(address minter) external onlyOwner {
        minters[minter] = true;
    }
    
    function mintReward(address to, uint256 amount, string memory reason) external {
        require(minters[msg.sender], "Not authorized to mint");
        require(totalSupply() + amount <= MAX_SUPPLY, "Max supply exceeded");
        
        _mint(to, amount);
        emit TokensEarned(to, amount, reason);
    }
    
    function stakeTokens(uint256 amount) external {
        require(balanceOf(msg.sender) >= amount, "Insufficient balance");
        
        _transfer(msg.sender, address(this), amount);
        stakingRewards[msg.sender] += amount;
        lastStakeTime[msg.sender] = block.timestamp;
        
        emit TokensStaked(msg.sender, amount);
    }
    
    function calculateStakingReward(address user) public view returns (uint256) {
        uint256 stakedAmount = stakingRewards[user];
        uint256 timeStaked = block.timestamp - lastStakeTime[user];
        
        // 10% APY for staking
        return (stakedAmount * timeStaked * 10) / (365 days * 100);
    }
    
    function claimStakingRewards() external {
        uint256 reward = calculateStakingReward(msg.sender);
        require(reward > 0, "No rewards to claim");
        
        lastStakeTime[msg.sender] = block.timestamp;
        _mint(msg.sender, reward);
        
        emit RewardsClaimed(msg.sender, reward);
    }
}

// NFT Certificates and Achievements
contract XCerberusCertificates is ERC721, Ownable, ReentrancyGuard {
    using Counters for Counters.Counter;
    Counters.Counter private _tokenIds;
    
    struct Certificate {
        string certificationType; // "course", "certification", "achievement", "skill_badge"
        string title;
        string description;
        string skillsProven;
        uint256 issueDate;
        uint256 expiryDate;
        string metadataURI;
        address issuer;
        bool isVerified;
        uint256 skillLevel; // 1-100
    }
    
    struct SkillNFT {
        string skillName;
        uint256 proficiencyLevel; // 1-100
        uint256 lastUpdated;
        string[] endorsements;
        uint256 practiceHours;
    }
    
    mapping(uint256 => Certificate) public certificates;
    mapping(uint256 => SkillNFT) public skillNFTs;
    mapping(address => uint256[]) public userCertificates;
    mapping(address => mapping(string => uint256)) public userSkillLevels;
    mapping(address => bool) public authorizedIssuers;
    
    XCyberToken public xcyberToken;
    
    event CertificateIssued(
        address indexed recipient,
        uint256 indexed tokenId,
        string certificationType,
        string title
    );
    
    event SkillLevelUpdated(
        address indexed user,
        string skill,
        uint256 newLevel,
        uint256 tokenId
    );
    
    constructor(address _xcyberToken) ERC721("XCerberus Certificates", "XCERT") {
        xcyberToken = XCyberToken(_xcyberToken);
    }
    
    function addAuthorizedIssuer(address issuer) external onlyOwner {
        authorizedIssuers[issuer] = true;
    }
    
    function issueCertificate(
        address recipient,
        string memory certificationType,
        string memory title,
        string memory description,
        string memory skillsProven,
        uint256 expiryDate,
        string memory metadataURI,
        uint256 skillLevel
    ) external returns (uint256) {
        require(authorizedIssuers[msg.sender] || msg.sender == owner(), "Not authorized");
        
        _tokenIds.increment();
        uint256 newTokenId = _tokenIds.current();
        
        _mint(recipient, newTokenId);
        
        certificates[newTokenId] = Certificate({
            certificationType: certificationType,
            title: title,
            description: description,
            skillsProven: skillsProven,
            issueDate: block.timestamp,
            expiryDate: expiryDate,
            metadataURI: metadataURI,
            issuer: msg.sender,
            isVerified: true,
            skillLevel: skillLevel
        });
        
        userCertificates[recipient].push(newTokenId);
        
        // Reward tokens for earning certificate
        uint256 rewardAmount = skillLevel * 10 * 10**18; // 10 tokens per skill level
        xcyberToken.mintReward(recipient, rewardAmount, "Certificate earned");
        
        emit CertificateIssued(recipient, newTokenId, certificationType, title);
        
        return newTokenId;
    }
    
    function updateSkillLevel(
        address user,
        string memory skillName,
        uint256 newLevel,
        uint256 practiceHours
    ) external {
        require(authorizedIssuers[msg.sender], "Not authorized");
        require(newLevel <= 100, "Invalid skill level");
        
        uint256 currentLevel = userSkillLevels[user][skillName];
        
        if (newLevel > currentLevel) {
            userSkillLevels[user][skillName] = newLevel;
            
            // Create or update skill NFT
            _tokenIds.increment();
            uint256 skillTokenId = _tokenIds.current();
            
            _mint(user, skillTokenId);
            
            skillNFTs[skillTokenId] = SkillNFT({
                skillName: skillName,
                proficiencyLevel: newLevel,
                lastUpdated: block.timestamp,
                endorsements: new string[](0),
                practiceHours: practiceHours
            });
            
            // Reward improvement
            uint256 improvement = newLevel - currentLevel;
            uint256 rewardAmount = improvement * 5 * 10**18; // 5 tokens per level improvement
            xcyberToken.mintReward(user, rewardAmount, "Skill improvement");
            
            emit SkillLevelUpdated(user, skillName, newLevel, skillTokenId);
        }
    }
    
    function getUserCertificates(address user) external view returns (uint256[] memory) {
        return userCertificates[user];
    }
    
    function verifyCertificate(uint256 tokenId) external view returns (bool) {
        require(_exists(tokenId), "Certificate does not exist");
        Certificate memory cert = certificates[tokenId];
        
        return cert.isVerified && 
               (cert.expiryDate == 0 || cert.expiryDate > block.timestamp);
    }
}

// Decentralized Content Marketplace
contract XCerberusMarketplace is Ownable, ReentrancyGuard {
    using Counters for Counters.Counter;
    Counters.Counter private _contentIds;
    
    struct Content {
        uint256 id;
        address creator;
        string title;
        string description;
        string contentType; // "course", "lab", "assessment", "tool"
        uint256 price; // in XCYBER tokens
        string metadataURI;
        bool isActive;
        uint256 totalSales;
        uint256 rating; // out of 100
        uint256 reviewCount;
    }
    
    struct Purchase {
        address buyer;
        uint256 contentId;
        uint256 purchaseDate;
        uint256 pricePaid;
        bool hasAccess;
    }
    
    mapping(uint256 => Content) public contents;
    mapping(address => uint256[]) public creatorContents;
    mapping(uint256 => Purchase[]) public contentPurchases;
    mapping(address => mapping(uint256 => bool)) public userAccess;
    mapping(address => uint256) public creatorEarnings;
    
    XCyberToken public xcyberToken;
    XCerberusCertificates public certificates;
    
    uint256 public platformFeePercentage = 10; // 10% platform fee
    
    event ContentListed(
        uint256 indexed contentId,
        address indexed creator,
        string title,
        uint256 price
    );
    
    event ContentPurchased(
        uint256 indexed contentId,
        address indexed buyer,
        address indexed creator,
        uint256 price
    );
    
    constructor(address _xcyberToken, address _certificates) {
        xcyberToken = XCyberToken(_xcyberToken);
        certificates = XCerberusCertificates(_certificates);
    }
    
    function listContent(
        string memory title,
        string memory description,
        string memory contentType,
        uint256 price,
        string memory metadataURI
    ) external returns (uint256) {
        _contentIds.increment();
        uint256 newContentId = _contentIds.current();
        
        contents[newContentId] = Content({
            id: newContentId,
            creator: msg.sender,
            title: title,
            description: description,
            contentType: contentType,
            price: price,
            metadataURI: metadataURI,
            isActive: true,
            totalSales: 0,
            rating: 0,
            reviewCount: 0
        });
        
        creatorContents[msg.sender].push(newContentId);
        
        emit ContentListed(newContentId, msg.sender, title, price);
        
        return newContentId;
    }
    
    function purchaseContent(uint256 contentId) external nonReentrant {
        Content storage content = contents[contentId];
        require(content.isActive, "Content not available");
        require(!userAccess[msg.sender][contentId], "Already purchased");
        
        uint256 price = content.price;
        require(xcyberToken.balanceOf(msg.sender) >= price, "Insufficient tokens");
        
        // Transfer tokens
        uint256 platformFee = (price * platformFeePercentage) / 100;
        uint256 creatorPayment = price - platformFee;
        
        xcyberToken.transferFrom(msg.sender, address(this), platformFee);
        xcyberToken.transferFrom(msg.sender, content.creator, creatorPayment);
        
        // Grant access
        userAccess[msg.sender][contentId] = true;
        content.totalSales++;
        creatorEarnings[content.creator] += creatorPayment;
        
        // Record purchase
        contentPurchases[contentId].push(Purchase({
            buyer: msg.sender,
            contentId: contentId,
            purchaseDate: block.timestamp,
            pricePaid: price,
            hasAccess: true
        }));
        
        emit ContentPurchased(contentId, msg.sender, content.creator, price);
    }
    
    function getCreatorContents(address creator) external view returns (uint256[] memory) {
        return creatorContents[creator];
    }
    
    function hasContentAccess(address user, uint256 contentId) external view returns (bool) {
        return userAccess[user][contentId];
    }
}

// DAO Governance for Platform Evolution
contract XCerberusDAO is Ownable {
    using Counters for Counters.Counter;
    Counters.Counter private _proposalIds;
    
    struct Proposal {
        uint256 id;
        address proposer;
        string title;
        string description;
        uint256 votingDeadline;
        uint256 forVotes;
        uint256 againstVotes;
        bool executed;
        mapping(address => bool) hasVoted;
        mapping(address => bool) voteChoice; // true = for, false = against
    }
    
    mapping(uint256 => Proposal) public proposals;
    mapping(address => uint256) public votingPower;
    
    XCyberToken public xcyberToken;
    XCerberusCertificates public certificates;
    
    uint256 public proposalThreshold = 1000 * 10**18; // 1000 XCYBER to propose
    uint256 public votingPeriod = 7 days;
    uint256 public quorumPercentage = 10; // 10% of total supply needed
    
    event ProposalCreated(
        uint256 indexed proposalId,
        address indexed proposer,
        string title
    );
    
    event VoteCast(
        uint256 indexed proposalId,
        address indexed voter,
        bool support,
        uint256 weight
    );
    
    constructor(address _xcyberToken, address _certificates) {
        xcyberToken = XCyberToken(_xcyberToken);
        certificates = XCerberusCertificates(_certificates);
    }
    
    function createProposal(
        string memory title,
        string memory description
    ) external returns (uint256) {
        require(
            xcyberToken.balanceOf(msg.sender) >= proposalThreshold,
            "Insufficient tokens to propose"
        );
        
        _proposalIds.increment();
        uint256 newProposalId = _proposalIds.current();
        
        Proposal storage newProposal = proposals[newProposalId];
        newProposal.id = newProposalId;
        newProposal.proposer = msg.sender;
        newProposal.title = title;
        newProposal.description = description;
        newProposal.votingDeadline = block.timestamp + votingPeriod;
        
        emit ProposalCreated(newProposalId, msg.sender, title);
        
        return newProposalId;
    }
    
    function vote(uint256 proposalId, bool support) external {
        Proposal storage proposal = proposals[proposalId];
        require(block.timestamp <= proposal.votingDeadline, "Voting period ended");
        require(!proposal.hasVoted[msg.sender], "Already voted");
        
        uint256 weight = calculateVotingPower(msg.sender);
        require(weight > 0, "No voting power");
        
        proposal.hasVoted[msg.sender] = true;
        proposal.voteChoice[msg.sender] = support;
        
        if (support) {
            proposal.forVotes += weight;
        } else {
            proposal.againstVotes += weight;
        }
        
        emit VoteCast(proposalId, msg.sender, support, weight);
    }
    
    function calculateVotingPower(address user) public view returns (uint256) {
        uint256 tokenBalance = xcyberToken.balanceOf(user);
        uint256 certificateCount = certificates.balanceOf(user);
        
        // Voting power = token balance + (certificates * 100 tokens equivalent)
        return tokenBalance + (certificateCount * 100 * 10**18);
    }
    
    function executeProposal(uint256 proposalId) external {
        Proposal storage proposal = proposals[proposalId];
        require(block.timestamp > proposal.votingDeadline, "Voting still active");
        require(!proposal.executed, "Already executed");
        
        uint256 totalVotes = proposal.forVotes + proposal.againstVotes;
        uint256 quorum = (xcyberToken.totalSupply() * quorumPercentage) / 100;
        
        require(totalVotes >= quorum, "Quorum not reached");
        require(proposal.forVotes > proposal.againstVotes, "Proposal rejected");
        
        proposal.executed = true;
        
        // Implementation of proposal execution would go here
        // This could include parameter changes, feature toggles, etc.
    }
}
