/** @type {import('tailwindcss').Config} */
export default {
  darkMode: 'class',
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      keyframes: {
        shimmer: {
          '100%': { transform: 'translateX(100%)' }
        }
      },
      animation: {
        shimmer: 'shimmer 2s infinite'
      },
      colors: {
        primary: '#88cc14',
        'primary-hover': '#7ab811',
        'cyber-black': '#0B1120',
        cyber: {
          black: '#0B1120',
          darker: '#080B14',
          green: '#88cc14',
          neon: '#00ff41',
          yellow: '#ffff00',
        },
        terminal: {
          DEFAULT: '#88cc14',
          hover: '#7ab811',
          light: 'rgba(136, 204, 20, 0.1)',
        }
      },
      fontFamily: {
        mono: ['JetBrains Mono', 'monospace'],
      },
      boxShadow: {
        'neon': '0 0 10px rgba(136, 204, 20, 0.5)',
        'neon-strong': '0 0 20px rgba(136, 204, 20, 0.7)',
      },
      animation: {
        'neon-pulse': 'neonPulse 2s infinite',
        'scanline': 'scanline 2s linear infinite',
        'float': 'float 3s ease-in-out infinite',
        'matrix-rain': 'matrixRain 20s linear infinite',
        'glitch': 'glitch 1s infinite',
        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'fadeIn': 'fadeIn 0.5s ease-in-out',
        'animation-delay-200': 'pulse 2s infinite 200ms',
        'animation-delay-400': 'pulse 2s infinite 400ms',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: 0 },
          '100%': { opacity: 1 },
        },
        neonPulse: {
          '0%, 100%': { opacity: 1 },
          '50%': { opacity: 0.5 },
        },
        scanline: {
          '0%': { transform: 'translateY(-100%)' },
          '100%': { transform: 'translateY(100%)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        matrixRain: {
          '0%': { transform: 'translateY(-100%)' },
          '100%': { transform: 'translateY(100%)' },
        },
        glitch: {
          '0%, 100%': { transform: 'translate(0)' },
          '33%': { transform: 'translate(-5px, 2px)' },
          '66%': { transform: 'translate(5px, -2px)' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
}