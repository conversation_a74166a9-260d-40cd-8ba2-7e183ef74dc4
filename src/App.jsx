import React, { lazy } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { SubscriptionProvider } from './contexts/SubscriptionContext';
import { SimpleSubscriptionProvider } from './contexts/SimpleSubscriptionContext';
import { ChatProvider } from './contexts/ChatContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { GlobalThemeProvider, useGlobalTheme } from './contexts/GlobalThemeContext';
import { AuthProvider } from './contexts/AuthContext';
import { ChallengeProvider } from './contexts/ChallengeContext';
import { LeaderboardProvider } from './contexts/LeaderboardContext';
import { LearningProvider } from './contexts/LearningContext';
import { StoreProvider } from './contexts/StoreContext';
import { AnalyticsProvider } from './contexts/AnalyticsContext';
import { GuestProgressProvider } from './contexts/GuestProgressContext';
import { DashboardProvider } from './contexts/DashboardContext';
import AdminDashboardProvider from './contexts/AdminDashboardContext';
import SimpleErrorBoundary from './components/common/SimpleErrorBoundary';
import GlobalHeader from './components/ui/GlobalHeader';
import GlobalFooter from './components/ui/GlobalFooter';
import Navbar from './components/Navbar';
import NewNavbar from './components/NewNavbar';
import Home from './pages/Home';
import LazyLoadWrapper from './components/LazyLoadWrapper';
import ProtectedRoute from './components/ProtectedRoute';
import GridOverlay from './components/GridOverlay';
import PerformanceMonitor from './components/PerformanceMonitor';
import Test from './test';

// Eagerly loaded components
import Footer from './components/layout/Footer';
import CybersecurityAI from './components/CybersecurityAI';
import FallbackPage from './components/FallbackPage';
import LearningModulePage from './pages/LearningModulePage';

// Lazy loaded pages
const Login = lazy(() => import('./pages/Login'));
const Challenges = lazy(() => import('./pages/Challenges'));
const StartHack = lazy(() => import('./pages/StartHack'));
const Leaderboard = lazy(() => import('./pages/Leaderboard'));
const Learn = lazy(() => import('./pages/Learn'));
const Profile = lazy(() => import('./pages/Profile'));
const Products = lazy(() => import('./pages/Products'));
const Pricing = lazy(() => import('./pages/Pricing'));
const Dashboard = lazy(() => import('./pages/Dashboard'));
const EnhancedDashboard = lazy(() => import('./pages/EnhancedDashboard'));
const SimplifiedDashboard = lazy(() => import('./components/dashboards/SimplifiedDashboard'));
const SecurityInsights = lazy(() => import('./pages/SecurityInsights'));
const EnhancedSecurityInsights = lazy(() => import('./pages/EnhancedSecurityInsights'));
const LearnModules = lazy(() => import('./pages/LearnModules'));
const About = lazy(() => import('./pages/About'));
const TestPage = lazy(() => import('./pages/TestPage'));
const SimpleHome = lazy(() => import('./pages/SimpleHome'));
const XCerberusLanding = lazy(() => import('./pages/XCerberusLanding'));
const Blog = lazy(() => import('./pages/Blog'));
const Store = lazy(() => import('./pages/Store'));
const SimpleLogin = lazy(() => import('./pages/SimpleLogin'));
const SimpleSignup = lazy(() => import('./pages/SimpleSignup'));
const SimpleDashboard = lazy(() => import('./pages/SimpleDashboard'));
const SimplePricing = lazy(() => import('./pages/SimplePricing'));
const SimpleLearnModules = lazy(() => import('./pages/SimpleLearnModules'));
const SimpleLearning = lazy(() => import('./pages/SimpleLearning'));
const SimpleChallenges = lazy(() => import('./pages/SimpleChallenges'));
const EnhancedChallenges = lazy(() => import('./pages/EnhancedChallenges'));
const StaticChallenges = lazy(() => import('./pages/StaticChallenges'));
const StaticLearning = lazy(() => import('./pages/StaticLearning'));
const ModulePreview = lazy(() => import('./pages/ModulePreview'));
const TestLogin = lazy(() => import('./pages/TestLogin'));
const ChallengeSimulator = lazy(() => import('./components/challenges/ChallengeSimulator'));
const SimpleLeaderboard = lazy(() => import('./pages/SimpleLeaderboard'));
const SimpleContact = lazy(() => import('./pages/SimpleContact'));
const AdminDashboard = lazy(() => import('./pages/admin/AdminDashboard'));
const SuperAdminDashboard = lazy(() => import('./pages/admin/SuperAdminDashboard'));
const EnhancedSuperAdminDashboard = lazy(() => import('./pages/admin/EnhancedSuperAdminDashboard'));
const EnhancedAdminDashboard = lazy(() => import('./pages/admin/EnhancedAdminDashboard'));
const ModernAdminDashboard = lazy(() => import('./pages/admin/ModernAdminDashboard'));
const SimpleAdminDashboard = lazy(() => import('./pages/admin/SimpleAdminDashboard'));
const TeamDashboard = lazy(() => import('./pages/teams/TeamDashboard'));
const TeamChat = lazy(() => import('./pages/teams/TeamChat'));
const DashboardInventory = lazy(() => import('./components/navigation/DashboardInventory'));
const DashboardNavigator = lazy(() => import('./components/navigation/DashboardNavigator'));
const QuickDashboardAccess = lazy(() => import('./components/navigation/QuickDashboardAccess'));

// New Core Features
const ContentManagementDashboard = lazy(() => import('./components/content/ContentManagementDashboard'));
const AdvancedAnalyticsDashboard = lazy(() => import('./components/analytics/AdvancedAnalyticsDashboard'));
const VirtualLabsDashboard = lazy(() => import('./components/labs/VirtualLabsDashboard'));
const CertificationDashboard = lazy(() => import('./components/certification/CertificationDashboard'));
const AssessmentDashboard = lazy(() => import('./components/assessment/AssessmentDashboard'));
const ContentMarketplaceDashboard = lazy(() => import('./components/marketplace/ContentMarketplaceDashboard'));
const NetworkVisualization3D = lazy(() => import('./components/vr/NetworkVisualization3D'));
const MobileOptimizedDashboard = lazy(() => import('./components/mobile/MobileOptimizedDashboard'));
const SubscriptionManagement = lazy(() => import('./pages/SubscriptionManagement'));
const SubscriptionDebug = lazy(() => import('./pages/SubscriptionDebug'));

// New global pages with role-based access
const GlobalLearn = lazy(() => import('./pages/GlobalLearn'));
const GlobalChallenges = lazy(() => import('./pages/GlobalChallenges'));
const GlobalStartHack = lazy(() => import('./pages/GlobalStartHack'));

// Wrapper component to use the theme context
const GlobalHeaderWrapper = () => {
  return <GlobalHeader />;
};

const GlobalFooterWrapper = () => {
  return <Footer />;
};

function App() {
  return (
    <SimpleErrorBoundary componentName="App">
      <GlobalThemeProvider>
        <AuthProvider>
          <ThemeProvider>
            <SimpleSubscriptionProvider>
              <SubscriptionProvider>
                <DashboardProvider>
                  <AdminDashboardProvider>
                    <ChallengeProvider>
                      <LeaderboardProvider>
                        <LearningProvider>
                          <StoreProvider>
                            <AnalyticsProvider>
                              <GuestProgressProvider>
                                <ChatProvider>
            <Router>
              <div className="min-h-screen bg-white text-gray-900 relative overflow-hidden">
                {/* Simple Grid Background */}
                <GridOverlay />
                <PerformanceMonitor />

                {/* Main Content */}
                <SimpleErrorBoundary componentName="MainContent">
                  <div className="relative z-50">
                    {/* Use GlobalHeader with theme toggle */}
                    <Routes>
                      <Route path="/dashboard/*" element={null} />
                      <Route path="/simplified-dashboard" element={null} />
                      <Route path="/enhanced-dashboard/*" element={null} />
                      <Route path="*" element={<GlobalHeaderWrapper />} />
                    </Routes>

                <Routes>
                  <Route path="/" element={<LazyLoadWrapper><XCerberusLanding /></LazyLoadWrapper>} />
                  <Route path="/login" element={<LazyLoadWrapper><SimpleLogin /></LazyLoadWrapper>} />
                  <Route path="/signup" element={<LazyLoadWrapper><SimpleSignup /></LazyLoadWrapper>} />
                  <Route path="/simplified-dashboard" element={<LazyLoadWrapper><SimpleDashboard /></LazyLoadWrapper>} />
                  <Route path="/security-insights" element={<LazyLoadWrapper><SecurityInsights /></LazyLoadWrapper>} />
                  <Route path="/enhanced-security-insights" element={<LazyLoadWrapper><EnhancedSecurityInsights /></LazyLoadWrapper>} />
                  <Route path="/blog" element={<LazyLoadWrapper><Blog /></LazyLoadWrapper>} />
                  <Route path="/challenges" element={<LazyLoadWrapper><StaticChallenges /></LazyLoadWrapper>} />
                  <Route path="/challenges/:challengeId" element={<LazyLoadWrapper><ChallengeSimulator /></LazyLoadWrapper>} />
                  <Route path="/games" element={<LazyLoadWrapper><StartHack /></LazyLoadWrapper>} />
                  <Route path="/leaderboard" element={<LazyLoadWrapper><SimpleLeaderboard /></LazyLoadWrapper>} />
                  <Route path="/learn" element={<LazyLoadWrapper><StaticLearning /></LazyLoadWrapper>} />
                  <Route path="/learn/preview/:moduleSlug" element={<LazyLoadWrapper><ModulePreview /></LazyLoadWrapper>} />
                  <Route path="/learn/:moduleId" element={<LazyLoadWrapper><LearningModulePage /></LazyLoadWrapper>} />
                  <Route path="/products" element={<LazyLoadWrapper><Products /></LazyLoadWrapper>} />
                  <Route path="/pricing" element={<LazyLoadWrapper><SimplePricing /></LazyLoadWrapper>} />
                  <Route path="/store" element={<LazyLoadWrapper><Store /></LazyLoadWrapper>} />
                  <Route path="/contact" element={<LazyLoadWrapper><SimpleContact /></LazyLoadWrapper>} />
                  <Route path="/about" element={<LazyLoadWrapper><About /></LazyLoadWrapper>} />
                  <Route path="/test" element={<TestPage />} />
                  <Route path="/test-component" element={<Test />} />
                  <Route path="/test-login" element={<LazyLoadWrapper><TestLogin /></LazyLoadWrapper>} />
                  <Route path="/subscription-debug" element={<LazyLoadWrapper><SubscriptionDebug /></LazyLoadWrapper>} />

                  {/* New global pages with role-based access */}
                  <Route path="/global-learn" element={<LazyLoadWrapper><GlobalLearn /></LazyLoadWrapper>} />
                  <Route path="/global-challenges" element={<LazyLoadWrapper><GlobalChallenges /></LazyLoadWrapper>} />
                  <Route path="/global-start-hack" element={<LazyLoadWrapper><GlobalStartHack /></LazyLoadWrapper>} />
                  <Route path="/dashboard-inventory" element={<LazyLoadWrapper><DashboardInventory /></LazyLoadWrapper>} />

                  {/* Core Platform Features */}
                  <Route path="/content-management" element={
                    <LazyLoadWrapper><ContentManagementDashboard /></LazyLoadWrapper>
                  } />
                  <Route path="/analytics-dashboard" element={
                    <LazyLoadWrapper><AdvancedAnalyticsDashboard /></LazyLoadWrapper>
                  } />
                  <Route path="/virtual-labs" element={
                    <LazyLoadWrapper><VirtualLabsDashboard /></LazyLoadWrapper>
                  } />
                  <Route path="/certifications" element={
                    <LazyLoadWrapper><CertificationDashboard /></LazyLoadWrapper>
                  } />
                  <Route path="/assessments" element={
                    <LazyLoadWrapper><AssessmentDashboard /></LazyLoadWrapper>
                  } />
                  <Route path="/marketplace" element={
                    <LazyLoadWrapper><ContentMarketplaceDashboard /></LazyLoadWrapper>
                  } />
                  <Route path="/network-visualization" element={
                    <LazyLoadWrapper><NetworkVisualization3D /></LazyLoadWrapper>
                  } />
                  <Route path="/mobile-dashboard" element={
                    <LazyLoadWrapper><MobileOptimizedDashboard /></LazyLoadWrapper>
                  } />

                  <Route path="/dashboard" element={
                    <ProtectedRoute>
                      <LazyLoadWrapper><Dashboard /></LazyLoadWrapper>
                    </ProtectedRoute>
                  } />
                  <Route path="/enhanced-dashboard/*" element={
                    <ProtectedRoute>
                      <LazyLoadWrapper><EnhancedDashboard /></LazyLoadWrapper>
                    </ProtectedRoute>
                  } />
                  <Route path="/simplified-dashboard" element={
                    <ProtectedRoute>
                      <LazyLoadWrapper><SimplifiedDashboard userData={{ username: 'chitti.gouthamkumar', coins: 100, completedChallenges: 0, totalPoints: 0 }} /></LazyLoadWrapper>
                    </ProtectedRoute>
                  } />
                  <Route path="/learn/modules" element={
                    <ProtectedRoute>
                      <LazyLoadWrapper><SimpleLearnModules /></LazyLoadWrapper>
                    </ProtectedRoute>
                  } />
                  <Route path="/profile" element={
                    <ProtectedRoute>
                      <LazyLoadWrapper><Profile /></LazyLoadWrapper>
                    </ProtectedRoute>
                  } />
                  <Route path="/subscription" element={
                    <ProtectedRoute>
                      <LazyLoadWrapper><SubscriptionManagement /></LazyLoadWrapper>
                    </ProtectedRoute>
                  } />
                  <Route path="/admin" element={
                    <ProtectedRoute>
                      <LazyLoadWrapper><SimpleAdminDashboard /></LazyLoadWrapper>
                    </ProtectedRoute>
                  } />
                  <Route path="/admin-modern" element={
                    <ProtectedRoute>
                      <LazyLoadWrapper><ModernAdminDashboard /></LazyLoadWrapper>
                    </ProtectedRoute>
                  } />
                  <Route path="/admin-old" element={
                    <ProtectedRoute>
                      <LazyLoadWrapper><EnhancedAdminDashboard /></LazyLoadWrapper>
                    </ProtectedRoute>
                  } />
                  <Route path="/super-admin" element={
                    <LazyLoadWrapper><EnhancedSuperAdminDashboard /></LazyLoadWrapper>
                  } />
                  <Route path="/super-admin-old" element={
                    <ProtectedRoute>
                      <LazyLoadWrapper><SuperAdminDashboard /></LazyLoadWrapper>
                    </ProtectedRoute>
                  } />
                  <Route path="/teams" element={
                    <ProtectedRoute>
                      <LazyLoadWrapper><TeamDashboard /></LazyLoadWrapper>
                    </ProtectedRoute>
                  } />
                  <Route path="/teams/:teamId" element={
                    <ProtectedRoute>
                      <LazyLoadWrapper><TeamChat /></LazyLoadWrapper>
                    </ProtectedRoute>
                  } />
                </Routes>

                {/* Show Footer on all routes except dashboards */}
                <Routes>
                  <Route path="/dashboard/*" element={null} />
                  <Route path="/simplified-dashboard" element={null} />
                  <Route path="/enhanced-dashboard/*" element={null} />
                  <Route path="*" element={<GlobalFooterWrapper />} />
                </Routes>

                {/* Cybersecurity AI Assistant */}
                <CybersecurityAI />

                {/* Dashboard Navigator - Global Navigation */}
                <DashboardNavigator />

                {/* Quick Dashboard Access - Bottom Left */}
                <QuickDashboardAccess />
                  </div>
                </SimpleErrorBoundary>
              </div>
            </Router>
                                </ChatProvider>
                              </GuestProgressProvider>
                            </AnalyticsProvider>
                          </StoreProvider>
                        </LearningProvider>
                      </LeaderboardProvider>
                    </ChallengeProvider>
                  </AdminDashboardProvider>
                </DashboardProvider>
              </SubscriptionProvider>
            </SimpleSubscriptionProvider>
          </ThemeProvider>
        </AuthProvider>
      </GlobalThemeProvider>
    </SimpleErrorBoundary>
  );
}



export default App;