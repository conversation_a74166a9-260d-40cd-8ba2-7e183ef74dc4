import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with retry logic and environment detection
const createSupabaseClient = () => {
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Supabase environment variables are not set');
    console.log('Available env vars:', {
      VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,
      VITE_SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY ? '[REDACTED]' : 'undefined'
    });
    throw new Error('Supabase configuration missing');
  }

  // Detect if we're using local Supabase
  const isLocalSupabase = supabaseUrl.includes('127.0.0.1') || supabaseUrl.includes('localhost');

  console.log(`🔧 Initializing Supabase client:`, {
    url: supabaseUrl,
    environment: isLocalSupabase ? 'LOCAL' : 'REMOTE',
    timestamp: new Date().toISOString()
  });

  const clientConfig = {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
      storage: {
        getItem: (key) => {
          try {
            return localStorage.getItem(key);
          } catch (error) {
            console.error('Error accessing localStorage:', error);
            return null;
          }
        },
        setItem: (key, value) => {
          try {
            localStorage.setItem(key, value);
          } catch (error) {
            console.error('Error setting localStorage:', error);
          }
        },
        removeItem: (key) => {
          try {
            localStorage.removeItem(key);
          } catch (error) {
            console.error('Error removing from localStorage:', error);
          }
        }
      }
    }
  };

  // Add additional configuration for local development
  if (isLocalSupabase) {
    clientConfig.db = {
      schema: 'public'
    };
    clientConfig.global = {
      headers: {
        'X-Client-Info': 'xcerberus-local-dev'
      }
    };
  }

  return createClient(supabaseUrl, supabaseAnonKey, clientConfig);
};

// Create Supabase client with error handling
let supabaseInstance = null;
try {
  supabaseInstance = createSupabaseClient();
} catch (error) {
  console.error('Failed to initialize Supabase client:', error);
  // Create a mock client for fallback
  supabaseInstance = {
    auth: {
      getSession: async () => ({ data: { session: null }, error: null }),
      onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } }),
      signUp: async () => ({ data: null, error: new Error('Supabase not initialized') }),
      signIn: async () => ({ data: null, error: new Error('Supabase not initialized') }),
      signOut: async () => ({ error: new Error('Supabase not initialized') })
    },
    from: () => ({
      select: () => ({ data: [], error: new Error('Supabase not initialized') }),
      insert: () => ({ data: null, error: new Error('Supabase not initialized') }),
      update: () => ({ data: null, error: new Error('Supabase not initialized') }),
      delete: () => ({ data: null, error: new Error('Supabase not initialized') }),
      rpc: () => ({ data: null, error: new Error('Supabase not initialized') })
    })
  };
}

export const supabase = supabaseInstance;

// Safe query wrapper with retry mechanism
export const safeQuery = async (operation, maxRetries = 3) => {
  let lastError;

  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await operation();
      return result;
    } catch (error) {
      console.error(`Query failed (attempt ${i + 1}/${maxRetries}):`, error);
      lastError = error;

      // Wait before retrying
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
  }

  throw lastError;
};

// Auth functions
export const signIn = async (email, password) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) throw error;

    // Set session in localStorage
    if (data.session) {
      localStorage.setItem('supabase.auth.token', data.session.access_token);
      localStorage.setItem('supabase.auth.refreshToken', data.session.refresh_token);
      localStorage.setItem('supabase.auth.user', JSON.stringify(data.session.user));
    }

    return data;
  } catch (error) {
    console.error('Sign in error:', error);
    throw error;
  }
};

export const signUp = async (email, password, username, fullName) => {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          username,
          full_name: fullName
        }
      }
    });

    if (error) throw error;

    // Set session in localStorage if available
    if (data.session) {
      localStorage.setItem('supabase.auth.token', data.session.access_token);
      localStorage.setItem('supabase.auth.refreshToken', data.session.refresh_token);
      localStorage.setItem('supabase.auth.user', JSON.stringify(data.session.user));
    }

    return data;
  } catch (error) {
    console.error('Sign up error:', error);
    throw error;
  }
};

export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;

    // Clear all auth data from localStorage
    localStorage.removeItem('supabase.auth.token');
    localStorage.removeItem('supabase.auth.refreshToken');
    localStorage.removeItem('supabase.auth.user');
    localStorage.removeItem('supabase.auth.expires_at');
  } catch (error) {
    console.error('Sign out error:', error);
    throw error;
  }
};

// Get current session
export const getSession = async () => {
  try {
    // Check localStorage first
    const token = localStorage.getItem('supabase.auth.token');
    const refreshToken = localStorage.getItem('supabase.auth.refreshToken');

    if (!token || !refreshToken) {
      return null;
    }

    const { data: { session }, error } = await supabase.auth.getSession();

    if (error) {
      // Try to refresh session
      const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession({
        refresh_token: refreshToken
      });

      if (refreshError) {
        throw refreshError;
      }

      if (refreshData.session) {
        localStorage.setItem('supabase.auth.token', refreshData.session.access_token);
        localStorage.setItem('supabase.auth.refreshToken', refreshData.session.refresh_token);
        localStorage.setItem('supabase.auth.user', JSON.stringify(refreshData.session.user));
        return refreshData.session;
      }
    }

    return session;
  } catch (error) {
    console.error('Get session error:', error);
    // Clear invalid session
    localStorage.removeItem('supabase.auth.token');
    localStorage.removeItem('supabase.auth.refreshToken');
    localStorage.removeItem('supabase.auth.user');
    localStorage.removeItem('supabase.auth.expires_at');
    return null;
  }
};

// Database operation helpers
export const getOpenChallenges = async () => {
  return safeQuery(() =>
    supabase
      .from('challenges')
      .select('*')
      .eq('is_open', true)
      .order('created_at', { ascending: false })
  );
};

export const getUserChallenges = async () => {
  return safeQuery(async () => {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) throw new Error('Not authenticated');

    return supabase
      .from('challenge_submissions')
      .select(`
        *,
        challenges (*)
      `)
      .eq('user_id', session.user.id)
      .order('submission_time', { ascending: false });
  });
};

export const submitChallenge = async (challengeId, status, pointsEarned = 0) => {
  return safeQuery(async () => {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) throw new Error('Not authenticated');

    return supabase
      .from('challenge_submissions')
      .insert([{
        user_id: session.user.id,
        challenge_id: challengeId,
        status,
        points_earned: pointsEarned,
        completion_time: status === 'completed' ? new Date() : null
      }])
      .select();
  });
};

// User profile helpers
export const getUserProfile = async () => {
  return safeQuery(async () => {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) throw new Error('Not authenticated');

    return supabase
      .from('users')
      .select('*')
      .eq('id', session.user.id)
      .single();
  });
};

// User coins helpers
export const getUserCoins = async () => {
  return safeQuery(async () => {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) throw new Error('Not authenticated');

    return supabase
      .from('user_coins')
      .select('*')
      .eq('user_id', session.user.id)
      .single();
  });
};

// Orders helpers
export const getOrders = async () => {
  return safeQuery(async () => {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) throw new Error('Not authenticated');

    return supabase
      .from('orders')
      .select(`
        *,
        order_items (
          *,
          products (*),
          product_variants (*)
        )
      `)
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false });
  });
};

// Products helpers
export const getProducts = async () => {
  return safeQuery(() =>
    supabase
      .from('products')
      .select(`
        *,
        product_variants (*)
      `)
      .order('created_at', { ascending: false })
  );
};

// Leaderboard helpers
export const getLeaderboard = async () => {
  return safeQuery(() =>
    supabase
      .from('leaderboard')
      .select(`
        *,
        users (username, avatar_url)
      `)
      .order('rank', { ascending: true })
      .limit(100)
  );
};

// Learning modules helpers
export const getLearningModules = async (isAuthenticated = false) => {
  // If not authenticated, return only free modules or previews
  if (!isAuthenticated) {
    return safeQuery(() =>
      supabase
        .from('learning_modules')
        .select(`
          *,
          module_sections (*),
          module_categories (*)
        `)
        .eq('is_preview', true)
        .order('display_order', { ascending: true })
    );
  }

  // If authenticated, get user's subscription tier and return appropriate modules
  return safeQuery(async () => {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) throw new Error('Not authenticated');

    // Get user's subscription tier
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('subscription_tier')
      .eq('id', session.user.id)
      .single();

    if (userError) throw userError;

    const tier = userData?.subscription_tier || 'free';

    // Return modules based on subscription tier
    if (tier === 'premium' || tier === 'business') {
      // Premium and business users get all modules
      return supabase
        .from('learning_modules')
        .select(`
          *,
          module_sections (*),
          module_categories (*)
        `)
        .order('display_order', { ascending: true });
    } else {
      // Free users get only free modules
      return supabase
        .from('learning_modules')
        .select(`
          *,
          module_sections (*),
          module_categories (*)
        `)
        .or('is_free.eq.true,is_preview.eq.true')
        .order('display_order', { ascending: true });
    }
  });
};

// Get specific learning module
export const getLearningModule = async (moduleId, isAuthenticated = false) => {
  // If not authenticated, return only if it's a free or preview module
  if (!isAuthenticated) {
    return safeQuery(() =>
      supabase
        .from('learning_modules')
        .select(`
          *,
          module_sections (*, module_content (*)),
          module_categories (*)
        `)
        .eq('id', moduleId)
        .or('is_free.eq.true,is_preview.eq.true')
        .single()
    );
  }

  // If authenticated, check user's subscription tier
  return safeQuery(async () => {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) throw new Error('Not authenticated');

    // Get user's subscription tier
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('subscription_tier')
      .eq('id', session.user.id)
      .single();

    if (userError) throw userError;

    const tier = userData?.subscription_tier || 'free';

    // Get the module
    const { data: moduleData, error: moduleError } = await supabase
      .from('learning_modules')
      .select(`
        *,
        module_sections (*, module_content (*)),
        module_categories (*)
      `)
      .eq('id', moduleId)
      .single();

    if (moduleError) throw moduleError;

    // Check if user has access to this module
    if (tier === 'premium' || tier === 'business') {
      // Premium and business users get all modules
      return { data: moduleData, error: null };
    } else if (moduleData.is_free || moduleData.is_preview) {
      // Free users get only free or preview modules
      return { data: moduleData, error: null };
    } else {
      // User doesn't have access to this module
      throw new Error('Subscription required to access this module');
    }
  });
};

// Challenge helpers
export const getChallenges = async (isAuthenticated = false) => {
  // If not authenticated, return only free challenges or previews
  if (!isAuthenticated) {
    return safeQuery(() =>
      supabase
        .from('challenges')
        .select(`
          *,
          challenge_categories (*)
        `)
        .eq('is_preview', true)
        .order('difficulty', { ascending: true })
    );
  }

  // If authenticated, get user's subscription tier and return appropriate challenges
  return safeQuery(async () => {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) throw new Error('Not authenticated');

    // Get user's subscription tier
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('subscription_tier')
      .eq('id', session.user.id)
      .single();

    if (userError) throw userError;

    const tier = userData?.subscription_tier || 'free';

    // Return challenges based on subscription tier
    if (tier === 'premium' || tier === 'business') {
      // Premium and business users get all challenges
      return supabase
        .from('challenges')
        .select(`
          *,
          challenge_categories (*)
        `)
        .order('difficulty', { ascending: true });
    } else {
      // Free users get only free challenges
      return supabase
        .from('challenges')
        .select(`
          *,
          challenge_categories (*)
        `)
        .or('is_free.eq.true,is_preview.eq.true')
        .order('difficulty', { ascending: true });
    }
  });
};

// Get specific challenge
export const getChallenge = async (challengeId, isAuthenticated = false) => {
  // If not authenticated, return only if it's a free or preview challenge
  if (!isAuthenticated) {
    return safeQuery(() =>
      supabase
        .from('challenges')
        .select(`
          *,
          challenge_categories (*),
          challenge_content (*)
        `)
        .eq('id', challengeId)
        .or('is_free.eq.true,is_preview.eq.true')
        .single()
    );
  }

  // If authenticated, check user's subscription tier
  return safeQuery(async () => {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) throw new Error('Not authenticated');

    // Get user's subscription tier
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('subscription_tier')
      .eq('id', session.user.id)
      .single();

    if (userError) throw userError;

    const tier = userData?.subscription_tier || 'free';

    // Get the challenge
    const { data: challengeData, error: challengeError } = await supabase
      .from('challenges')
      .select(`
        *,
        challenge_categories (*),
        challenge_content (*)
      `)
      .eq('id', challengeId)
      .single();

    if (challengeError) throw challengeError;

    // Check if user has access to this challenge
    if (tier === 'premium' || tier === 'business') {
      // Premium and business users get all challenges
      return { data: challengeData, error: null };
    } else if (challengeData.is_free || challengeData.is_preview) {
      // Free users get only free or preview challenges
      return { data: challengeData, error: null };
    } else {
      // User doesn't have access to this challenge
      throw new Error('Subscription required to access this challenge');
    }
  });
};