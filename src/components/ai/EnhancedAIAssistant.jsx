import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaRobot, FaPaperPlane, FaMicrophone, FaStop, FaLightbulb,
  FaBook, FaCode, FaLaptop, FaCertificate, FaChartLine,
  FaUser, FaBrain, FaGraduationCap, FaTarget, FaHistory
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import enhancedAIService from '../../services/enhancedAIService';

/**
 * Enhanced AI Assistant Component
 * 
 * Advanced AI chat interface with context awareness, personalized responses,
 * and intelligent tutoring capabilities.
 */
const EnhancedAIAssistant = ({ isOpen, onClose }) => {
  const { darkMode } = useGlobalTheme();
  const { user } = useAuth();
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [userContext, setUserContext] = useState(null);
  const [suggestions, setSuggestions] = useState([]);
  const [activeTab, setActiveTab] = useState('chat');
  const messagesEndRef = useRef(null);
  const recognitionRef = useRef(null);

  // Initialize AI context when component mounts
  useEffect(() => {
    if (user?.id && isOpen) {
      initializeAIContext();
    }
  }, [user?.id, isOpen]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize speech recognition
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = false;
      recognitionRef.current.lang = 'en-US';

      recognitionRef.current.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setInputMessage(transcript);
        setIsListening(false);
      };

      recognitionRef.current.onerror = () => {
        setIsListening(false);
      };

      recognitionRef.current.onend = () => {
        setIsListening(false);
      };
    }
  }, []);

  const initializeAIContext = async () => {
    try {
      const context = await enhancedAIService.initializeUserContext(user.id);
      setUserContext(context);
      
      // Add welcome message with personalized context
      const welcomeMessage = {
        id: Date.now(),
        type: 'ai',
        content: getPersonalizedWelcome(context),
        timestamp: new Date().toISOString(),
        context: {
          skillLevel: context.skillLevel,
          suggestions: getInitialSuggestions(context)
        }
      };
      
      setMessages([welcomeMessage]);
      setSuggestions(getInitialSuggestions(context));
    } catch (error) {
      console.error('Error initializing AI context:', error);
    }
  };

  const getPersonalizedWelcome = (context) => {
    const skillLevel = context.skillLevel || 'beginner';
    const name = user?.user_metadata?.full_name || 'there';
    
    const welcomeMessages = {
      beginner: `Hi ${name}! 👋 I'm your AI cybersecurity tutor. I see you're just starting your journey - that's exciting! I'm here to help you learn at your own pace. What would you like to explore today?`,
      intermediate: `Welcome back, ${name}! 🎯 I can see you've been making great progress in your cybersecurity studies. Based on your recent activity, I'm ready to help you tackle more advanced concepts. What's on your mind?`,
      advanced: `Hello ${name}! 🚀 Great to see an advanced learner here. I'm equipped to discuss complex cybersecurity topics and can help you dive deep into specialized areas. What challenging topic shall we explore?`,
      expert: `Greetings ${name}! 🎖️ I recognize your expertise level. I'm here to discuss cutting-edge cybersecurity concepts, emerging threats, and advanced techniques. What would you like to analyze today?`
    };
    
    return welcomeMessages[skillLevel] || welcomeMessages.beginner;
  };

  const getInitialSuggestions = (context) => {
    const skillLevel = context.skillLevel || 'beginner';
    
    const suggestionsByLevel = {
      beginner: [
        "What is cybersecurity?",
        "How do I start learning ethical hacking?",
        "Explain common web vulnerabilities",
        "Show me a simple lab exercise"
      ],
      intermediate: [
        "Advanced SQL injection techniques",
        "How to use Metasploit effectively?",
        "Network penetration testing methodology",
        "Recommend certification path"
      ],
      advanced: [
        "Zero-day exploit development",
        "Advanced persistent threat analysis",
        "Red team engagement strategies",
        "Cloud security assessment"
      ],
      expert: [
        "Quantum cryptography implications",
        "AI-powered attack vectors",
        "Advanced malware analysis",
        "Threat intelligence automation"
      ]
    };
    
    return suggestionsByLevel[skillLevel] || suggestionsByLevel.beginner;
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response = await enhancedAIService.enhancedChat(user.id, inputMessage);
      
      if (response.success) {
        const aiMessage = {
          id: Date.now() + 1,
          type: 'ai',
          content: response.content,
          timestamp: new Date().toISOString(),
          suggestions: response.suggestions,
          resources: response.resources,
          nextSteps: response.nextSteps,
          context: response.context
        };
        
        setMessages(prev => [...prev, aiMessage]);
        setSuggestions(response.suggestions || []);
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      const errorMessage = {
        id: Date.now() + 1,
        type: 'ai',
        content: "I apologize, but I'm having trouble processing your request right now. Please try again in a moment.",
        timestamp: new Date().toISOString(),
        isError: true
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestionClick = (suggestion) => {
    setInputMessage(suggestion);
  };

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      setIsListening(true);
      recognitionRef.current.start();
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
      setIsListening(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const renderMessage = (message) => (
    <motion.div
      key={message.id}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} mb-4`}
    >
      <div className={`max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
        {message.type === 'ai' && (
          <div className="flex items-center mb-2">
            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#88cc14] to-[#7ab512] flex items-center justify-center mr-2">
              <FaRobot className="text-black text-sm" />
            </div>
            <span className="text-sm text-gray-500">AI Assistant</span>
            {message.context?.skillLevel && (
              <span className={`ml-2 px-2 py-1 rounded text-xs ${
                message.context.skillLevel === 'expert' ? 'bg-purple-500/20 text-purple-500' :
                message.context.skillLevel === 'advanced' ? 'bg-red-500/20 text-red-500' :
                message.context.skillLevel === 'intermediate' ? 'bg-yellow-500/20 text-yellow-500' :
                'bg-green-500/20 text-green-500'
              }`}>
                {message.context.skillLevel}
              </span>
            )}
          </div>
        )}
        
        <div className={`p-4 rounded-lg ${
          message.type === 'user'
            ? 'bg-[#88cc14] text-black'
            : message.isError
              ? darkMode ? 'bg-red-900/20 border border-red-500/30' : 'bg-red-50 border border-red-200'
              : darkMode ? 'bg-[#1A1F35] border border-gray-700' : 'bg-white border border-gray-200'
        }`}>
          <div className="whitespace-pre-wrap">{message.content}</div>
          
          {/* AI Message Enhancements */}
          {message.type === 'ai' && !message.isError && (
            <div className="mt-4 space-y-3">
              {/* Resources */}
              {message.resources && message.resources.length > 0 && (
                <div>
                  <h4 className="text-sm font-semibold mb-2 flex items-center">
                    <FaBook className="mr-2" />
                    Recommended Resources
                  </h4>
                  <div className="space-y-1">
                    {message.resources.map((resource, index) => (
                      <a
                        key={index}
                        href={resource.url}
                        className="block text-sm text-[#88cc14] hover:underline"
                      >
                        📚 {resource.title}
                      </a>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Next Steps */}
              {message.nextSteps && message.nextSteps.length > 0 && (
                <div>
                  <h4 className="text-sm font-semibold mb-2 flex items-center">
                    <FaTarget className="mr-2" />
                    Next Steps
                  </h4>
                  <ul className="space-y-1">
                    {message.nextSteps.map((step, index) => (
                      <li key={index} className="text-sm flex items-start">
                        <span className="text-[#88cc14] mr-2">{index + 1}.</span>
                        {step}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              {/* Personalized Tips */}
              {message.context?.personalizedTips && message.context.personalizedTips.length > 0 && (
                <div>
                  <h4 className="text-sm font-semibold mb-2 flex items-center">
                    <FaLightbulb className="mr-2" />
                    Personalized Tips
                  </h4>
                  <div className="space-y-1">
                    {message.context.personalizedTips.map((tip, index) => (
                      <div key={index} className="text-sm text-gray-600 dark:text-gray-400">
                        {tip}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
        
        {message.type === 'user' && (
          <div className="flex items-center justify-end mt-2">
            <span className="text-sm text-gray-500 mr-2">You</span>
            <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
              <FaUser className="text-white text-sm" />
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );

  const renderAnalytics = () => (
    <div className="p-4 space-y-4">
      <h3 className="text-lg font-semibold mb-4">Learning Analytics</h3>
      
      {userContext && (
        <div className="space-y-4">
          {/* Skill Level */}
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-gray-50'}`}>
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">Current Skill Level</span>
              <span className={`px-3 py-1 rounded-full text-sm ${
                userContext.skillLevel === 'expert' ? 'bg-purple-500/20 text-purple-500' :
                userContext.skillLevel === 'advanced' ? 'bg-red-500/20 text-red-500' :
                userContext.skillLevel === 'intermediate' ? 'bg-yellow-500/20 text-yellow-500' :
                'bg-green-500/20 text-green-500'
              }`}>
                {userContext.skillLevel}
              </span>
            </div>
          </div>
          
          {/* Strong Areas */}
          {userContext.strongAreas && userContext.strongAreas.length > 0 && (
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-gray-50'}`}>
              <h4 className="font-medium mb-2 flex items-center">
                <FaChartLine className="mr-2 text-green-500" />
                Strong Areas
              </h4>
              <div className="flex flex-wrap gap-2">
                {userContext.strongAreas.map((area, index) => (
                  <span key={index} className="px-2 py-1 bg-green-500/20 text-green-500 rounded text-sm">
                    {area}
                  </span>
                ))}
              </div>
            </div>
          )}
          
          {/* Areas for Improvement */}
          {userContext.weakAreas && userContext.weakAreas.length > 0 && (
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-gray-50'}`}>
              <h4 className="font-medium mb-2 flex items-center">
                <FaTarget className="mr-2 text-orange-500" />
                Areas for Improvement
              </h4>
              <div className="flex flex-wrap gap-2">
                {userContext.weakAreas.map((area, index) => (
                  <span key={index} className="px-2 py-1 bg-orange-500/20 text-orange-500 rounded text-sm">
                    {area}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50`}
    >
      <div className={`w-full max-w-4xl h-[80vh] rounded-lg ${
        darkMode ? 'bg-[#0B1120] border border-gray-700' : 'bg-white border border-gray-200'
      } flex flex-col`}>
        {/* Header */}
        <div className={`p-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} flex items-center justify-between`}>
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-[#88cc14] to-[#7ab512] flex items-center justify-center mr-3">
              <FaBrain className="text-black text-lg" />
            </div>
            <div>
              <h2 className="text-lg font-bold">Enhanced AI Assistant</h2>
              <p className="text-sm text-gray-500">Context-aware cybersecurity tutor</p>
            </div>
          </div>
          
          {/* Tabs */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => setActiveTab('chat')}
              className={`px-3 py-1 rounded text-sm ${
                activeTab === 'chat' ? 'bg-[#88cc14] text-black' : 'text-gray-500 hover:text-gray-300'
              }`}
            >
              Chat
            </button>
            <button
              onClick={() => setActiveTab('analytics')}
              className={`px-3 py-1 rounded text-sm ${
                activeTab === 'analytics' ? 'bg-[#88cc14] text-black' : 'text-gray-500 hover:text-gray-300'
              }`}
            >
              Analytics
            </button>
            <button
              onClick={onClose}
              className="ml-4 text-gray-500 hover:text-gray-300"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex">
          {/* Main Chat Area */}
          <div className="flex-1 flex flex-col">
            {activeTab === 'chat' ? (
              <>
                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-4">
                  {messages.map(renderMessage)}
                  {isLoading && (
                    <div className="flex justify-start mb-4">
                      <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-gray-100'}`}>
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#88cc14] mr-2"></div>
                          <span className="text-sm">AI is thinking...</span>
                        </div>
                      </div>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>

                {/* Input Area */}
                <div className={`p-4 border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                  {/* Suggestions */}
                  {suggestions.length > 0 && (
                    <div className="mb-3">
                      <div className="flex flex-wrap gap-2">
                        {suggestions.slice(0, 3).map((suggestion, index) => (
                          <button
                            key={index}
                            onClick={() => handleSuggestionClick(suggestion)}
                            className={`px-3 py-1 rounded-full text-sm border ${
                              darkMode 
                                ? 'border-gray-600 hover:border-[#88cc14] text-gray-300 hover:text-[#88cc14]'
                                : 'border-gray-300 hover:border-[#88cc14] text-gray-600 hover:text-[#88cc14]'
                            } transition-colors`}
                          >
                            {suggestion}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Input */}
                  <div className="flex items-center gap-2">
                    <div className="flex-1 relative">
                      <input
                        type="text"
                        value={inputMessage}
                        onChange={(e) => setInputMessage(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                        placeholder="Ask me anything about cybersecurity..."
                        className={`w-full p-3 pr-12 rounded-lg border ${
                          darkMode 
                            ? 'bg-[#1A1F35] border-gray-600 text-white placeholder-gray-400'
                            : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                        } focus:outline-none focus:border-[#88cc14]`}
                        disabled={isLoading}
                      />
                      
                      {/* Voice Input */}
                      {recognitionRef.current && (
                        <button
                          onClick={isListening ? stopListening : startListening}
                          className={`absolute right-3 top-1/2 transform -translate-y-1/2 ${
                            isListening ? 'text-red-500' : 'text-gray-400 hover:text-[#88cc14]'
                          }`}
                        >
                          {isListening ? <FaStop /> : <FaMicrophone />}
                        </button>
                      )}
                    </div>
                    
                    <button
                      onClick={handleSendMessage}
                      disabled={!inputMessage.trim() || isLoading}
                      className="bg-[#88cc14] text-black p-3 rounded-lg hover:bg-[#7ab512] disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <FaPaperPlane />
                    </button>
                  </div>
                </div>
              </>
            ) : (
              renderAnalytics()
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default EnhancedAIAssistant;
