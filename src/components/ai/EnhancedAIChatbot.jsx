import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaRobot, FaUser, FaPaperPlane, FaMicrophone, FaStop,
  FaCode, FaShieldAlt, FaBrain, FaLightbulb, FaCopy,
  FaThumbsUp, FaThumbsDown, FaExpand, FaCompress,
  FaDownload, FaShare, FaBookmark, FaCog
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import localLLMService from '../../services/LocalLLMService';

/**
 * Enhanced AI Chatbot
 * 
 * Advanced AI assistant powered by local LLMs with reasoning capabilities,
 * cybersecurity expertise, and context-aware responses.
 */
const EnhancedAIChatbot = ({ isOpen, onToggle, context = {} }) => {
  const { darkMode } = useGlobalTheme();
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [conversationId, setConversationId] = useState(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedModel, setSelectedModel] = useState('cybersecurity');
  const [showSettings, setShowSettings] = useState(false);
  
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const recognitionRef = useRef(null);

  // Initialize conversation
  useEffect(() => {
    if (isOpen && !conversationId) {
      const newConversationId = localLLMService.startConversation('user', context);
      setConversationId(newConversationId);
      
      // Add welcome message
      setMessages([{
        id: Date.now(),
        role: 'assistant',
        content: getWelcomeMessage(),
        timestamp: new Date(),
        model: selectedModel
      }]);
    }
  }, [isOpen, context]);

  // Auto-scroll to bottom
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize speech recognition
  useEffect(() => {
    if ('webkitSpeechRecognition' in window) {
      recognitionRef.current = new window.webkitSpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = false;
      recognitionRef.current.lang = 'en-US';

      recognitionRef.current.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setInputText(transcript);
        setIsListening(false);
      };

      recognitionRef.current.onerror = () => {
        setIsListening(false);
      };

      recognitionRef.current.onend = () => {
        setIsListening(false);
      };
    }
  }, []);

  /**
   * Get context-aware welcome message
   */
  const getWelcomeMessage = () => {
    const welcomeMessages = {
      cybersecurity: "🛡️ Hello! I'm your AI cybersecurity mentor. I can help you with security concepts, threat analysis, secure coding, and hands-on lab guidance. What would you like to learn today?",
      coding: "💻 Hi! I'm here to help you with secure coding practices, code reviews, and vulnerability analysis. Share your code or ask about security best practices!",
      learning: "🎓 Welcome! I'm your personalized learning assistant. I can create custom learning paths, recommend courses, and help you track your cybersecurity journey.",
      general: "🤖 Hello! I'm your AI assistant powered by advanced local LLMs. I can help with cybersecurity questions, provide reasoning-based answers, and assist with your learning goals."
    };

    return welcomeMessages[selectedModel] || welcomeMessages.general;
  };

  /**
   * Send message to AI
   */
  const sendMessage = async () => {
    if (!inputText.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      role: 'user',
      content: inputText,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    try {
      let response;
      
      // Choose appropriate AI service based on model
      switch (selectedModel) {
        case 'cybersecurity':
          response = await localLLMService.getCybersecurityAdvice(inputText, context.userLevel);
          break;
        case 'reasoning':
          response = await localLLMService.generateReasoningResponse(inputText);
          break;
        case 'coding':
          response = await localLLMService.analyzeCode(inputText, context.language);
          break;
        default:
          response = await localLLMService.generateResponse(inputText, {
            model: selectedModel,
            context: context.domain
          });
      }

      const aiMessage = {
        id: Date.now() + 1,
        role: 'assistant',
        content: response.response || response.finalAnswer,
        timestamp: new Date(),
        model: selectedModel,
        reasoning: response.reasoningSteps,
        confidence: response.confidence,
        sources: response.sources
      };

      setMessages(prev => [...prev, aiMessage]);
      
      // Add to conversation history
      if (conversationId) {
        localLLMService.addToConversation(conversationId, inputText, 'user');
        localLLMService.addToConversation(conversationId, aiMessage.content, 'assistant');
      }

    } catch (error) {
      console.error('Failed to get AI response:', error);
      
      const errorMessage = {
        id: Date.now() + 1,
        role: 'assistant',
        content: "I apologize, but I'm having trouble connecting to the AI service. Please try again in a moment.",
        timestamp: new Date(),
        isError: true
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle voice input
   */
  const toggleVoiceInput = () => {
    if (isListening) {
      recognitionRef.current?.stop();
      setIsListening(false);
    } else {
      recognitionRef.current?.start();
      setIsListening(true);
    }
  };

  /**
   * Copy message content
   */
  const copyMessage = (content) => {
    navigator.clipboard.writeText(content);
    // Could add toast notification here
  };

  /**
   * Rate message
   */
  const rateMessage = (messageId, rating) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, rating } : msg
    ));
  };

  /**
   * Scroll to bottom
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  /**
   * Handle key press
   */
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  /**
   * Export conversation
   */
  const exportConversation = () => {
    const conversationText = messages.map(msg => 
      `${msg.role.toUpperCase()}: ${msg.content}`
    ).join('\n\n');
    
    const blob = new Blob([conversationText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-conversation-${new Date().toISOString().split('T')[0]}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className={`fixed ${isExpanded ? 'inset-4' : 'bottom-4 right-4 w-96 h-[600px]'} 
        ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} 
        border ${darkMode ? 'border-gray-700' : 'border-gray-200'} 
        rounded-xl shadow-2xl z-50 flex flex-col`}
    >
      {/* Header */}
      <div className={`p-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} flex items-center justify-between`}>
        <div className="flex items-center">
          <div className="w-10 h-10 bg-gradient-to-r from-[#88cc14] to-green-500 rounded-full flex items-center justify-center mr-3">
            <FaRobot className="text-white" />
          </div>
          <div>
            <h3 className="font-semibold">AI Cybersecurity Assistant</h3>
            <p className="text-xs text-gray-500">Powered by Local LLMs</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <FaCog className="text-gray-500" />
          </button>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            {isExpanded ? <FaCompress /> : <FaExpand />}
          </button>
          <button
            onClick={onToggle}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            ×
          </button>
        </div>
      </div>

      {/* Settings Panel */}
      <AnimatePresence>
        {showSettings && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: 'auto' }}
            exit={{ height: 0 }}
            className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} overflow-hidden`}
          >
            <div className="p-4">
              <div className="mb-3">
                <label className="block text-sm font-medium mb-2">AI Model</label>
                <select
                  value={selectedModel}
                  onChange={(e) => setSelectedModel(e.target.value)}
                  className={`w-full p-2 rounded-lg border ${
                    darkMode ? 'bg-[#252D4A] border-gray-600' : 'bg-white border-gray-300'
                  }`}
                >
                  <option value="cybersecurity">🛡️ Cybersecurity Expert</option>
                  <option value="reasoning">🧠 Reasoning Engine</option>
                  <option value="coding">💻 Secure Coding</option>
                  <option value="general">🤖 General Assistant</option>
                </select>
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={exportConversation}
                  className="flex items-center px-3 py-1 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600"
                >
                  <FaDownload className="mr-1" />
                  Export
                </button>
                <button
                  onClick={() => setMessages([])}
                  className="flex items-center px-3 py-1 text-sm bg-red-500 text-white rounded-lg hover:bg-red-600"
                >
                  Clear
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <MessageBubble
            key={message.id}
            message={message}
            onCopy={copyMessage}
            onRate={rateMessage}
            darkMode={darkMode}
          />
        ))}
        
        {isLoading && (
          <div className="flex items-center space-x-2 text-gray-500">
            <div className="animate-spin w-4 h-4 border-2 border-[#88cc14] border-t-transparent rounded-full"></div>
            <span>AI is thinking...</span>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className={`p-4 border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="flex items-end space-x-2">
          <div className="flex-1">
            <textarea
              ref={inputRef}
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about cybersecurity..."
              className={`w-full p-3 rounded-lg border resize-none ${
                darkMode 
                  ? 'bg-[#252D4A] border-gray-600 text-white' 
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              rows={2}
              disabled={isLoading}
            />
          </div>
          
          <div className="flex flex-col space-y-2">
            {recognitionRef.current && (
              <button
                onClick={toggleVoiceInput}
                className={`p-3 rounded-lg ${
                  isListening 
                    ? 'bg-red-500 text-white' 
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                }`}
              >
                {isListening ? <FaStop /> : <FaMicrophone />}
              </button>
            )}
            
            <button
              onClick={sendMessage}
              disabled={!inputText.trim() || isLoading}
              className="p-3 bg-[#88cc14] text-black rounded-lg hover:bg-[#7ab512] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FaPaperPlane />
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

/**
 * Message Bubble Component
 */
const MessageBubble = ({ message, onCopy, onRate, darkMode }) => {
  const isUser = message.role === 'user';
  
  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
      <div className={`max-w-[80%] ${isUser ? 'order-2' : 'order-1'}`}>
        <div
          className={`p-3 rounded-lg ${
            isUser
              ? 'bg-[#88cc14] text-black'
              : darkMode
              ? 'bg-[#252D4A] text-white'
              : 'bg-gray-100 text-gray-900'
          } ${message.isError ? 'bg-red-500 text-white' : ''}`}
        >
          <div className="whitespace-pre-wrap">{message.content}</div>
          
          {/* Reasoning steps for reasoning model */}
          {message.reasoning && (
            <div className="mt-3 pt-3 border-t border-gray-300 dark:border-gray-600">
              <div className="text-sm font-medium mb-2">Reasoning Steps:</div>
              {message.reasoning.map((step, index) => (
                <div key={index} className="text-sm mb-1">
                  {index + 1}. {step}
                </div>
              ))}
            </div>
          )}
          
          {/* Confidence and sources */}
          {(message.confidence || message.sources) && (
            <div className="mt-2 text-xs opacity-75">
              {message.confidence && <span>Confidence: {message.confidence}%</span>}
              {message.sources && <span className="ml-2">Sources: {message.sources.length}</span>}
            </div>
          )}
        </div>
        
        {/* Message actions */}
        {!isUser && (
          <div className="flex items-center space-x-2 mt-2">
            <button
              onClick={() => onCopy(message.content)}
              className="p-1 text-gray-400 hover:text-gray-600"
            >
              <FaCopy className="text-xs" />
            </button>
            <button
              onClick={() => onRate(message.id, 'up')}
              className={`p-1 ${message.rating === 'up' ? 'text-green-500' : 'text-gray-400 hover:text-green-500'}`}
            >
              <FaThumbsUp className="text-xs" />
            </button>
            <button
              onClick={() => onRate(message.id, 'down')}
              className={`p-1 ${message.rating === 'down' ? 'text-red-500' : 'text-gray-400 hover:text-red-500'}`}
            >
              <FaThumbsDown className="text-xs" />
            </button>
            <span className="text-xs text-gray-400">
              {message.timestamp.toLocaleTimeString()}
            </span>
          </div>
        )}
      </div>
      
      {/* Avatar */}
      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${isUser ? 'order-1 mr-2' : 'order-2 ml-2'}`}>
        {isUser ? (
          <FaUser className="text-gray-500" />
        ) : (
          <div className="w-8 h-8 bg-gradient-to-r from-[#88cc14] to-green-500 rounded-full flex items-center justify-center">
            <FaRobot className="text-white text-sm" />
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedAIChatbot;
