import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import Header from './ui/Header';

function Navbar() {
  const navigate = useNavigate();
  const [user, setUser] = useState(null);

  useEffect(() => {
    // Get initial auth state
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user || null);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user || null);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    navigate('/');
  };

  // Define navigation items based on user subscription level
  const getNavItems = () => {
    // Check if user has premium subscription
    const isPremium = user?.user_metadata?.subscription === 'premium' || user?.user_metadata?.subscription === 'business';

    // Base navigation items for all users
    const baseItems = [
      { to: "/challenges", icon: FaCode, text: "Challenges" },
      { to: "/leaderboard", icon: FaTrophy, text: "Leaderboard" },
      { to: "/products", icon: FaStore, text: "Store" }
    ];

    // Premium-only navigation items
    const premiumItems = [
      { to: "/learn", icon: FaGraduationCap, text: "Learn" },
      { to: "/games", icon: FaGamepad, text: "Start Hack" },
    ];

    // Return combined items if premium, otherwise just base items
    return isPremium ? [...premiumItems, ...baseItems] : baseItems;
  };

  const navItems = getNavItems();



  return (
    <nav
      className={`fixed w-full z-50 transition-all duration-300 ${
        isScrolled ? 'py-3 bg-black shadow-lg' : 'py-4 md:py-5 bg-black bg-opacity-90'
      }`}
    >
      <div className="container mx-auto px-5">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <Link to="/" className="flex items-center z-20 group">
            <div className="relative">
              <div className="absolute inset-0 bg-[#2DD4BF]/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <span className="font-bold text-2xl relative z-10">
                <span className="text-[#2DD4BF]">X</span>
                <span className="text-amber-500">Cerberus</span>
              </span>
            </div>
          </Link>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="md:hidden z-20 p-2 rounded-lg hover:bg-gray-800 transition-colors"
          >
            {isOpen ? (
              <FaTimes className="text-xl text-[#2DD4BF]" />
            ) : (
              <FaBars className="text-xl text-[#2DD4BF]" />
            )}
          </button>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.to}
                to={item.to}
                className="flex items-center space-x-2 nav-link relative group"
              >
                <item.icon className="text-xl text-[#2DD4BF] group-hover:text-white transition-colors" />
                <span className="text-[#2DD4BF] group-hover:text-white transition-colors">{item.text}</span>
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-[#2DD4BF] transition-all duration-300 group-hover:w-full"></span>
              </Link>
            ))}

            {user ? (
              <div className="relative dashboard-menu-container">
                <button
                  onClick={handleDashboardClick}
                  className="bg-[#2DD4BF] text-white font-bold px-6 py-2 rounded-lg hover:bg-[#2DD4BF]/80 transition-all duration-300 hover:shadow-lg hover:shadow-[#2DD4BF]/20 flex items-center gap-2 relative overflow-hidden group"
                >
                  <FaUser />
                  <span>Dashboard</span>
                </button>

                {showDashboardMenu && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                    <Link to="/dashboard" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Standard Dashboard
                    </Link>
                    <Link to="/enhanced-dashboard" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Enhanced Dashboard
                    </Link>
                    <Link to="/simplified-dashboard" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      XCerberus Dashboard
                    </Link>
                    <hr className="my-1" />
                    <button
                      onClick={async () => {
                        await supabase.auth.signOut();
                        navigate('/');
                      }}
                      className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                    >
                      Sign Out
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <Link
                to="/login"
                className="bg-[#2DD4BF] text-white font-bold px-6 py-2 rounded-lg hover:bg-[#2DD4BF]/80 transition-all duration-300 hover:shadow-lg hover:shadow-[#2DD4BF]/20 relative overflow-hidden group"
              >
                <span className="relative z-10">Sign In</span>
                <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-30 transition-opacity duration-500" style={{transform: 'translateX(-100%)'}}></span>
              </Link>
            )}
          </div>

          {/* Mobile Navigation */}
          <motion.div
            initial={false}
            animate={{
              opacity: isOpen ? 1 : 0,
              pointerEvents: isOpen ? 'auto' : 'none'
            }}
            className="fixed inset-0 bg-black/90 md:hidden backdrop-blur-sm"
            onClick={() => setIsOpen(false)}
          />

          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: isOpen ? 0 : '100%' }}
            transition={{ type: 'tween' }}
            className="fixed top-0 right-0 bottom-0 w-3/4 bg-black shadow-xl md:hidden z-10 p-6"
          >
            <div className="flex flex-col h-full">
              <div className="flex-1 space-y-4 mt-16">
                {navItems.map((item) => (
                  <Link
                    key={item.to}
                    to={item.to}
                    onClick={() => setIsOpen(false)}
                    className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-800 transition-colors text-[#2DD4BF] group"
                  >
                    <item.icon className="text-[#2DD4BF] text-xl group-hover:scale-110 transition-transform" />
                    <span className="font-medium group-hover:translate-x-1 transition-transform">{item.text}</span>
                  </Link>
                ))}
              </div>

              {user ? (
                <button
                  onClick={handleDashboardClick}
                  className="w-full bg-[#2DD4BF] text-white font-bold px-6 py-3 rounded-lg hover:bg-[#2DD4BF]/80 transition-all flex items-center justify-center gap-2"
                >
                  <FaUser />
                  <span>Dashboard</span>
                </button>
              ) : (
                <Link
                  to="/login"
                  onClick={() => setIsOpen(false)}
                  className="w-full bg-[#2DD4BF] text-white font-bold px-6 py-3 rounded-lg hover:bg-[#2DD4BF]/80 transition-all text-center"
                >
                  Sign In
                </Link>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </nav>
  );
}

export default Navbar;