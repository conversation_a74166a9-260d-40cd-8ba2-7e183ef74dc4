import React from 'react';
import { Link } from 'react-router-dom';
import { FaTwitter, FaGithub, FaLinkedin, FaDiscord } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

const GlobalFooter = () => {
  const { darkMode } = useGlobalTheme();
  
  const footerLinks = [
    {
      title: 'Platform',
      links: [
        { name: 'Challenges', path: '/challenges' },
        { name: 'Learn', path: '/learn/modules' },
        { name: 'Leaderboard', path: '/leaderboard' },
        { name: 'Store', path: '/store' },
      ]
    },
    {
      title: 'Company',
      links: [
        { name: 'About Us', path: '/about' },
        { name: 'Pricing', path: '/pricing' },
        { name: 'Contact', path: '/contact' },
        { name: 'Careers', path: '/careers' },
      ]
    },
    {
      title: 'Resources',
      links: [
        { name: 'Blog', path: '/blog' },
        { name: 'Documentation', path: '/docs' },
        { name: 'FAQ', path: '/faq' },
        { name: 'Support', path: '/support' },
      ]
    }
  ];
  
  const socialLinks = [
    { icon: <FaTwitter />, url: 'https://twitter.com' },
    { icon: <FaGithub />, url: 'https://github.com' },
    { icon: <FaLinkedin />, url: 'https://linkedin.com' },
    { icon: <FaDiscord />, url: 'https://discord.com' },
  ];
  
  return (
    <footer className={`${darkMode ? 'bg-[#0B1120] border-gray-800' : 'bg-gray-50 border-gray-200'} border-t mt-16`}>
      <div className="container mx-auto px-4 py-12">
        {/* Main Footer */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-8">
          {/* Logo and Description */}
          <div className="lg:col-span-2">
            <Link to="/" className="flex items-center mb-4">
              <span className="text-[#88cc14] font-bold text-2xl mr-1">X</span>
              <span className={`font-bold text-2xl ${darkMode ? 'text-white' : 'text-gray-900'}`}>Cerberus</span>
            </Link>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 max-w-md`}>
              XCerberus is a cutting-edge cybersecurity learning platform designed to help you master the skills needed to protect digital assets in today's threat landscape.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social, index) => (
                <a 
                  key={index}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'} transition-colors`}
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>
          
          {/* Footer Links */}
          {footerLinks.map((section, index) => (
            <div key={index}>
              <h3 className={`font-bold text-lg mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>{section.title}</h3>
              <ul className="space-y-3">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link 
                      to={link.path}
                      className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'} transition-colors`}
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
        
        {/* Bottom Footer */}
        <div className={`pt-8 ${darkMode ? 'border-gray-800' : 'border-gray-200'} border-t flex flex-col md:flex-row justify-between items-center`}>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm mb-4 md:mb-0`}>
            &copy; {new Date().getFullYear()} XCerberus. All rights reserved.
          </p>
          <div className="flex space-x-6">
            <Link to="/privacy" className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'} text-sm transition-colors`}>
              Privacy Policy
            </Link>
            <Link to="/terms" className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'} text-sm transition-colors`}>
              Terms of Service
            </Link>
            <Link to="/cookies" className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'} text-sm transition-colors`}>
              Cookie Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default GlobalFooter;
