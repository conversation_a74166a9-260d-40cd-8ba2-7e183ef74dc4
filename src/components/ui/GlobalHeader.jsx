import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FaBars, FaTimes, FaSun, FaMoon, FaUser, FaStore, FaTrophy, FaGraduationCap, FaShieldAlt } from 'react-icons/fa';
import XCerberusLogo from '../icons/XCerberusLogo';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

const GlobalHeader = () => {
  const { darkMode, toggleDarkMode } = useGlobalTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  // Track scroll position to add background on scroll
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsOpen(false);
  }, [location.pathname]);

  const navLinks = [
    { name: 'Insights', path: '/security-insights', icon: <FaShieldAlt /> },
    { name: 'Challenges', path: '/challenges', icon: <FaTrophy /> },
    { name: 'Learn', path: '/learn', icon: <FaGraduationCap /> },
    { name: 'Leaderboard', path: '/leaderboard', icon: <FaTrophy /> },
    { name: 'Store', path: '/store', icon: <FaStore /> },
    { name: 'Pricing', path: '/pricing', icon: <FaStore /> },
  ];

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled || isOpen
          ? darkMode
            ? 'bg-[#0B1120]/95 backdrop-blur-sm shadow-lg'
            : 'bg-white/95 backdrop-blur-sm shadow-lg'
          : darkMode
            ? 'bg-[#0B1120]'
            : 'bg-white'
      } ${darkMode ? 'border-gray-800' : 'border-gray-200'} border-b`}
    >
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <Link to="/" className="flex items-center z-50">
            <XCerberusLogo darkMode={darkMode} />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navLinks.map((link) => (
              <Link
                key={link.path}
                to={link.path}
                className={`${darkMode ? 'text-gray-300 hover:text-[#88cc14]' : 'text-gray-700 hover:text-[#88cc14]'} transition-colors ${
                  location.pathname === link.path ? 'text-[#88cc14]' : ''
                }`}
              >
                {link.name}
              </Link>
            ))}
          </nav>

          {/* Auth Buttons and Theme Toggle */}
          <div className="hidden md:flex items-center space-x-4">
            <button
              onClick={toggleDarkMode}
              className={`p-2 rounded-full ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-200'} transition-colors`}
              aria-label={darkMode ? 'Switch to light mode' : 'Switch to dark mode'}
            >
              {darkMode ? (
                <FaSun className="text-yellow-400" />
              ) : (
                <FaMoon className="text-gray-700" />
              )}
            </button>

            <Link
              to="/login"
              className={`${darkMode ? 'text-gray-300 hover:text-white' : 'text-gray-700 hover:text-black'} transition-colors`}
            >
              Log In
            </Link>
            <Link
              to="/signup"
              className="bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium px-4 py-2 rounded-lg transition-colors"
            >
              Sign Up
            </Link>
          </div>

          {/* Mobile Menu Button and Theme Toggle */}
          <div className="flex items-center space-x-3 md:hidden">
            <button
              onClick={toggleDarkMode}
              className={`p-2 rounded-full ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-200'} transition-colors`}
              aria-label={darkMode ? 'Switch to light mode' : 'Switch to dark mode'}
            >
              {darkMode ? (
                <FaSun className="text-yellow-400" />
              ) : (
                <FaMoon className="text-gray-700" />
              )}
            </button>

            <button
              className={`${darkMode ? 'text-gray-300 hover:text-white' : 'text-gray-700 hover:text-black'} p-2`}
              onClick={() => setIsOpen(!isOpen)}
              aria-label="Toggle menu"
            >
              {isOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <div
        className={`fixed inset-0 ${darkMode ? 'bg-[#0B1120]' : 'bg-white'} z-40 transition-transform duration-300 transform ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        } md:hidden`}
        style={{ top: '72px' }}
      >
        <div className="container mx-auto px-4 py-6">
          <nav className="flex flex-col space-y-6">
            {navLinks.map((link) => (
              <Link
                key={link.path}
                to={link.path}
                className={`text-xl flex items-center gap-3 ${
                  darkMode
                    ? 'text-gray-300 hover:text-[#88cc14]'
                    : 'text-gray-700 hover:text-[#88cc14]'
                } transition-colors ${
                  location.pathname === link.path ? 'text-[#88cc14]' : ''
                }`}
                onClick={() => setIsOpen(false)}
              >
                {link.icon}
                {link.name}
              </Link>
            ))}

            <div className={`pt-6 ${darkMode ? 'border-gray-800' : 'border-gray-200'} border-t flex flex-col space-y-4`}>
              <Link
                to="/login"
                className={`${
                  darkMode
                    ? 'bg-transparent hover:bg-gray-800 text-white border-gray-700'
                    : 'bg-transparent hover:bg-gray-100 text-gray-900 border-gray-300'
                } font-medium px-4 py-2 rounded-lg transition-colors text-center border`}
                onClick={() => setIsOpen(false)}
              >
                Log In
              </Link>
              <Link
                to="/signup"
                className="bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium px-4 py-2 rounded-lg transition-colors text-center"
                onClick={() => setIsOpen(false)}
              >
                Sign Up
              </Link>
            </div>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default GlobalHeader;
