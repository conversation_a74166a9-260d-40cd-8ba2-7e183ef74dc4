import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  FaHome, FaUser, FaChartLine, FaCode, FaBook, FaTrophy, FaShieldAlt,
  FaUsers, FaGamepad, FaGraduationCap, FaStore, FaNewspaper, FaEye,
  FaLaptopCode, FaUserTie, FaUserSecret, FaCrown, FaGem, FaLock,
  FaCheckCircle, FaClock, FaExclamationTriangle, FaArrowRight
} from 'react-icons/fa';
import { useAuth } from '../../contexts/AuthContext';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

/**
 * DashboardInventory Component
 * 
 * Provides a comprehensive overview of all dashboards and modules in the application
 * with detailed information about features, implementation status, and access levels.
 */
const DashboardInventory = () => {
  const navigate = useNavigate();
  const { user, profile } = useAuth();
  const { subscriptionLevel } = useSubscription();
  const { darkMode } = useGlobalTheme();
  
  const [expandedDashboard, setExpandedDashboard] = useState(null);

  // Complete dashboard inventory
  const dashboardInventory = [
    {
      category: 'User Dashboards',
      description: 'Primary user interfaces for different user types and subscription levels',
      dashboards: [
        {
          id: 'dashboard',
          name: 'Main Dashboard (Unified)',
          path: '/dashboard',
          icon: FaHome,
          description: 'The primary dashboard that adapts based on subscription level',
          implementation: 'complete',
          accessLevel: 'free',
          features: [
            'Learning Analytics Widget',
            'Challenge Analytics Widget', 
            'Recommendations Widget',
            'Real-time Data Updates',
            'Subscription-based Feature Access',
            'Progress Tracking',
            'Skills Radar Chart (Premium+)',
            'Team Analytics (Business)'
          ],
          dataSource: 'Supabase (profiles, user_progress, challenges)',
          interactiveFeatures: [
            'Time range selection',
            'Widget expansion/collapse',
            'Upgrade prompts for premium features',
            'Real-time subscription updates'
          ],
          currentContent: 'Live data from database with fallback demo content'
        },
        {
          id: 'enhanced-dashboard',
          name: 'Enhanced Dashboard',
          path: '/enhanced-dashboard',
          icon: FaChartLine,
          description: 'Advanced dashboard with learning paths and AI assistant',
          implementation: 'complete',
          accessLevel: 'premium',
          features: [
            'Learning Paths Navigation',
            'Module View with Progress',
            'Progress Tracker',
            'AI Learning Assistant',
            'Advanced Analytics',
            'Personalized Recommendations'
          ],
          dataSource: 'Supabase + AI service integration',
          interactiveFeatures: [
            'AI chat assistant',
            'Learning path navigation',
            'Module completion tracking',
            'Progress visualization'
          ],
          currentContent: 'Mock learning paths with interactive AI assistant'
        },
        {
          id: 'simplified-dashboard',
          name: 'Simplified Dashboard',
          path: '/simplified-dashboard',
          icon: FaUser,
          description: 'Clean, minimal dashboard for basic users',
          implementation: 'complete',
          accessLevel: 'free',
          features: [
            'XCerberus Coins Display',
            'Quick Action Buttons',
            'Recent Activity Feed',
            'Subscription Status',
            'Basic Navigation',
            'Premium Feature Previews'
          ],
          dataSource: 'Static data with user context',
          interactiveFeatures: [
            'Navigation to other modules',
            'Premium feature previews',
            'Activity timeline'
          ],
          currentContent: 'Static demo data with user-specific information'
        }
      ]
    },
    {
      category: 'Learning & Education',
      description: 'Educational content and learning management systems',
      dashboards: [
        {
          id: 'learn',
          name: 'Learning Hub (Static)',
          path: '/learn',
          icon: FaBook,
          description: 'Static learning content and module previews',
          implementation: 'complete',
          accessLevel: 'free',
          features: [
            'Learning Module Catalog',
            'Module Previews',
            'Category Filtering',
            'Difficulty Levels',
            'Progress Indicators',
            'Free Content Access'
          ],
          dataSource: 'Static JSON data',
          interactiveFeatures: [
            'Module preview modal',
            'Category filtering',
            'Search functionality',
            'Progress tracking'
          ],
          currentContent: 'Comprehensive cybersecurity learning modules'
        },
        {
          id: 'learn-modules',
          name: 'Interactive Learning',
          path: '/learn/modules',
          icon: FaGraduationCap,
          description: 'Interactive learning modules with hands-on exercises',
          implementation: 'complete',
          accessLevel: 'premium',
          features: [
            'Interactive Modules',
            'Hands-on Labs',
            'Progress Tracking',
            'Completion Certificates',
            'Skill Assessments',
            'Personalized Learning Paths'
          ],
          dataSource: 'Supabase + learning content API',
          interactiveFeatures: [
            'Interactive exercises',
            'Code editors',
            'Virtual labs',
            'Progress saving'
          ],
          currentContent: 'Premium learning content with interactive elements'
        },
        {
          id: 'security-insights',
          name: 'Security Insights',
          path: '/security-insights',
          icon: FaShieldAlt,
          description: 'Real-time threat intelligence and security education',
          implementation: 'complete',
          accessLevel: 'free',
          features: [
            'Threat Intelligence Feed',
            'Security News',
            'Educational Articles',
            'Vulnerability Database',
            'Security Tools Matrix',
            'Basic Analytics'
          ],
          dataSource: 'External threat intelligence APIs',
          interactiveFeatures: [
            'Live threat feed',
            'Article reading',
            'Tool exploration',
            'Basic search'
          ],
          currentContent: 'Live threat intelligence data and educational content'
        },
        {
          id: 'enhanced-security-insights',
          name: 'Enhanced Security Hub',
          path: '/enhanced-security-insights',
          icon: FaEye,
          description: 'Advanced threat intelligence with interactive features',
          implementation: 'complete',
          accessLevel: 'premium',
          features: [
            'Advanced Threat Analytics',
            'AI Threat Assistant',
            'Threat Hunting Academy',
            'Live Threat Feed',
            'Correlation Analysis',
            'Custom Dashboards'
          ],
          dataSource: 'Multiple threat intelligence APIs + AI services',
          interactiveFeatures: [
            'AI-powered threat analysis',
            'Interactive threat hunting',
            'Custom dashboard creation',
            'Advanced filtering'
          ],
          currentContent: 'Advanced threat intelligence with AI-powered insights'
        }
      ]
    },
    {
      category: 'Challenges & Games',
      description: 'Interactive challenges and gamified learning experiences',
      dashboards: [
        {
          id: 'challenges',
          name: 'Challenges Hub',
          path: '/challenges',
          icon: FaCode,
          description: 'Cybersecurity challenges across different difficulty levels',
          implementation: 'complete',
          accessLevel: 'free',
          features: [
            'Challenge Categories',
            'Difficulty Levels',
            'Points System',
            'Leaderboard Integration',
            'Progress Tracking',
            'Solution Explanations'
          ],
          dataSource: 'Supabase (challenges, user_progress)',
          interactiveFeatures: [
            'Challenge simulation',
            'Code submission',
            'Hint system',
            'Progress tracking'
          ],
          currentContent: 'Database-driven challenges with real-time progress'
        },
        {
          id: 'games',
          name: 'Start Hack (Games)',
          path: '/games',
          icon: FaGamepad,
          description: 'Interactive hacking simulations and games',
          implementation: 'complete',
          accessLevel: 'premium',
          features: [
            'Hacking Simulations',
            'Interactive Games',
            'Skill Building Exercises',
            'Virtual Environments',
            'Achievement System',
            'Multiplayer Challenges'
          ],
          dataSource: 'Game engine + Supabase',
          interactiveFeatures: [
            'Real-time simulations',
            'Interactive environments',
            'Multiplayer features',
            'Achievement unlocking'
          ],
          currentContent: 'Interactive hacking simulations and educational games'
        }
      ]
    }
  ];

  // Check access level
  const hasAccess = (accessLevel) => {
    // 🔧 DEVELOPMENT MODE: Temporarily disable all access restrictions
    const isDevelopmentMode = true;

    if (isDevelopmentMode) {
      console.log(`🔧 Dev Mode: Granting access to ${accessLevel} level`);
      return true;
    }

    // Production access control (will be enabled later)
    if (accessLevel === 'free') return true;
    if (accessLevel === 'premium') return ['Premium', 'Business'].includes(subscriptionLevel);
    if (accessLevel === 'business') return subscriptionLevel === 'Business';
    if (accessLevel === 'admin') return profile?.role === 'admin' || profile?.role === 'super_admin';
    if (accessLevel === 'super-admin') return profile?.role === 'super_admin';
    return false;
  };

  // Get status color
  const getStatusColor = (implementation) => {
    switch (implementation) {
      case 'complete': return 'text-green-500';
      case 'partial': return 'text-yellow-500';
      case 'planned': return 'text-gray-500';
      default: return 'text-gray-500';
    }
  };

  // Get access badge
  const getAccessBadge = (accessLevel) => {
    switch (accessLevel) {
      case 'free': return { text: 'Free', color: 'bg-green-500', icon: null };
      case 'premium': return { text: 'Premium', color: 'bg-blue-500', icon: FaCrown };
      case 'business': return { text: 'Business', color: 'bg-purple-500', icon: FaGem };
      case 'admin': return { text: 'Admin', color: 'bg-orange-500', icon: FaUserTie };
      case 'super-admin': return { text: 'Super Admin', color: 'bg-red-500', icon: FaUserSecret };
      default: return { text: 'Unknown', color: 'bg-gray-500', icon: null };
    }
  };

  return (
    <div className={`min-h-screen p-6 ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Dashboard Inventory</h1>
          <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Comprehensive overview of all dashboards and modules in the XCerberus platform
          </p>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
            <div className="text-2xl font-bold text-[#88cc14]">
              {dashboardInventory.reduce((total, category) => total + category.dashboards.length, 0)}
            </div>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Dashboards</div>
          </div>
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
            <div className="text-2xl font-bold text-green-500">
              {dashboardInventory.reduce((total, category) => 
                total + category.dashboards.filter(d => d.implementation === 'complete').length, 0
              )}
            </div>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Complete</div>
          </div>
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
            <div className="text-2xl font-bold text-blue-500">
              {dashboardInventory.reduce((total, category) => 
                total + category.dashboards.filter(d => hasAccess(d.accessLevel)).length, 0
              )}
            </div>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Accessible</div>
          </div>
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
            <div className="text-2xl font-bold text-purple-500">{dashboardInventory.length}</div>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Categories</div>
          </div>
        </div>

        {/* Dashboard Categories */}
        <div className="space-y-8">
          {dashboardInventory.map((category, categoryIndex) => (
            <div key={categoryIndex} className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <div className="mb-6">
                <h2 className="text-xl font-bold mb-2">{category.category}</h2>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{category.description}</p>
              </div>

              <div className="space-y-4">
                {category.dashboards.map((dashboard, dashboardIndex) => {
                  const IconComponent = dashboard.icon;
                  const accessBadge = getAccessBadge(dashboard.accessLevel);
                  const userHasAccess = hasAccess(dashboard.accessLevel);
                  const isExpanded = expandedDashboard === `${categoryIndex}-${dashboardIndex}`;

                  return (
                    <motion.div
                      key={dashboardIndex}
                      className={`border rounded-lg overflow-hidden ${darkMode ? 'border-gray-600 bg-[#252D4A]' : 'border-gray-300 bg-gray-50'}`}
                    >
                      {/* Dashboard Header */}
                      <div
                        className={`p-4 cursor-pointer ${darkMode ? 'hover:bg-[#2A3441]' : 'hover:bg-gray-100'}`}
                        onClick={() => setExpandedDashboard(isExpanded ? null : `${categoryIndex}-${dashboardIndex}`)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className={`p-3 rounded-lg ${userHasAccess ? 'bg-[#88cc14]/20 text-[#88cc14]' : 'bg-gray-500/20 text-gray-500'}`}>
                              <IconComponent className="text-xl" />
                            </div>
                            <div>
                              <div className="flex items-center gap-3 mb-1">
                                <h3 className="text-lg font-semibold">{dashboard.name}</h3>
                                <span className={`px-2 py-1 rounded-full text-xs text-white ${accessBadge.color}`}>
                                  {accessBadge.icon && <accessBadge.icon className="inline mr-1" />}
                                  {accessBadge.text}
                                </span>
                                <span className={`flex items-center text-sm ${getStatusColor(dashboard.implementation)}`}>
                                  {dashboard.implementation === 'complete' && <FaCheckCircle className="mr-1" />}
                                  {dashboard.implementation === 'partial' && <FaClock className="mr-1" />}
                                  {dashboard.implementation === 'planned' && <FaExclamationTriangle className="mr-1" />}
                                  {dashboard.implementation}
                                </span>
                              </div>
                              <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{dashboard.description}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            {!userHasAccess && <FaLock className="text-gray-400" />}
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                if (userHasAccess) navigate(dashboard.path);
                              }}
                              disabled={!userHasAccess}
                              className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
                                userHasAccess
                                  ? 'bg-[#88cc14] text-black hover:bg-[#7ab512]'
                                  : 'bg-gray-500 text-gray-300 cursor-not-allowed'
                              }`}
                            >
                              {userHasAccess ? 'Open' : 'Locked'}
                              <FaArrowRight />
                            </button>
                          </div>
                        </div>
                      </div>

                      {/* Expanded Details */}
                      {isExpanded && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          className={`border-t ${darkMode ? 'border-gray-600 bg-[#1E2532]' : 'border-gray-300 bg-white'}`}
                        >
                          <div className="p-6">
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                              {/* Features */}
                              <div>
                                <h4 className="font-semibold mb-3">Key Features</h4>
                                <div className="space-y-2">
                                  {dashboard.features.map((feature, featureIndex) => (
                                    <div key={featureIndex} className="flex items-center gap-2">
                                      <FaCheckCircle className="text-green-500 text-sm" />
                                      <span className="text-sm">{feature}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>

                              {/* Technical Details */}
                              <div>
                                <h4 className="font-semibold mb-3">Technical Details</h4>
                                <div className="space-y-3">
                                  <div>
                                    <span className="text-sm font-medium">Data Source:</span>
                                    <p className="text-sm text-gray-500">{dashboard.dataSource}</p>
                                  </div>
                                  <div>
                                    <span className="text-sm font-medium">Current Content:</span>
                                    <p className="text-sm text-gray-500">{dashboard.currentContent}</p>
                                  </div>
                                </div>
                              </div>

                              {/* Interactive Features */}
                              <div className="lg:col-span-2">
                                <h4 className="font-semibold mb-3">Interactive Features</h4>
                                <div className="flex flex-wrap gap-2">
                                  {dashboard.interactiveFeatures.map((feature, featureIndex) => (
                                    <span
                                      key={featureIndex}
                                      className={`px-3 py-1 rounded-full text-sm ${
                                        darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'
                                      }`}
                                    >
                                      {feature}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </motion.div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className={`mt-8 p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            This inventory shows all available dashboards and modules in the XCerberus platform.
            Access levels are determined by your subscription tier and user role.
            Click on any dashboard to expand details or use the "Open" button to navigate directly.
          </p>
        </div>
      </div>
    </div>
  );
};

export default DashboardInventory;
