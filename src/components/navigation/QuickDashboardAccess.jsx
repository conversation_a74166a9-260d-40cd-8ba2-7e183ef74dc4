import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaHome, FaUser, FaChartLine, FaCode, FaBook, FaTrophy, FaShieldAlt,
  FaUsers, FaGamepad, FaGraduationCap, FaStore, FaNewspaper, FaEye,
  FaLaptopCode, FaUserTie, FaUserSecret, FaArrowRight, FaGrid3x3,
  FaTimes, FaRocket
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

/**
 * QuickDashboardAccess Component
 * 
 * Provides quick access to all dashboards with a grid-based interface.
 * Shows current dashboard and allows one-click navigation to any other dashboard.
 */
const QuickDashboardAccess = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { darkMode } = useGlobalTheme();
  const [isOpen, setIsOpen] = useState(false);

  // Quick access dashboard list (all restrictions removed for development)
  const quickAccessDashboards = [
    {
      id: 'simplified-dashboard',
      name: 'Simple',
      path: '/simplified-dashboard',
      icon: FaUser,
      color: 'bg-green-500',
      description: 'Basic dashboard'
    },
    {
      id: 'dashboard',
      name: 'Main',
      path: '/dashboard',
      icon: FaHome,
      color: 'bg-blue-500',
      description: 'Unified dashboard'
    },
    {
      id: 'enhanced-dashboard',
      name: 'Enhanced',
      path: '/enhanced-dashboard',
      icon: FaChartLine,
      color: 'bg-purple-500',
      description: 'Advanced features'
    },
    {
      id: 'learn',
      name: 'Learning',
      path: '/learn',
      icon: FaBook,
      color: 'bg-indigo-500',
      description: 'Learning hub'
    },
    {
      id: 'learn-modules',
      name: 'Interactive',
      path: '/learn/modules',
      icon: FaGraduationCap,
      color: 'bg-teal-500',
      description: 'Interactive learning'
    },
    {
      id: 'challenges',
      name: 'Challenges',
      path: '/challenges',
      icon: FaCode,
      color: 'bg-orange-500',
      description: 'Security challenges'
    },
    {
      id: 'games',
      name: 'Games',
      path: '/games',
      icon: FaGamepad,
      color: 'bg-pink-500',
      description: 'Hacking games'
    },
    {
      id: 'security-insights',
      name: 'Security',
      path: '/security-insights',
      icon: FaShieldAlt,
      color: 'bg-red-500',
      description: 'Threat intelligence'
    },
    {
      id: 'enhanced-security-insights',
      name: 'Adv Security',
      path: '/enhanced-security-insights',
      icon: FaEye,
      color: 'bg-yellow-500',
      description: 'Advanced threats'
    },
    {
      id: 'leaderboard',
      name: 'Leaderboard',
      path: '/leaderboard',
      icon: FaTrophy,
      color: 'bg-amber-500',
      description: 'Rankings'
    },
    {
      id: 'teams',
      name: 'Teams',
      path: '/teams',
      icon: FaUsers,
      color: 'bg-cyan-500',
      description: 'Team collaboration'
    },
    {
      id: 'admin',
      name: 'Admin',
      path: '/admin',
      icon: FaUserTie,
      color: 'bg-gray-500',
      description: 'Basic admin'
    },
    {
      id: 'admin-modern',
      name: 'Modern Admin',
      path: '/admin-modern',
      icon: FaLaptopCode,
      color: 'bg-slate-500',
      description: 'Modern admin'
    },
    {
      id: 'super-admin',
      name: 'Super Admin',
      path: '/super-admin',
      icon: FaUserSecret,
      color: 'bg-black',
      description: 'Platform control'
    },
    {
      id: 'dashboard-inventory',
      name: 'Inventory',
      path: '/dashboard-inventory',
      icon: FaGrid3x3,
      color: 'bg-emerald-500',
      description: 'Dashboard overview'
    }
  ];

  // Get current dashboard
  const getCurrentDashboard = () => {
    return quickAccessDashboards.find(dashboard => 
      location.pathname === dashboard.path || 
      (dashboard.path !== '/' && location.pathname.startsWith(dashboard.path))
    );
  };

  const currentDashboard = getCurrentDashboard();

  // Handle navigation
  const handleNavigate = (path) => {
    navigate(path);
    setIsOpen(false);
  };

  return (
    <>
      {/* Quick Access Button */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className={`fixed bottom-6 left-6 z-40 w-14 h-14 rounded-full ${
          darkMode ? 'bg-[#88cc14] text-black' : 'bg-[#88cc14] text-black'
        } flex items-center justify-center shadow-lg hover:scale-110 transition-transform`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        title="Quick Dashboard Access"
      >
        <FaGrid3x3 className="text-lg" />
      </motion.button>

      {/* Current Dashboard Indicator */}
      {currentDashboard && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={`fixed bottom-24 left-6 z-30 px-3 py-2 rounded-lg ${
            darkMode ? 'bg-[#1A1F35] text-white border border-gray-700' : 'bg-white text-gray-900 border border-gray-200'
          } shadow-lg`}
        >
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${currentDashboard.color}`}></div>
            <span className="text-sm font-medium">{currentDashboard.name}</span>
          </div>
        </motion.div>
      )}

      {/* Quick Access Modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
            style={{ backgroundColor: 'rgba(0, 0, 0, 0.8)' }}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className={`w-full max-w-4xl max-h-[80vh] rounded-lg overflow-hidden ${
                darkMode ? 'bg-[#1A1F35] text-white' : 'bg-white text-gray-900'
              }`}
            >
              {/* Header */}
              <div className={`p-6 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold flex items-center">
                      <FaRocket className="mr-3 text-[#88cc14]" />
                      Quick Dashboard Access
                    </h2>
                    <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Navigate to any dashboard instantly
                    </p>
                  </div>
                  <button
                    onClick={() => setIsOpen(false)}
                    className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
                  >
                    <FaTimes />
                  </button>
                </div>
              </div>

              {/* Dashboard Grid */}
              <div className="p-6 overflow-y-auto max-h-[60vh]">
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {quickAccessDashboards.map((dashboard) => {
                    const IconComponent = dashboard.icon;
                    const isCurrentDashboard = currentDashboard?.id === dashboard.id;

                    return (
                      <motion.button
                        key={dashboard.id}
                        onClick={() => handleNavigate(dashboard.path)}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className={`p-4 rounded-lg border-2 transition-all ${
                          isCurrentDashboard
                            ? 'border-[#88cc14] bg-[#88cc14]/10'
                            : darkMode
                              ? 'border-gray-600 bg-[#252D4A] hover:border-gray-500'
                              : 'border-gray-200 bg-gray-50 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex flex-col items-center text-center">
                          <div className={`w-12 h-12 rounded-lg ${dashboard.color} flex items-center justify-center mb-3`}>
                            <IconComponent className="text-white text-xl" />
                          </div>
                          <h3 className="font-semibold text-sm mb-1">{dashboard.name}</h3>
                          <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            {dashboard.description}
                          </p>
                          {isCurrentDashboard && (
                            <div className="mt-2 px-2 py-1 bg-[#88cc14] text-black rounded text-xs font-medium">
                              Current
                            </div>
                          )}
                        </div>
                      </motion.button>
                    );
                  })}
                </div>

                {/* Development Notice */}
                <div className={`mt-6 p-4 rounded-lg ${darkMode ? 'bg-blue-900/20 border border-blue-700' : 'bg-blue-50 border border-blue-200'}`}>
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm font-medium text-blue-600">Development Mode</span>
                  </div>
                  <p className="text-sm text-blue-600">
                    All access restrictions are temporarily disabled. You can access any dashboard for testing and development purposes.
                  </p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default QuickDashboardAccess;
