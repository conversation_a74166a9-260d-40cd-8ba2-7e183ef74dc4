import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaHome, FaUser, FaChartLine, FaCode, FaBook, FaTrophy, FaShieldAlt,
  FaUsers, FaCog, FaGamepad, FaGraduationCap, FaStore, FaNewspaper,
  FaRocket, FaEye, FaLaptopCode, FaUserTie, FaUserSecret, FaSearch,
  FaTimes, FaBars, FaArrowRight, FaLock, FaCrown, FaGem
} from 'react-icons/fa';
import { useAuth } from '../../contexts/AuthContext';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

/**
 * DashboardNavigator Component
 * 
 * A comprehensive navigation system that provides access to all dashboards and modules
 * in the application. Includes categorization, search, and access control.
 */
const DashboardNavigator = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, profile } = useAuth();
  const { subscriptionLevel } = useSubscription();
  const { darkMode } = useGlobalTheme();
  
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');

  // Dashboard inventory with comprehensive information
  const dashboards = [
    // User Dashboards
    {
      id: 'dashboard',
      name: 'Main Dashboard',
      path: '/dashboard',
      icon: FaHome,
      category: 'user',
      description: 'Unified dashboard with analytics, progress tracking, and recommendations',
      features: ['Learning Analytics', 'Challenge Progress', 'Recommendations', 'Real-time Updates'],
      status: 'active',
      accessLevel: 'free',
      implementation: 'complete'
    },
    {
      id: 'enhanced-dashboard',
      name: 'Enhanced Dashboard',
      path: '/enhanced-dashboard',
      icon: FaChartLine,
      category: 'user',
      description: 'Advanced dashboard with learning paths and AI assistant',
      features: ['Learning Paths', 'Module View', 'Progress Tracker', 'AI Assistant'],
      status: 'active',
      accessLevel: 'premium',
      implementation: 'complete'
    },
    {
      id: 'simplified-dashboard',
      name: 'Simplified Dashboard',
      path: '/simplified-dashboard',
      icon: FaUser,
      category: 'user',
      description: 'Clean, minimal dashboard focused on core features',
      features: ['XCerberus Coins', 'Quick Actions', 'Recent Activity', 'Subscription Status'],
      status: 'active',
      accessLevel: 'free',
      implementation: 'complete'
    },

    // Learning & Education
    {
      id: 'learn',
      name: 'Learning Hub',
      path: '/learn',
      icon: FaBook,
      category: 'learning',
      description: 'Comprehensive learning modules and educational content',
      features: ['Static Learning Content', 'Module Previews', 'Progress Tracking'],
      status: 'active',
      accessLevel: 'free',
      implementation: 'complete'
    },
    {
      id: 'learn-modules',
      name: 'Interactive Learning',
      path: '/learn/modules',
      icon: FaGraduationCap,
      category: 'learning',
      description: 'Interactive learning modules with hands-on exercises',
      features: ['Interactive Modules', 'Hands-on Labs', 'Progress Tracking'],
      status: 'active',
      accessLevel: 'premium',
      implementation: 'complete'
    },
    {
      id: 'security-insights',
      name: 'Security Insights',
      path: '/security-insights',
      icon: FaShieldAlt,
      category: 'learning',
      description: 'Real-time threat intelligence and security education',
      features: ['Threat Intelligence', 'Security News', 'Educational Content'],
      status: 'active',
      accessLevel: 'free',
      implementation: 'complete'
    },
    {
      id: 'enhanced-security-insights',
      name: 'Enhanced Security Hub',
      path: '/enhanced-security-insights',
      icon: FaEye,
      category: 'learning',
      description: 'Advanced threat intelligence with interactive features',
      features: ['Threat Analytics', 'AI Assistant', 'Hunting Academy', 'Live Feed'],
      status: 'active',
      accessLevel: 'premium',
      implementation: 'complete'
    },

    // Challenges & Games
    {
      id: 'challenges',
      name: 'Challenges',
      path: '/challenges',
      icon: FaCode,
      category: 'challenges',
      description: 'Cybersecurity challenges across different difficulty levels',
      features: ['Multiple Difficulty Levels', 'Categories', 'Points System', 'Leaderboard'],
      status: 'active',
      accessLevel: 'free',
      implementation: 'complete'
    },
    {
      id: 'games',
      name: 'Start Hack',
      path: '/games',
      icon: FaGamepad,
      category: 'challenges',
      description: 'Interactive hacking simulations and games',
      features: ['Hacking Simulations', 'Interactive Games', 'Skill Building'],
      status: 'active',
      accessLevel: 'premium',
      implementation: 'complete'
    },

    // Community & Competition
    {
      id: 'leaderboard',
      name: 'Leaderboard',
      path: '/leaderboard',
      icon: FaTrophy,
      category: 'community',
      description: 'Competitive rankings and achievements',
      features: ['Global Rankings', 'Points Tracking', 'Achievement System'],
      status: 'active',
      accessLevel: 'free',
      implementation: 'complete'
    },
    {
      id: 'teams',
      name: 'Team Dashboard',
      path: '/teams',
      icon: FaUsers,
      category: 'community',
      description: 'Team collaboration and management features',
      features: ['Team Management', 'Collaboration Tools', 'Team Analytics'],
      status: 'active',
      accessLevel: 'business',
      implementation: 'partial'
    },

    // Admin Dashboards
    {
      id: 'admin',
      name: 'Simple Admin',
      path: '/admin',
      icon: FaUserTie,
      category: 'admin',
      description: 'Basic admin dashboard focused on subscription management',
      features: ['User Subscription Management', 'Basic Analytics'],
      status: 'active',
      accessLevel: 'admin',
      implementation: 'complete'
    },
    {
      id: 'admin-modern',
      name: 'Modern Admin',
      path: '/admin-modern',
      icon: FaLaptopCode,
      category: 'admin',
      description: 'Modern admin interface with comprehensive features',
      features: ['User Management', 'Content Management', 'Analytics', 'Notifications'],
      status: 'active',
      accessLevel: 'admin',
      implementation: 'complete'
    },
    {
      id: 'super-admin',
      name: 'Enhanced Super Admin',
      path: '/super-admin',
      icon: FaUserSecret,
      category: 'admin',
      description: 'Complete platform management with white-label capabilities',
      features: ['Platform Statistics', 'Dashboard Management', 'White-Label Creation', 'User Management', 'Subscription Analytics', 'Notification System'],
      status: 'active',
      accessLevel: 'super-admin',
      implementation: 'complete'
    },

    // Other Features
    {
      id: 'profile',
      name: 'User Profile',
      path: '/profile',
      icon: FaUser,
      category: 'user',
      description: 'User profile management and settings',
      features: ['Profile Editing', 'Settings', 'Preferences'],
      status: 'active',
      accessLevel: 'free',
      implementation: 'complete'
    },
    {
      id: 'pricing',
      name: 'Pricing & Subscriptions',
      path: '/pricing',
      icon: FaCrown,
      category: 'business',
      description: 'Subscription plans and pricing information',
      features: ['Subscription Plans', 'Feature Comparison', 'Upgrade Options'],
      status: 'active',
      accessLevel: 'free',
      implementation: 'complete'
    },
    {
      id: 'products',
      name: 'Products',
      path: '/products',
      icon: FaStore,
      category: 'business',
      description: 'Product showcase and information',
      features: ['Product Information', 'Feature Details'],
      status: 'active',
      accessLevel: 'free',
      implementation: 'complete'
    },
    {
      id: 'blog',
      name: 'Blog',
      path: '/blog',
      icon: FaNewspaper,
      category: 'content',
      description: 'Educational blog posts and articles',
      features: ['Educational Articles', 'Security News', 'Tutorials'],
      status: 'active',
      accessLevel: 'free',
      implementation: 'complete'
    },

    // Core Platform Features
    {
      id: 'content-management',
      name: 'Content Management',
      path: '/content-management',
      icon: FaBook,
      category: 'content',
      description: 'Dynamic learning materials management system',
      features: ['Content creation', 'Media management', 'Analytics', 'Workflows'],
      status: 'active',
      accessLevel: 'premium',
      implementation: 'complete',
      isNew: true
    },
    {
      id: 'analytics-dashboard',
      name: 'Advanced Analytics',
      path: '/analytics-dashboard',
      icon: FaChartLine,
      category: 'analytics',
      description: 'Business intelligence and performance metrics',
      features: ['Real-time metrics', 'Trend analysis', 'Custom reports', 'Predictive analytics'],
      status: 'active',
      accessLevel: 'business',
      implementation: 'complete',
      isNew: true
    },
    {
      id: 'virtual-labs',
      name: 'Virtual Labs',
      path: '/virtual-labs',
      icon: FaLaptopCode,
      category: 'labs',
      description: 'Live virtual environments for hands-on learning',
      features: ['Container orchestration', 'Real-time access', 'Progress tracking', 'Resource management'],
      status: 'active',
      accessLevel: 'premium',
      implementation: 'complete',
      isNew: true
    },
    {
      id: 'certifications',
      name: 'Certification System',
      path: '/certifications',
      icon: FaTrophy,
      category: 'certification',
      description: 'Industry-recognized cybersecurity certifications',
      features: ['Digital certificates', 'Progress tracking', 'Blockchain verification', 'Continuing education'],
      status: 'active',
      accessLevel: 'premium',
      implementation: 'complete',
      isNew: true
    },
    {
      id: 'assessments',
      name: 'Assessment Engine',
      path: '/assessments',
      icon: FaCode,
      category: 'assessment',
      description: 'Advanced assessment system with automated grading',
      features: ['Smart grading', 'Question banks', 'Proctoring', 'Performance analytics'],
      status: 'active',
      accessLevel: 'premium',
      implementation: 'complete',
      isNew: true
    },

    // Web3 Features
    {
      id: 'web3-dashboard',
      name: 'Web3 Dashboard',
      path: '/web3',
      icon: FaRocket,
      category: 'web3',
      description: 'Decentralized platform with blockchain integration',
      features: ['Wallet connection', 'XCYBER tokens', 'NFT certificates', 'DAO governance'],
      status: 'active',
      accessLevel: 'premium',
      implementation: 'complete',
      isNew: true
    },
    {
      id: 'marketplace-web3',
      name: 'Web3 Marketplace',
      path: '/marketplace',
      icon: FaStore,
      category: 'web3',
      description: 'Decentralized content marketplace with crypto payments',
      features: ['Token payments', 'Creator rewards', 'Smart contracts', 'Revenue sharing'],
      status: 'active',
      accessLevel: 'premium',
      implementation: 'complete',
      isNew: true
    },
    {
      id: 'network-visualization',
      name: '3D Network Visualization',
      path: '/network-visualization',
      icon: FaLaptopCode,
      category: 'vr',
      description: 'Immersive 3D network security visualization',
      features: ['VR/AR support', '3D topology', 'Threat visualization', 'Real-time monitoring'],
      status: 'active',
      accessLevel: 'premium',
      implementation: 'complete',
      isNew: true
    }
  ];

  // Categories for filtering
  const categories = [
    { id: 'all', name: 'All Dashboards', icon: FaHome },
    { id: 'user', name: 'User Dashboards', icon: FaUser },
    { id: 'learning', name: 'Learning & Education', icon: FaBook },
    { id: 'challenges', name: 'Challenges & Games', icon: FaCode },
    { id: 'community', name: 'Community', icon: FaUsers },
    { id: 'admin', name: 'Administration', icon: FaUserTie },
    { id: 'business', name: 'Business', icon: FaStore },
    { id: 'content', name: 'Content', icon: FaNewspaper },
    { id: 'analytics', name: 'Analytics', icon: FaChartLine },
    { id: 'labs', name: 'Virtual Labs', icon: FaLaptopCode },
    { id: 'certification', name: 'Certifications', icon: FaTrophy },
    { id: 'assessment', name: 'Assessments', icon: FaQuestionCircle },
    { id: 'web3', name: 'Web3 & Blockchain', icon: FaRocket },
    { id: 'vr', name: 'VR/AR', icon: FaEye }
  ];

  // Check if user has access to a dashboard
  const hasAccess = (dashboard) => {
    // 🔧 DEVELOPMENT MODE: Temporarily disable all access restrictions
    const isDevelopmentMode = true;

    if (isDevelopmentMode) {
      console.log(`🔧 Dev Mode: Granting access to ${dashboard.name}`);
      return true;
    }

    // Production access control (will be enabled later)
    if (dashboard.accessLevel === 'free') return true;
    if (dashboard.accessLevel === 'premium') return ['Premium', 'Business'].includes(subscriptionLevel);
    if (dashboard.accessLevel === 'business') return subscriptionLevel === 'Business';
    if (dashboard.accessLevel === 'admin') return profile?.role === 'admin' || profile?.role === 'super_admin';
    if (dashboard.accessLevel === 'super-admin') return profile?.role === 'super_admin';
    return false;
  };

  // Filter dashboards based on search and category
  const filteredDashboards = dashboards.filter(dashboard => {
    const matchesSearch = dashboard.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dashboard.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dashboard.features.some(feature => feature.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = activeCategory === 'all' || dashboard.category === activeCategory;

    return matchesSearch && matchesCategory;
  });

  // Get access level badge
  const getAccessBadge = (accessLevel) => {
    switch (accessLevel) {
      case 'free':
        return { text: 'Free', color: 'bg-green-500', icon: null };
      case 'premium':
        return { text: 'Premium', color: 'bg-blue-500', icon: FaCrown };
      case 'business':
        return { text: 'Business', color: 'bg-purple-500', icon: FaGem };
      case 'admin':
        return { text: 'Admin', color: 'bg-orange-500', icon: FaUserTie };
      case 'super-admin':
        return { text: 'Super Admin', color: 'bg-red-500', icon: FaUserSecret };
      default:
        return { text: 'Unknown', color: 'bg-gray-500', icon: null };
    }
  };

  // Get implementation status badge
  const getStatusBadge = (implementation) => {
    switch (implementation) {
      case 'complete':
        return { text: 'Complete', color: 'bg-green-500' };
      case 'partial':
        return { text: 'Partial', color: 'bg-yellow-500' };
      case 'planned':
        return { text: 'Planned', color: 'bg-gray-500' };
      default:
        return { text: 'Unknown', color: 'bg-gray-500' };
    }
  };

  // Handle navigation
  const handleNavigate = (path) => {
    navigate(path);
    setIsOpen(false);
  };

  return (
    <>
      {/* Floating Navigation Button */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className={`fixed top-4 right-4 z-40 w-12 h-12 rounded-full ${
          darkMode ? 'bg-[#88cc14] text-black' : 'bg-[#88cc14] text-black'
        } flex items-center justify-center shadow-lg hover:scale-110 transition-transform`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
      >
        <FaBars className="text-lg" />
      </motion.button>

      {/* Navigation Modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
            style={{ backgroundColor: 'rgba(0, 0, 0, 0.8)' }}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className={`w-full max-w-6xl max-h-[90vh] rounded-lg overflow-hidden ${
                darkMode ? 'bg-[#1A1F35] text-white' : 'bg-white text-gray-900'
              }`}
            >
              {/* Header */}
              <div className={`p-6 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-2xl font-bold">Dashboard Navigator</h2>
                  <button
                    onClick={() => setIsOpen(false)}
                    className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
                  >
                    <FaTimes />
                  </button>
                </div>

                {/* Search */}
                <div className="relative mb-4">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search dashboards, features, or descriptions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
                      darkMode
                        ? 'bg-[#252D4A] border-gray-600 text-white placeholder-gray-400'
                        : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                  />
                </div>

                {/* Category Filters */}
                <div className="flex flex-wrap gap-2">
                  {categories.map(category => (
                    <button
                      key={category.id}
                      onClick={() => setActiveCategory(category.id)}
                      className={`px-3 py-1 rounded-full text-sm flex items-center gap-2 transition-colors ${
                        activeCategory === category.id
                          ? 'bg-[#88cc14] text-black'
                          : darkMode
                            ? 'bg-[#252D4A] text-gray-300 hover:bg-gray-600'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      <category.icon className="text-xs" />
                      {category.name}
                    </button>
                  ))}
                </div>
              </div>

              {/* Dashboard Grid */}
              <div className="p-6 overflow-y-auto max-h-[60vh]">
                {filteredDashboards.length === 0 ? (
                  <div className="text-center py-12">
                    <FaSearch className="mx-auto text-4xl text-gray-400 mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No dashboards found</h3>
                    <p className="text-gray-500">Try adjusting your search or category filter</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {filteredDashboards.map(dashboard => {
                      const IconComponent = dashboard.icon;
                      const accessBadge = getAccessBadge(dashboard.accessLevel);
                      const statusBadge = getStatusBadge(dashboard.implementation);
                      const userHasAccess = hasAccess(dashboard);
                      const isCurrentPage = location.pathname === dashboard.path;

                      return (
                        <motion.div
                          key={dashboard.id}
                          whileHover={{ y: -2 }}
                          className={`p-4 rounded-lg border cursor-pointer transition-all ${
                            isCurrentPage
                              ? 'border-[#88cc14] bg-[#88cc14]/10'
                              : darkMode
                                ? 'border-gray-700 bg-[#252D4A] hover:border-gray-600'
                                : 'border-gray-200 bg-gray-50 hover:border-gray-300'
                          } ${!userHasAccess ? 'opacity-60' : ''}`}
                          onClick={() => userHasAccess && handleNavigate(dashboard.path)}
                        >
                          {/* Header */}
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-lg ${
                                isCurrentPage ? 'bg-[#88cc14] text-black' : 'bg-[#88cc14]/20 text-[#88cc14]'
                              }`}>
                                <IconComponent className="text-lg" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-sm">{dashboard.name}</h3>
                                <div className="flex items-center gap-2 mt-1">
                                  <span className={`px-2 py-1 rounded-full text-xs text-white ${accessBadge.color}`}>
                                    {accessBadge.icon && <accessBadge.icon className="inline mr-1" />}
                                    {accessBadge.text}
                                  </span>
                                  <span className={`px-2 py-1 rounded-full text-xs text-white ${statusBadge.color}`}>
                                    {statusBadge.text}
                                  </span>
                                </div>
                              </div>
                            </div>
                            {!userHasAccess && <FaLock className="text-gray-400" />}
                            {isCurrentPage && <div className="w-2 h-2 bg-[#88cc14] rounded-full" />}
                          </div>

                          {/* Description */}
                          <p className={`text-sm mb-3 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            {dashboard.description}
                          </p>

                          {/* Features */}
                          <div className="mb-3">
                            <h4 className="text-xs font-semibold mb-2 text-gray-500 uppercase">Features</h4>
                            <div className="flex flex-wrap gap-1">
                              {dashboard.features.slice(0, 3).map((feature, index) => (
                                <span
                                  key={index}
                                  className={`px-2 py-1 rounded text-xs ${
                                    darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'
                                  }`}
                                >
                                  {feature}
                                </span>
                              ))}
                              {dashboard.features.length > 3 && (
                                <span className={`px-2 py-1 rounded text-xs ${
                                  darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'
                                }`}>
                                  +{dashboard.features.length - 3} more
                                </span>
                              )}
                            </div>
                          </div>

                          {/* Action */}
                          <div className="flex items-center justify-between">
                            <span className={`text-xs ${
                              dashboard.status === 'active' ? 'text-green-500' : 'text-yellow-500'
                            }`}>
                              ● {dashboard.status}
                            </span>
                            {userHasAccess ? (
                              <div className="flex items-center text-[#88cc14] text-sm">
                                {isCurrentPage ? 'Current' : 'Open'}
                                <FaArrowRight className="ml-1" />
                              </div>
                            ) : (
                              <div className="flex items-center text-gray-400 text-sm">
                                <FaLock className="mr-1" />
                                Locked
                              </div>
                            )}
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                )}

                {/* Summary Stats */}
                <div className={`mt-6 p-4 rounded-lg ${darkMode ? 'bg-[#252D4A]' : 'bg-gray-100'}`}>
                  <h3 className="font-semibold mb-2">Dashboard Summary</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <div className="text-2xl font-bold text-[#88cc14]">{dashboards.length}</div>
                      <div className="text-gray-500">Total Dashboards</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-500">
                        {dashboards.filter(d => hasAccess(d)).length}
                      </div>
                      <div className="text-gray-500">Accessible</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-blue-500">
                        {dashboards.filter(d => d.implementation === 'complete').length}
                      </div>
                      <div className="text-gray-500">Complete</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-yellow-500">
                        {dashboards.filter(d => d.implementation === 'partial').length}
                      </div>
                      <div className="text-gray-500">In Progress</div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default DashboardNavigator;
