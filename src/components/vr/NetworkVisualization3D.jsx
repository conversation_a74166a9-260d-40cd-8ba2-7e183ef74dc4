import React, { useRef, useEffect, useState, Suspense } from 'react';
import { <PERSON><PERSON>, useFrame, useThree } from '@react-three/fiber';
import { OrbitControls, Text, Sphere, Line, Html } from '@react-three/drei';
import { motion } from 'framer-motion';
import * as THREE from 'three';
import {
  FaVrCardboard, FaExpand, FaCompress, FaPlay, FaPause,
  FaRedo, FaEye, FaCog, FaShieldAlt, FaExclamationTriangle,
  FaLock, FaUnlock, FaWifi, FaServer, FaDesktop, FaMobile
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

/**
 * 3D Network Visualization Component
 * 
 * Interactive 3D network topology visualization with VR/AR support,
 * security threat visualization, and real-time network monitoring.
 */

// Network node component
const NetworkNode = ({ position, nodeData, onClick, isSelected, threatLevel }) => {
  const meshRef = useRef();
  const [hovered, setHovered] = useState(false);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.01;
      if (hovered) {
        meshRef.current.scale.setScalar(1.2);
      } else {
        meshRef.current.scale.setScalar(1);
      }
    }
  });

  const getNodeColor = () => {
    if (threatLevel === 'high') return '#ff4444';
    if (threatLevel === 'medium') return '#ffaa44';
    if (threatLevel === 'low') return '#44ff44';
    return '#4488ff';
  };

  const getNodeIcon = () => {
    switch (nodeData.type) {
      case 'server': return '🖥️';
      case 'router': return '📡';
      case 'firewall': return '🛡️';
      case 'switch': return '🔀';
      case 'endpoint': return '💻';
      case 'mobile': return '📱';
      default: return '🔵';
    }
  };

  return (
    <group position={position}>
      <Sphere
        ref={meshRef}
        args={[0.5, 32, 32]}
        onClick={onClick}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        <meshStandardMaterial
          color={getNodeColor()}
          emissive={isSelected ? '#ffffff' : '#000000'}
          emissiveIntensity={isSelected ? 0.3 : 0}
          transparent
          opacity={0.8}
        />
      </Sphere>
      
      {/* Node label */}
      <Html distanceFactor={10}>
        <div className="bg-black/80 text-white px-2 py-1 rounded text-xs whitespace-nowrap">
          <span className="mr-1">{getNodeIcon()}</span>
          {nodeData.name}
        </div>
      </Html>
      
      {/* Threat indicator */}
      {threatLevel !== 'none' && (
        <Sphere position={[0.7, 0.7, 0]} args={[0.2, 16, 16]}>
          <meshBasicMaterial color={threatLevel === 'high' ? '#ff0000' : '#ffaa00'} />
        </Sphere>
      )}
    </group>
  );
};

// Network connection component
const NetworkConnection = ({ start, end, connectionType, isActive, threatDetected }) => {
  const lineRef = useRef();
  const [opacity, setOpacity] = useState(0.6);

  useFrame((state) => {
    if (lineRef.current && isActive) {
      const time = state.clock.getElapsedTime();
      setOpacity(0.6 + 0.4 * Math.sin(time * 3));
    }
  });

  const getConnectionColor = () => {
    if (threatDetected) return '#ff4444';
    if (connectionType === 'secure') return '#44ff44';
    if (connectionType === 'encrypted') return '#4444ff';
    return '#888888';
  };

  return (
    <Line
      ref={lineRef}
      points={[start, end]}
      color={getConnectionColor()}
      lineWidth={threatDetected ? 3 : isActive ? 2 : 1}
      transparent
      opacity={opacity}
    />
  );
};

// Threat visualization component
const ThreatVisualization = ({ threats }) => {
  return (
    <>
      {threats.map((threat, index) => (
        <group key={index} position={threat.position}>
          {/* Pulsing threat indicator */}
          <Sphere args={[1, 16, 16]}>
            <meshBasicMaterial
              color="#ff0000"
              transparent
              opacity={0.3}
            />
          </Sphere>
          
          {/* Threat label */}
          <Html distanceFactor={15}>
            <div className="bg-red-600 text-white px-3 py-2 rounded-lg shadow-lg">
              <div className="flex items-center">
                <FaExclamationTriangle className="mr-2" />
                <div>
                  <div className="font-bold">{threat.type}</div>
                  <div className="text-xs">{threat.description}</div>
                </div>
              </div>
            </div>
          </Html>
        </group>
      ))}
    </>
  );
};

// Main 3D scene component
const NetworkScene = ({ networkData, selectedNode, onNodeSelect, showThreats, animationSpeed }) => {
  const { camera } = useThree();
  
  useEffect(() => {
    camera.position.set(10, 10, 10);
    camera.lookAt(0, 0, 0);
  }, [camera]);

  return (
    <>
      {/* Lighting */}
      <ambientLight intensity={0.6} />
      <pointLight position={[10, 10, 10]} intensity={1} />
      <pointLight position={[-10, -10, -10]} intensity={0.5} />
      
      {/* Network nodes */}
      {networkData.nodes.map((node, index) => (
        <NetworkNode
          key={node.id}
          position={node.position}
          nodeData={node}
          onClick={() => onNodeSelect(node)}
          isSelected={selectedNode?.id === node.id}
          threatLevel={node.threatLevel || 'none'}
        />
      ))}
      
      {/* Network connections */}
      {networkData.connections.map((connection, index) => (
        <NetworkConnection
          key={index}
          start={connection.start}
          end={connection.end}
          connectionType={connection.type}
          isActive={connection.isActive}
          threatDetected={connection.threatDetected}
        />
      ))}
      
      {/* Threat visualizations */}
      {showThreats && (
        <ThreatVisualization threats={networkData.threats || []} />
      )}
      
      {/* Grid floor */}
      <gridHelper args={[20, 20, '#444444', '#222222']} />
      
      {/* Controls */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        maxDistance={50}
        minDistance={2}
      />
    </>
  );
};

// Main component
const NetworkVisualization3D = () => {
  const { darkMode } = useGlobalTheme();
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isVRMode, setIsVRMode] = useState(false);
  const [selectedNode, setSelectedNode] = useState(null);
  const [showThreats, setShowThreats] = useState(true);
  const [animationSpeed, setAnimationSpeed] = useState(1);
  const [isPlaying, setIsPlaying] = useState(true);
  const canvasRef = useRef();

  // Mock network data
  const [networkData] = useState({
    nodes: [
      {
        id: 'firewall-1',
        name: 'Main Firewall',
        type: 'firewall',
        position: [0, 0, 0],
        threatLevel: 'low',
        status: 'active',
        connections: 5
      },
      {
        id: 'server-1',
        name: 'Web Server',
        type: 'server',
        position: [4, 2, 0],
        threatLevel: 'high',
        status: 'compromised',
        connections: 3
      },
      {
        id: 'server-2',
        name: 'Database Server',
        type: 'server',
        position: [-4, 2, 0],
        threatLevel: 'medium',
        status: 'warning',
        connections: 2
      },
      {
        id: 'router-1',
        name: 'Core Router',
        type: 'router',
        position: [0, 4, 0],
        threatLevel: 'none',
        status: 'active',
        connections: 8
      },
      {
        id: 'switch-1',
        name: 'Access Switch',
        type: 'switch',
        position: [6, 0, 3],
        threatLevel: 'low',
        status: 'active',
        connections: 12
      },
      {
        id: 'endpoint-1',
        name: 'Admin Workstation',
        type: 'endpoint',
        position: [8, -2, 3],
        threatLevel: 'medium',
        status: 'suspicious',
        connections: 1
      },
      {
        id: 'mobile-1',
        name: 'Mobile Device',
        type: 'mobile',
        position: [-6, -2, -3],
        threatLevel: 'high',
        status: 'infected',
        connections: 1
      }
    ],
    connections: [
      {
        start: [0, 0, 0],
        end: [4, 2, 0],
        type: 'encrypted',
        isActive: true,
        threatDetected: true
      },
      {
        start: [0, 0, 0],
        end: [-4, 2, 0],
        type: 'secure',
        isActive: true,
        threatDetected: false
      },
      {
        start: [0, 0, 0],
        end: [0, 4, 0],
        type: 'standard',
        isActive: true,
        threatDetected: false
      },
      {
        start: [0, 4, 0],
        end: [6, 0, 3],
        type: 'standard',
        isActive: true,
        threatDetected: false
      },
      {
        start: [6, 0, 3],
        end: [8, -2, 3],
        type: 'standard',
        isActive: false,
        threatDetected: true
      },
      {
        start: [0, 0, 0],
        end: [-6, -2, -3],
        type: 'wireless',
        isActive: true,
        threatDetected: true
      }
    ],
    threats: [
      {
        id: 'threat-1',
        type: 'Malware Detected',
        description: 'Suspicious activity on mobile device',
        position: [-6, -1, -3],
        severity: 'high'
      },
      {
        id: 'threat-2',
        type: 'Unauthorized Access',
        description: 'Failed login attempts on web server',
        position: [4, 3, 0],
        severity: 'high'
      },
      {
        id: 'threat-3',
        type: 'Data Exfiltration',
        description: 'Unusual outbound traffic detected',
        position: [8, -1, 3],
        severity: 'medium'
      }
    ]
  });

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      canvasRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const toggleVRMode = () => {
    setIsVRMode(!isVRMode);
    // In a real implementation, this would initialize WebXR
    console.log('VR Mode:', !isVRMode);
  };

  const resetView = () => {
    setSelectedNode(null);
    // Reset camera position would be handled by OrbitControls
  };

  const renderNodeDetails = () => {
    if (!selectedNode) return null;

    return (
      <motion.div
        initial={{ opacity: 0, x: 300 }}
        animate={{ opacity: 1, x: 0 }}
        className={`absolute top-4 right-4 w-80 p-4 rounded-lg ${
          darkMode ? 'bg-[#1A1F35] border border-gray-700' : 'bg-white border border-gray-200'
        } shadow-lg`}
      >
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-bold">{selectedNode.name}</h3>
          <button
            onClick={() => setSelectedNode(null)}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>
        
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-500">Type:</span>
            <span className="font-medium">{selectedNode.type}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-500">Status:</span>
            <span className={`px-2 py-1 rounded text-xs ${
              selectedNode.status === 'active' ? 'bg-green-500/20 text-green-500' :
              selectedNode.status === 'warning' ? 'bg-yellow-500/20 text-yellow-500' :
              selectedNode.status === 'compromised' ? 'bg-red-500/20 text-red-500' :
              'bg-gray-500/20 text-gray-500'
            }`}>
              {selectedNode.status}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-500">Threat Level:</span>
            <span className={`px-2 py-1 rounded text-xs ${
              selectedNode.threatLevel === 'high' ? 'bg-red-500/20 text-red-500' :
              selectedNode.threatLevel === 'medium' ? 'bg-yellow-500/20 text-yellow-500' :
              selectedNode.threatLevel === 'low' ? 'bg-green-500/20 text-green-500' :
              'bg-gray-500/20 text-gray-500'
            }`}>
              {selectedNode.threatLevel || 'none'}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-500">Connections:</span>
            <span className="font-medium">{selectedNode.connections}</span>
          </div>
        </div>
        
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <h4 className="font-medium mb-2">Actions</h4>
          <div className="flex gap-2">
            <button className="flex-1 bg-blue-500 text-white px-3 py-2 rounded text-sm hover:bg-blue-600">
              Investigate
            </button>
            <button className="flex-1 bg-red-500 text-white px-3 py-2 rounded text-sm hover:bg-red-600">
              Isolate
            </button>
          </div>
        </div>
      </motion.div>
    );
  };

  const renderControls = () => (
    <div className="absolute bottom-4 left-4 flex gap-2">
      <button
        onClick={() => setIsPlaying(!isPlaying)}
        className={`p-3 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'} hover:bg-gray-100 dark:hover:bg-gray-800`}
      >
        {isPlaying ? <FaPause /> : <FaPlay />}
      </button>
      
      <button
        onClick={resetView}
        className={`p-3 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'} hover:bg-gray-100 dark:hover:bg-gray-800`}
      >
        <FaRedo />
      </button>
      
      <button
        onClick={() => setShowThreats(!showThreats)}
        className={`p-3 rounded-lg ${showThreats ? 'bg-red-500 text-white' : darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'} hover:bg-gray-100 dark:hover:bg-gray-800`}
      >
        <FaShieldAlt />
      </button>
      
      <button
        onClick={toggleVRMode}
        className={`p-3 rounded-lg ${isVRMode ? 'bg-[#88cc14] text-black' : darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'} hover:bg-gray-100 dark:hover:bg-gray-800`}
      >
        <FaVrCardboard />
      </button>
      
      <button
        onClick={toggleFullscreen}
        className={`p-3 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'} hover:bg-gray-100 dark:hover:bg-gray-800`}
      >
        {isFullscreen ? <FaCompress /> : <FaExpand />}
      </button>
    </div>
  );

  const renderLegend = () => (
    <div className={`absolute top-4 left-4 p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35] border border-gray-700' : 'bg-white border border-gray-200'} shadow-lg`}>
      <h3 className="font-bold mb-3">Network Legend</h3>
      
      <div className="space-y-2 text-sm">
        <div className="flex items-center">
          <div className="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
          <span>Normal</span>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
          <span>Secure</span>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-4 bg-yellow-500 rounded-full mr-2"></div>
          <span>Warning</span>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-4 bg-red-500 rounded-full mr-2"></div>
          <span>Threat</span>
        </div>
      </div>
      
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <h4 className="font-medium mb-2">Device Types</h4>
        <div className="grid grid-cols-2 gap-1 text-xs">
          <div>🖥️ Server</div>
          <div>📡 Router</div>
          <div>🛡️ Firewall</div>
          <div>🔀 Switch</div>
          <div>💻 Endpoint</div>
          <div>📱 Mobile</div>
        </div>
      </div>
    </div>
  );

  return (
    <div className={`relative w-full h-screen ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'}`}>
      <div ref={canvasRef} className="w-full h-full">
        <Canvas
          camera={{ position: [10, 10, 10], fov: 75 }}
          gl={{ antialias: true, alpha: false }}
        >
          <Suspense fallback={null}>
            <NetworkScene
              networkData={networkData}
              selectedNode={selectedNode}
              onNodeSelect={setSelectedNode}
              showThreats={showThreats}
              animationSpeed={animationSpeed}
            />
          </Suspense>
        </Canvas>
      </div>
      
      {renderLegend()}
      {renderNodeDetails()}
      {renderControls()}
      
      {/* VR Mode Indicator */}
      {isVRMode && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-[#88cc14] text-black px-6 py-3 rounded-lg font-bold">
          VR Mode Active - Put on your headset!
        </div>
      )}
    </div>
  );
};

export default NetworkVisualization3D;
