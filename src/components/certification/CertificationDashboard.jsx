import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaCertificate, FaTrophy, FaGraduationCap, FaCalendarAlt, FaCheck,
  FaClock, FaUsers, FaChartLine, FaDownload, FaEye, FaPlay,
  FaBook, FaCode, FaShieldAlt, FaNetworkWired, FaCloud, FaStar,
  FaAward, FaExternalLinkAlt, FaSpinner, FaCheckCircle, FaTimesCircle
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import certificationService from '../../services/certificationService';

/**
 * Certification Dashboard
 * 
 * Comprehensive dashboard for managing certifications, tracking progress,
 * and viewing industry-recognized credentials.
 */
const CertificationDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [programs, setPrograms] = useState([]);
  const [userCertifications, setUserCertifications] = useState([]);
  const [progress, setProgress] = useState({});
  const [selectedProgram, setSelectedProgram] = useState(null);

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [programsResult, certificationsResult] = await Promise.all([
          certificationService.getCertificationPrograms(),
          certificationService.getUserCertifications()
        ]);

        if (programsResult.success) setPrograms(programsResult.programs);
        if (certificationsResult.success) setUserCertifications(certificationsResult.certifications);
      } catch (error) {
        console.error('Error fetching certification data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Enroll in certification program
  const handleEnroll = async (programId) => {
    try {
      const result = await certificationService.enrollInProgram(programId);
      if (result.success) {
        alert('Successfully enrolled in certification program!');
        // Refresh data
        const programsResult = await certificationService.getCertificationPrograms();
        if (programsResult.success) setPrograms(programsResult.programs);
      } else {
        alert('Failed to enroll: ' + result.error);
      }
    } catch (error) {
      console.error('Error enrolling:', error);
      alert('Failed to enroll in program');
    }
  };

  // Get level color
  const getLevelColor = (level) => {
    switch (level) {
      case 'foundation': return 'bg-green-500/20 text-green-500';
      case 'associate': return 'bg-blue-500/20 text-blue-500';
      case 'professional': return 'bg-purple-500/20 text-purple-500';
      case 'expert': return 'bg-red-500/20 text-red-500';
      default: return 'bg-gray-500/20 text-gray-500';
    }
  };

  // Get category icon
  const getCategoryIcon = (category) => {
    switch (category) {
      case 'web_security': return <FaShieldAlt className="text-blue-500" />;
      case 'network_security': return <FaNetworkWired className="text-green-500" />;
      case 'cloud_security': return <FaCloud className="text-purple-500" />;
      case 'penetration_testing': return <FaCode className="text-red-500" />;
      case 'security_analysis': return <FaChartLine className="text-orange-500" />;
      default: return <FaCertificate className="text-gray-500" />;
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-500/20 text-green-500';
      case 'expired': return 'bg-red-500/20 text-red-500';
      case 'suspended': return 'bg-yellow-500/20 text-yellow-500';
      case 'revoked': return 'bg-gray-500/20 text-gray-500';
      default: return 'bg-gray-500/20 text-gray-500';
    }
  };

  // Render overview tab
  const renderOverview = () => (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Available Programs</p>
              <p className="text-2xl font-bold text-[#88cc14]">{programs.length}</p>
            </div>
            <FaCertificate className="text-3xl text-[#88cc14]" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>My Certifications</p>
              <p className="text-2xl font-bold text-blue-500">{userCertifications.length}</p>
            </div>
            <FaTrophy className="text-3xl text-blue-500" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Active Certs</p>
              <p className="text-2xl font-bold text-green-500">
                {userCertifications.filter(cert => cert.status === 'active').length}
              </p>
            </div>
            <FaCheckCircle className="text-3xl text-green-500" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Expiring Soon</p>
              <p className="text-2xl font-bold text-orange-500">
                {userCertifications.filter(cert => {
                  const expiryDate = new Date(cert.expiry_date);
                  const threeMonthsFromNow = new Date();
                  threeMonthsFromNow.setMonth(threeMonthsFromNow.getMonth() + 3);
                  return expiryDate <= threeMonthsFromNow && cert.status === 'active';
                }).length}
              </p>
            </div>
            <FaClock className="text-3xl text-orange-500" />
          </div>
        </motion.div>
      </div>

      {/* My Certifications */}
      {userCertifications.length > 0 && (
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <h3 className="text-lg font-semibold mb-4">My Certifications</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {userCertifications.slice(0, 6).map(cert => (
              <motion.div
                key={cert.id}
                whileHover={{ scale: 1.02 }}
                className={`p-4 rounded-lg border ${darkMode ? 'border-gray-600 bg-[#252D4A]' : 'border-gray-200 bg-gray-50'}`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getCategoryIcon(cert.certification_programs.category)}
                    <span className={`px-2 py-1 rounded text-xs ${getLevelColor(cert.certification_programs.level)}`}>
                      {cert.certification_programs.level}
                    </span>
                  </div>
                  <span className={`px-2 py-1 rounded text-xs ${getStatusColor(cert.status)}`}>
                    {cert.status}
                  </span>
                </div>
                
                <h4 className="font-semibold text-sm mb-1">{cert.certification_programs.name}</h4>
                <p className="text-xs text-gray-500 mb-2">Certificate #{cert.certificate_number}</p>
                
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Issued: {new Date(cert.issued_date).toLocaleDateString()}</span>
                  <span>Expires: {new Date(cert.expiry_date).toLocaleDateString()}</span>
                </div>
                
                {cert.grade && (
                  <div className="mt-2 flex items-center justify-between">
                    <span className="text-xs text-gray-500">Grade:</span>
                    <span className="text-sm font-bold text-[#88cc14]">{cert.grade}</span>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
          
          {userCertifications.length > 6 && (
            <div className="text-center mt-4">
              <button
                onClick={() => setActiveTab('my-certs')}
                className="text-[#88cc14] hover:underline"
              >
                View All Certifications ({userCertifications.length})
              </button>
            </div>
          )}
        </div>
      )}

      {/* Quick Actions */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            onClick={() => setActiveTab('programs')}
            className="p-4 bg-[#88cc14] text-black rounded-lg hover:bg-[#7ab512] flex flex-col items-center gap-2"
          >
            <FaCertificate className="text-xl" />
            <span className="text-sm font-medium">Browse Programs</span>
          </button>
          
          <button
            onClick={() => setActiveTab('my-certs')}
            className="p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex flex-col items-center gap-2"
          >
            <FaTrophy className="text-xl" />
            <span className="text-sm font-medium">My Certificates</span>
          </button>
          
          <button
            onClick={() => setActiveTab('progress')}
            className="p-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 flex flex-col items-center gap-2"
          >
            <FaChartLine className="text-xl" />
            <span className="text-sm font-medium">Track Progress</span>
          </button>
          
          <button
            onClick={() => setActiveTab('verify')}
            className="p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 flex flex-col items-center gap-2"
          >
            <FaCheckCircle className="text-xl" />
            <span className="text-sm font-medium">Verify Certificate</span>
          </button>
        </div>
      </div>
    </div>
  );

  // Render certification programs tab
  const renderPrograms = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {programs.map(program => (
          <motion.div
            key={program.id}
            whileHover={{ scale: 1.02 }}
            className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-2">
                {getCategoryIcon(program.category)}
                <span className={`px-2 py-1 rounded text-xs ${getLevelColor(program.level)}`}>
                  {program.level}
                </span>
              </div>
              <span className="px-2 py-1 bg-blue-500/20 text-blue-500 rounded text-xs">
                {program.code}
              </span>
            </div>
            
            <h3 className="text-lg font-semibold mb-2">{program.name}</h3>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 line-clamp-3`}>
              {program.description}
            </p>
            
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Requirements:</span>
                <span className="font-medium">{program.certification_requirements?.length || 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Validity:</span>
                <span className="font-medium">{program.validity_period_months} months</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Pass Rate:</span>
                <span className="font-medium text-green-500">
                  {program.certification_analytics?.[0]?.pass_rate || 0}%
                </span>
              </div>
            </div>
            
            {program.prerequisites && program.prerequisites.length > 0 && (
              <div className="mb-4">
                <p className="text-sm font-medium mb-2">Prerequisites:</p>
                <ul className="text-xs text-gray-500 space-y-1">
                  {program.prerequisites.slice(0, 3).map((prereq, index) => (
                    <li key={index}>• {prereq}</li>
                  ))}
                </ul>
              </div>
            )}
            
            <div className="flex gap-2">
              <button
                onClick={() => handleEnroll(program.id)}
                className="flex-1 bg-[#88cc14] text-black px-3 py-2 rounded text-sm hover:bg-[#7ab512] flex items-center justify-center"
              >
                <FaPlay className="mr-2" />
                Enroll Now
              </button>
              <button
                onClick={() => setSelectedProgram(program)}
                className="bg-blue-500 text-white px-3 py-2 rounded text-sm hover:bg-blue-600"
              >
                <FaEye />
              </button>
            </div>
            
            {program.certification_fee > 0 && (
              <div className="mt-3 text-center">
                <span className="text-lg font-bold text-[#88cc14]">
                  ${(program.certification_fee / 100).toFixed(2)}
                </span>
                <span className="text-sm text-gray-500 ml-1">certification fee</span>
              </div>
            )}
          </motion.div>
        ))}
      </div>
    </div>
  );

  // Render my certifications tab
  const renderMyCertifications = () => (
    <div className="space-y-6">
      {userCertifications.length === 0 ? (
        <div className="text-center py-12">
          <FaCertificate className="text-4xl text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Certifications Yet</h3>
          <p className="text-gray-500 mb-4">Start your certification journey today</p>
          <button
            onClick={() => setActiveTab('programs')}
            className="bg-[#88cc14] text-black px-6 py-3 rounded-lg hover:bg-[#7ab512]"
          >
            Browse Certification Programs
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6">
          {userCertifications.map(cert => (
            <motion.div
              key={cert.id}
              whileHover={{ scale: 1.01 }}
              className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {getCategoryIcon(cert.certification_programs.category)}
                    <div>
                      <h3 className="text-lg font-semibold">{cert.certification_programs.name}</h3>
                      <p className="text-sm text-gray-500">Certificate #{cert.certificate_number}</p>
                    </div>
                  </div>
                  <span className={`px-3 py-1 rounded text-sm ${getStatusColor(cert.status)}`}>
                    {cert.status}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 flex items-center">
                    <FaDownload className="mr-2" />
                    Download
                  </button>
                  <button className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 flex items-center">
                    <FaExternalLinkAlt className="mr-2" />
                    Verify
                  </button>
                </div>
              </div>
              
              <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Issued:</span>
                  <p className="font-medium">{new Date(cert.issued_date).toLocaleDateString()}</p>
                </div>
                <div>
                  <span className="text-gray-500">Expires:</span>
                  <p className="font-medium">{new Date(cert.expiry_date).toLocaleDateString()}</p>
                </div>
                <div>
                  <span className="text-gray-500">Grade:</span>
                  <p className="font-medium text-[#88cc14]">{cert.grade || 'N/A'}</p>
                </div>
                <div>
                  <span className="text-gray-500">Score:</span>
                  <p className="font-medium">{cert.final_score || 'N/A'}%</p>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-gray-500/10 rounded">
                <p className="text-sm text-gray-500 mb-1">Verification Code:</p>
                <p className="text-sm font-mono">{cert.verification_code}</p>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );

  // Navigation tabs
  const tabs = [
    { id: 'overview', name: 'Overview', icon: FaChartLine },
    { id: 'programs', name: 'Certification Programs', icon: FaCertificate },
    { id: 'my-certs', name: 'My Certifications', icon: FaTrophy },
    { id: 'progress', name: 'Progress Tracking', icon: FaGraduationCap },
    { id: 'verify', name: 'Verify Certificate', icon: FaCheckCircle }
  ];

  if (loading) {
    return (
      <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} flex items-center justify-center`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14] mx-auto mb-4"></div>
          <p className={`${darkMode ? 'text-white' : 'text-gray-900'}`}>Loading Certifications...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Header */}
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-b px-6 py-4`}>
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <FaCertificate className="mr-3 text-[#88cc14]" />
              Certification Dashboard
            </h1>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Earn industry-recognized cybersecurity certifications
            </p>
          </div>
          <button
            onClick={() => setActiveTab('programs')}
            className="bg-[#88cc14] text-black px-4 py-2 rounded-lg hover:bg-[#7ab512] flex items-center"
          >
            <FaCertificate className="mr-2" />
            Browse Programs
          </button>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className={`w-64 ${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-r min-h-screen`}>
          <nav className="p-4">
            <div className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center transition-colors ${
                    activeTab === tab.id
                      ? 'bg-[#88cc14] text-black'
                      : darkMode
                        ? 'hover:bg-[#252D4A] text-gray-300'
                        : 'hover:bg-gray-100 text-gray-700'
                  }`}
                >
                  <tab.icon className="mr-3" />
                  {tab.name}
                </button>
              ))}
            </div>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'programs' && renderPrograms()}
          {activeTab === 'my-certs' && renderMyCertifications()}
          {activeTab === 'progress' && (
            <div className="text-center py-12">
              <FaGraduationCap className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Progress Tracking</h3>
              <p className="text-gray-500 mb-4">Track your certification progress and requirements</p>
            </div>
          )}
          {activeTab === 'verify' && (
            <div className="text-center py-12">
              <FaCheckCircle className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Certificate Verification</h3>
              <p className="text-gray-500 mb-4">Verify the authenticity of XCerberus certificates</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CertificationDashboard;
