import React, { useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaCode, FaLock, FaBug, FaHeart, FaThumbsUp, FaThumbsDown, FaSpinner } from 'react-icons/fa';
import { useChat } from '../../contexts/ChatContext';
import { FEEDBACK_TYPES, handleResponseFeedback } from '../../lib/cybersecurityLLM';
import XCerberusIcon from '../icons/XCerberusIcon';

function ChatMessages() {
  const { state } = useChat();
  const { messages, isTyping } = state;
  const messagesEndRef = useRef(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const getMessageIcon = (type, category) => {
    if (type === 'user') return null;

    switch (category) {
      case 'technical':
        return <FaCode className="text-blue-400" />;
      case 'security':
        return <FaLock className="text-red-400" />;
      case 'challenges':
        return <FaBug className="text-purple-400" />;
      default:
        return <XCerberusIcon size={16} />;
    }
  };

  const formatContent = (content) => {
    if (!content) return '';

    // Track if we're inside a list to properly group list items
    let inUnorderedList = false;
    let inOrderedList = false;
    let listItems = [];
    let result = [];
    let codeBlock = false;
    let codeLanguage = '';
    let codeContent = [];
    let tableRows = [];
    let inTable = false;

    // Split content into sections
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Handle code blocks
      if (line.startsWith('```')) {
        if (!codeBlock) {
          // Start of code block
          codeBlock = true;
          codeLanguage = line.slice(3).trim();
          codeContent = [];
        } else {
          // End of code block
          result.push(
            <pre key={`code-${i}`} className="bg-gray-800 p-3 rounded my-3 overflow-x-auto w-full">
              <code className="text-gray-300 font-mono text-sm">{codeContent.join('\n')}</code>
            </pre>
          );
          codeBlock = false;
        }
        continue;
      }

      if (codeBlock) {
        codeContent.push(line);
        continue;
      }

      // Headers
      if (line.startsWith('##') && !line.startsWith('###')) {
        // Finish any open lists
        if (inUnorderedList) {
          result.push(<ul key={`ul-${i}`} className="list-disc pl-5 mb-3">{listItems}</ul>);
          listItems = [];
          inUnorderedList = false;
        }
        if (inOrderedList) {
          result.push(<ol key={`ol-${i}`} className="list-decimal pl-5 mb-3">{listItems}</ol>);
          listItems = [];
          inOrderedList = false;
        }

        result.push(
          <h2 key={`h2-${i}`} className="text-lg font-bold text-white mt-4 mb-2">
            {line.replace(/^##\s*/, '').trim()}
          </h2>
        );
        continue;
      }

      if (line.startsWith('###')) {
        // Finish any open lists
        if (inUnorderedList) {
          result.push(<ul key={`ul-${i}`} className="list-disc pl-5 mb-3">{listItems}</ul>);
          listItems = [];
          inUnorderedList = false;
        }
        if (inOrderedList) {
          result.push(<ol key={`ol-${i}`} className="list-decimal pl-5 mb-3">{listItems}</ol>);
          listItems = [];
          inOrderedList = false;
        }

        result.push(
          <h3 key={`h3-${i}`} className="text-md font-bold text-gray-300 mt-3 mb-1">
            {line.replace(/^###\s*/, '').trim()}
          </h3>
        );
        continue;
      }

      // Unordered Lists
      if (line.trim().startsWith('*') || line.trim().startsWith('•')) {
        const listItemContent = line.replace(/^\s*[*•]\s*/, '').trim();

        // If we're not already in an unordered list, start one
        if (!inUnorderedList) {
          // Finish any ordered list first
          if (inOrderedList) {
            result.push(<ol key={`ol-${i}`} className="list-decimal pl-5 mb-3">{listItems}</ol>);
            listItems = [];
            inOrderedList = false;
          }
          inUnorderedList = true;
        }

        listItems.push(
          <li key={`li-${i}`} className="text-gray-300 my-1">
            {listItemContent}
          </li>
        );
        continue;
      }

      // Ordered Lists
      if (/^\s*\d+\./.test(line)) {
        const listItemContent = line.replace(/^\s*\d+\.\s*/, '').trim();

        // If we're not already in an ordered list, start one
        if (!inOrderedList) {
          // Finish any unordered list first
          if (inUnorderedList) {
            result.push(<ul key={`ul-${i}`} className="list-disc pl-5 mb-3">{listItems}</ul>);
            listItems = [];
            inUnorderedList = false;
          }
          inOrderedList = true;
        }

        listItems.push(
          <li key={`li-${i}`} className="text-gray-300 my-1">
            {listItemContent}
          </li>
        );
        continue;
      }

      // If we reach here and we're in a list, but the current line is not a list item,
      // we need to close the list
      if ((inUnorderedList || inOrderedList) && line.trim() !== '') {
        if (inUnorderedList) {
          result.push(<ul key={`ul-${i}`} className="list-disc pl-5 mb-3">{listItems}</ul>);
          listItems = [];
          inUnorderedList = false;
        }
        if (inOrderedList) {
          result.push(<ol key={`ol-${i}`} className="list-decimal pl-5 mb-3">{listItems}</ol>);
          listItems = [];
          inOrderedList = false;
        }
      }

      // Empty lines
      if (line.trim() === '') {
        // Don't add breaks inside lists
        if (!inUnorderedList && !inOrderedList) {
          result.push(<br key={`br-${i}`} />);
        }
        continue;
      }

      // Bold text
      if (line.includes('**')) {
        const parts = line.split(/\*\*/);
        const formattedLine = [];

        for (let j = 0; j < parts.length; j++) {
          if (j % 2 === 0) {
            // Regular text
            formattedLine.push(<span key={`span-${i}-${j}`}>{parts[j]}</span>);
          } else {
            // Bold text
            formattedLine.push(<strong key={`strong-${i}-${j}`} className="font-bold">{parts[j]}</strong>);
          }
        }

        result.push(
          <p key={`p-${i}`} className="text-gray-300 mb-2">
            {formattedLine}
          </p>
        );
        continue;
      }

      // Check for table rows (lines with | characters)
      if (line.includes('|') && line.trim().startsWith('|') && line.trim().endsWith('|')) {
        if (!inTable) {
          // Start a new table
          inTable = true;
          tableRows = [];
        }

        // Process the table row
        const cells = line.split('|').filter(cell => cell.trim() !== '');
        const isHeader = cells.some(cell => cell.includes('---'));

        if (!isHeader) {
          tableRows.push(
            <tr key={`tr-${i}`} className="border-b border-gray-700">
              {cells.map((cell, cellIndex) => (
                <td key={`td-${i}-${cellIndex}`} className="py-1 px-2 text-gray-300">
                  {cell.trim()}
                </td>
              ))}
            </tr>
          );
        }
        continue;
      } else if (inTable) {
        // End of table
        result.push(
          <div key={`table-${i}`} className="overflow-x-auto my-3">
            <table className="min-w-full bg-gray-800 border border-gray-700 rounded">
              <tbody>
                {tableRows}
              </tbody>
            </table>
          </div>
        );
        inTable = false;
        tableRows = [];
      }

      // Check for markdown links [text](url)
      const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
      if (linkRegex.test(line)) {
        // Reset regex state
        linkRegex.lastIndex = 0;

        // Split the line by links
        let lastIndex = 0;
        const parts = [];
        let match;

        while ((match = linkRegex.exec(line)) !== null) {
          // Add text before the link
          if (match.index > lastIndex) {
            parts.push(<span key={`text-${i}-${lastIndex}`}>{line.substring(lastIndex, match.index)}</span>);
          }

          // Add the link
          parts.push(
            <a
              key={`link-${i}-${match.index}`}
              href={match[2]}
              target="_blank"
              rel="noopener noreferrer"
              className="text-[#88cc14] hover:underline"
            >
              {match[1]}
            </a>
          );

          lastIndex = match.index + match[0].length;
        }

        // Add any remaining text
        if (lastIndex < line.length) {
          parts.push(<span key={`text-${i}-${lastIndex}`}>{line.substring(lastIndex)}</span>);
        }

        result.push(
          <p key={`p-${i}`} className="text-gray-300 mb-2">
            {parts}
          </p>
        );
      } else {
        // Regular text without links
        result.push(
          <p key={`p-${i}`} className="text-gray-300 mb-2">
            {line}
          </p>
        );
      }
    }

    // Close any open elements at the end
    if (inUnorderedList) {
      result.push(<ul key="ul-final" className="list-disc pl-5 mb-3">{listItems}</ul>);
    }
    if (inOrderedList) {
      result.push(<ol key="ol-final" className="list-decimal pl-5 mb-3">{listItems}</ol>);
    }
    if (inTable) {
      result.push(
        <div key="table-final" className="overflow-x-auto my-3">
          <table className="min-w-full bg-gray-800 border border-gray-700 rounded">
            <tbody>
              {tableRows}
            </tbody>
          </table>
        </div>
      );
    }

    return result;
  };

  const handleFeedback = async (message, feedbackType) => {
    if (message.type === 'ai' && !message.feedback) {
      try {
        const success = await handleResponseFeedback(
          message.content,
          feedbackType,
          message.category
        );

        if (success) {
          message.feedback = feedbackType;
          // Force re-render
          const messageIndex = messages.findIndex(m => m === message);
          if (messageIndex !== -1) {
            messages[messageIndex] = { ...message };
          }
        }
      } catch (error) {
        console.error('Error handling feedback:', error);
      }
    }
  };

  // Only log in development mode
  if (import.meta.env.DEV) {
    console.log('Rendering messages:', messages.length);
  }

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4">
      {messages.length === 0 && (
        <div className="space-y-4">
          <div className="flex items-start gap-2">
            <div className="w-8 h-8 rounded-full bg-[#88cc14]/10 flex items-center justify-center flex-shrink-0">
              <XCerberusIcon size={20} />
            </div>
            <div className="bg-gray-800 text-gray-200 rounded-lg p-4 max-w-[80%]">
              <div className="whitespace-pre-wrap">
                <h2 className="text-lg font-bold text-white mb-2">Welcome to XCerberus AI</h2>
                <p className="text-gray-300 mb-2">I'm your intelligent cybersecurity assistant, powered by Google's Gemini AI. Ask me anything about:</p>
                <ul className="list-disc pl-5 mb-3">
                  <li className="text-gray-300 my-1">Security technologies (SIEM, EDR, SOC, Firewalls)</li>
                  <li className="text-gray-300 my-1">Attack techniques (XSS, SQL Injection, Buffer Overflow)</li>
                  <li className="text-gray-300 my-1">Security concepts (Zero Trust, Threat Hunting, Cloud Security)</li>
                  <li className="text-gray-300 my-1">Security practices (Penetration Testing, Incident Response, Vulnerability Management)</li>
                </ul>
                <p className="text-gray-300 mb-2">I can provide detailed explanations, technical insights, and practical advice on cybersecurity topics.</p>
                <p className="text-gray-300">How can I assist with your cybersecurity questions today?</p>
              </div>
            </div>
          </div>
        </div>
      )}
      {messages.map((message, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} items-start gap-2`}
        >
          {message.type === 'ai' && (
            <div className="w-8 h-8 rounded-full bg-[#88cc14]/10 flex items-center justify-center flex-shrink-0">
              {getMessageIcon(message.type, message.category)}
            </div>
          )}
          <div
            className={`max-w-[80%] rounded-lg p-4 ${
              message.type === 'user'
                ? 'bg-[#88cc14] text-black ml-auto font-medium'
                : 'bg-gray-800 text-gray-200'
            }`}
          >
            {message.type === 'ai' && message.thinking ? (
              <div className="flex items-center gap-2 text-gray-400">
                <FaSpinner className="animate-spin" />
                <span>Generating response...</span>
              </div>
            ) : (
              <div className="whitespace-pre-wrap">
                {message.type === 'ai' ? formatContent(message.content) : (
                  <div className="font-medium">{message.content}</div>
                )}
              </div>
            )}

            <div className="flex items-center justify-between mt-3 pt-2 border-t border-gray-700">
              <div className={`text-xs ${
                message.type === 'user' ? 'text-black/60' : 'text-gray-500'
              }`}>
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                {message.cached && ' • cached'}
                {message.type === 'ai' && message.source && message.source === 'error' && ' • Error'}
              </div>

              {message.type === 'ai' && !message.thinking && (
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => handleFeedback(message, FEEDBACK_TYPES.HEART)}
                    className={`transition-colors ${
                      message.feedback === FEEDBACK_TYPES.HEART
                        ? 'text-red-500'
                        : 'text-gray-400 hover:text-red-500'
                    }`}
                    title="Love this response"
                  >
                    <FaHeart />
                  </button>
                  <button
                    onClick={() => handleFeedback(message, FEEDBACK_TYPES.LIKE)}
                    className={`transition-colors ${
                      message.feedback === FEEDBACK_TYPES.LIKE
                        ? 'text-[#88cc14]'
                        : 'text-gray-400 hover:text-[#88cc14]'
                    }`}
                    title="Good response"
                  >
                    <FaThumbsUp />
                  </button>
                  <button
                    onClick={() => handleFeedback(message, FEEDBACK_TYPES.DISLIKE)}
                    className={`transition-colors ${
                      message.feedback === FEEDBACK_TYPES.DISLIKE
                        ? 'text-gray-600'
                        : 'text-gray-400 hover:text-gray-600'
                    }`}
                    title="Not helpful"
                  >
                    <FaThumbsDown />
                  </button>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      ))}

      {isTyping && (
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-full bg-[#88cc14]/10 flex items-center justify-center">
            <XCerberusIcon size={16} />
          </div>
          <div className="bg-gray-800 rounded-lg p-4 max-w-[80%]">
            <div className="flex flex-col">
              <div className="text-gray-400 text-sm mb-1">XCerberus AI is generating a response...</div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }} />
              </div>
            </div>
          </div>
        </div>
      )}
      <div ref={messagesEndRef} />
    </div>
  );
}

export default ChatMessages;