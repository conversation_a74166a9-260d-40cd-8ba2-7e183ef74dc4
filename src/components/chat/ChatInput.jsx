import React, { useRef } from 'react';
import { FaPaperPlane, FaLightbulb, FaTimes } from 'react-icons/fa';
import { useChat } from '../../contexts/ChatContext';
import { motion } from 'framer-motion';

function ChatInput() {
  const { state, setInput, setShowSuggestions, handleSubmit } = useChat();
  const { input, isTyping } = state;
  const inputRef = useRef(null);

  const onSubmit = (e) => {
    e.preventDefault();
    if (!input.trim() || isTyping) return;

    // Only log in development mode
    if (import.meta.env.DEV) {
      console.log('Submitting input:', input);
    }

    handleSubmit(input);
  };

  // Handle Enter key press
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!input.trim() || isTyping) return;
      handleSubmit(input);
    }
  };

  // Also handle button click separately
  const handleSendClick = () => {
    if (!input.trim() || isTyping) return;

    // Only log in development mode
    if (import.meta.env.DEV) {
      console.log('Send button clicked, submitting:', input);
    }

    handleSubmit(input);
  };

  return (
    <form
      onSubmit={onSubmit}
      className="p-3 bg-[#1c1c1c] border-t border-gray-800"
    >
      <div className="flex items-center gap-2">
        <motion.span
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="text-[#88cc14] whitespace-nowrap select-none"
        >
          XCerberus@ai:~$
        </motion.span>
        <div className="flex-1 relative">
          <input
            ref={inputRef}
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            className="w-full bg-transparent border-none outline-none text-[#88cc14] min-w-0 font-mono pr-6"
            placeholder="Ask me about cybersecurity..."
            disabled={isTyping}
            spellCheck="false"
            autoComplete="off"
            autoCapitalize="off"
          />
          {input && (
            <button
              type="button"
              onClick={() => setInput('')}
              className="absolute right-0 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white transition-colors"
            >
              <FaTimes className="text-xs" />
            </button>
          )}
        </div>
        <button
          type="button"
          onClick={handleSendClick}
          disabled={!input.trim() || isTyping}
          className={`p-2 rounded-full ${!input.trim() || isTyping ? 'text-gray-600' : 'text-[#88cc14] hover:bg-gray-800'} transition-colors`}
          title="Send message"
        >
          <FaPaperPlane className="text-sm" />
        </button>
      </div>
      <div className="flex justify-between items-center mt-1">
        <div className="flex items-center gap-1 text-xs text-gray-500">
          <FaLightbulb className="text-[#88cc14] text-xs" />
          <span className="text-xs">Ask about cybersecurity topics, tools, or techniques</span>
        </div>
        <button
          type="button"
          onClick={() => setShowSuggestions(prev => !prev)}
          className="text-gray-500 hover:text-[#88cc14] transition-colors"
        >
          <FaLightbulb className="text-xs" />
        </button>
      </div>
    </form>
  );
}

export default ChatInput;