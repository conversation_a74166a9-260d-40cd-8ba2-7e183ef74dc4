import React from 'react';
import { useChat } from '../../contexts/ChatContext';
import { motion } from 'framer-motion';

function ChatSuggestions() {
  const { state, setInput } = useChat();
  const { showSuggestions, messages } = state;

  if (!showSuggestions || messages.length > 1) {
    return null;
  }

  const suggestions = [
    "What is SIEM and how does it work?",
    "Explain Zero Trust architecture benefits",
    "Compare EDR vs XDR capabilities",
    "How to implement effective Threat Hunting?",
    "SOC analyst responsibilities and skills",
    "Best practices for Incident Response",
    "Vulnerability Management lifecycle",
    "Cloud Security challenges and solutions"
  ];

  return (
    <div className="px-3 py-2 bg-gray-900 border-t border-gray-800">
      <div className="flex flex-wrap gap-1">
        {suggestions.map((suggestion, index) => (
          <motion.button
            key={index}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
            onClick={() => setInput(suggestion)}
            className="px-2 py-1 rounded-full bg-gray-800 text-xs text-gray-300 hover:bg-gray-700 transition-colors border border-gray-700"
          >
            {suggestion}
          </motion.button>
        ))}
      </div>
    </div>
  );
}

export default ChatSuggestions;