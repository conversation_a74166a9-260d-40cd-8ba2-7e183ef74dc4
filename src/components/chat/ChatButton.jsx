import React from 'react';
import { motion } from 'framer-motion';
import { useChat } from '../../contexts/ChatContext';
import XCerberusIcon from '../icons/XCerberusIcon';

function ChatButton() {
  const { toggleChat } = useChat();

  return (
    <motion.button
      onClick={toggleChat}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className="fixed bottom-4 right-4 bg-[#88cc14] text-black p-3 rounded-full shadow-lg hover:bg-[#7ab811] transition-colors z-50 group"
    >
      <XCerberusIcon size={24} />
      <div className="absolute -top-10 right-0 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
        Ask XCerberus AI
      </div>
    </motion.button>
  );
}

export default ChatButton;