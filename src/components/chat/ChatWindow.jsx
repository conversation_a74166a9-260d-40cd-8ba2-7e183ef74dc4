import React, { useRef, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaMinus, FaExpand, FaTimes, FaHistory, FaUser, FaTrash } from 'react-icons/fa';
import { useChat } from '../../contexts/ChatContext';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import ChatSuggestions from './ChatSuggestions';
import XCerberusIcon from '../icons/XCerberusIcon';

function ChatWindow() {
  const { state, dispatch, minimizeChat, maximizeChat, toggleExpand, clearMessages, setMessages } = useChat();
  const { isMinimized, isExpanded, conversationHistory } = state;
  const [showHistory, setShowHistory] = useState(false);
  const chatContainerRef = useRef(null);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (chatContainerRef.current) {
        // Adjust chat container based on window size
        if (window.innerWidth < 640 && isExpanded) {
          // On small screens, make sure the chat doesn't overflow
          chatContainerRef.current.style.maxHeight = `${window.innerHeight - 20}px`;
        }
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Call once on mount

    return () => window.removeEventListener('resize', handleResize);
  }, [isExpanded]);

  if (isMinimized) {
    return (
      <motion.button
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        onClick={maximizeChat}
        className="fixed bottom-4 right-4 bg-[#88cc14] text-black p-4 rounded-full shadow-lg hover:bg-[#7ab811] transition-colors z-50 group"
      >
        <XCerberusIcon size={28} />
        <span className="absolute -top-2 -right-2 w-4 h-4 bg-red-500 rounded-full animate-pulse"></span>
        <div className="absolute -top-10 right-0 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
          XCerberus AI
        </div>
      </motion.button>
    );
  }

  return (
    <div
      ref={chatContainerRef}
      className={`fixed ${
        isExpanded
          ? 'inset-4 md:inset-10'
          : 'bottom-4 right-4 w-80 sm:w-96 h-[450px]'
      } z-50 transition-all duration-300`}
    >
      <motion.div
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 50, opacity: 0 }}
        className="bg-gray-900 rounded-lg shadow-xl border border-gray-800 overflow-hidden w-full h-full flex flex-col"
      >
        {/* Header */}
        <div className="bg-black p-3 flex items-center justify-between border-b border-gray-800">
          <div className="flex items-center gap-2">
            <div className="w-7 h-7 flex items-center justify-center">
              <XCerberusIcon size={28} />
            </div>
            <div>
              <h3 className="font-bold text-white text-sm">XCerberus AI</h3>
              <p className="text-xs text-gray-400">Your Cybersecurity Assistant</p>
            </div>
          </div>
          <div className="flex items-center gap-1">

            <button
              onClick={() => setShowHistory(!showHistory)}
              className="text-gray-400 hover:text-white transition-colors p-1"
              title="Conversation History"
            >
              <FaHistory className="text-xs" />
            </button>
            <button
              onClick={clearMessages}
              className="text-gray-400 hover:text-white transition-colors p-1"
              title="Clear Conversation"
            >
              <FaTrash className="text-xs" />
            </button>
            <button
              onClick={minimizeChat}
              className="text-gray-400 hover:text-white transition-colors p-1"
              title="Minimize"
            >
              <FaMinus className="text-xs" />
            </button>
            <button
              onClick={toggleExpand}
              className="text-gray-400 hover:text-white transition-colors p-1"
              title={isExpanded ? "Minimize" : "Expand"}
            >
              <FaExpand className="text-xs" />
            </button>
          </div>
        </div>

        {/* Conversation History Panel */}
        {showHistory && (
          <div className="bg-gray-800 border-b border-gray-700 p-2 max-h-60 overflow-y-auto">
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-xs font-bold text-gray-400">Conversation History</h4>
              <button
                onClick={() => {
                  clearMessages();
                  dispatch({ type: 'CLEAR_ACTIVE_CONVERSATION' });
                  setShowHistory(false);
                }}
                className="text-xs text-[#88cc14] hover:text-white transition-colors px-2 py-1 rounded bg-gray-700 hover:bg-gray-600"
              >
                New Chat
              </button>
            </div>

            {conversationHistory && conversationHistory.length > 0 ? (
              <div className="space-y-2">
                {conversationHistory.map((conversation) => (
                  <div
                    key={conversation.id}
                    className="p-2 bg-gray-700 rounded cursor-pointer hover:bg-gray-600 transition-colors"
                    onClick={() => {
                      // Load this conversation
                      setMessages(conversation.messages);
                      dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: conversation.id });
                      setShowHistory(false);
                    }}
                  >
                    <div className="flex items-center gap-1 mb-1">
                      <FaUser className="text-xs text-blue-400" />
                      <span className="text-xs text-gray-300 truncate">
                        {conversation.title}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500">
                        {conversation.messages.length} messages
                      </span>
                      <span className="text-xs text-gray-500">
                        {conversation.updatedAt.toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                <p className="text-xs">No conversation history found</p>
              </div>
            )}
          </div>
        )}

        {/* Messages */}
        <ChatMessages />

        {/* Input */}
        <ChatInput />

        {/* Suggestions */}
        <ChatSuggestions />
      </motion.div>
    </div>
  );
}

export default ChatWindow;