import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaChartLine, FaUsers, FaDollarSign, FaGraduationCap, FaCertificate,
  FaLaptop, FaBook, FaCode, FaTrophy, FaCalendarAlt, FaDownload,
  FaFilter, FaSearch, FaEye, FaArrowUp, FaArrowDown, FaEquals
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import {
  LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';

/**
 * Advanced Analytics Dashboard
 * 
 * Comprehensive business intelligence dashboard with real-time metrics,
 * predictive analytics, and detailed reporting capabilities.
 */
const AdvancedAnalyticsDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState('30d');
  const [loading, setLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState({
    overview: {},
    users: {},
    content: {},
    revenue: {},
    certifications: {},
    labs: {}
  });

  // Fetch analytics data
  useEffect(() => {
    const fetchAnalytics = async () => {
      setLoading(true);
      try {
        // Simulate API calls to various analytics endpoints
        const mockData = {
          overview: {
            totalUsers: 15420,
            activeUsers: 8930,
            totalRevenue: 245680,
            completionRate: 73.5,
            growthRate: 12.3,
            churnRate: 4.2
          },
          userMetrics: {
            newUsers: [
              { date: '2024-11-01', users: 45 },
              { date: '2024-11-02', users: 52 },
              { date: '2024-11-03', users: 38 },
              { date: '2024-11-04', users: 67 },
              { date: '2024-11-05', users: 71 },
              { date: '2024-11-06', users: 59 },
              { date: '2024-11-07', users: 83 }
            ],
            usersByTier: [
              { name: 'Free', value: 8420, color: '#10B981' },
              { name: 'Premium', value: 4200, color: '#3B82F6' },
              { name: 'Business', value: 2800, color: '#8B5CF6' }
            ],
            engagement: [
              { date: '2024-11-01', sessions: 1240, duration: 28 },
              { date: '2024-11-02', sessions: 1380, duration: 32 },
              { date: '2024-11-03', sessions: 1150, duration: 25 },
              { date: '2024-11-04', sessions: 1520, duration: 35 },
              { date: '2024-11-05', sessions: 1680, duration: 38 },
              { date: '2024-11-06', sessions: 1450, duration: 30 },
              { date: '2024-11-07', sessions: 1720, duration: 42 }
            ]
          },
          contentMetrics: {
            popularContent: [
              { name: 'Web Security Basics', views: 3420, completions: 2890 },
              { name: 'SQL Injection Lab', views: 2890, completions: 2340 },
              { name: 'Network Pentesting', views: 2340, completions: 1890 },
              { name: 'XSS Prevention', views: 1980, completions: 1650 },
              { name: 'Cryptography 101', views: 1650, completions: 1320 }
            ],
            contentPerformance: [
              { date: '2024-11-01', views: 4200, completions: 3150 },
              { date: '2024-11-02', views: 4680, completions: 3510 },
              { date: '2024-11-03', views: 4320, completions: 3240 },
              { date: '2024-11-04', views: 5100, completions: 3825 },
              { date: '2024-11-05', views: 5520, completions: 4140 },
              { date: '2024-11-06', views: 5280, completions: 3960 },
              { date: '2024-11-07', views: 5940, completions: 4455 }
            ]
          },
          revenueMetrics: {
            monthlyRevenue: [
              { month: 'Jul', revenue: 18500, subscriptions: 420 },
              { month: 'Aug', revenue: 21200, subscriptions: 485 },
              { month: 'Sep', revenue: 24800, subscriptions: 560 },
              { month: 'Oct', revenue: 28300, subscriptions: 640 },
              { month: 'Nov', revenue: 32100, subscriptions: 725 },
              { month: 'Dec', revenue: 35900, subscriptions: 810 }
            ],
            revenueByTier: [
              { tier: 'Premium', revenue: 156800, percentage: 64 },
              { tier: 'Business', revenue: 88880, percentage: 36 }
            ]
          }
        };

        setAnalyticsData(mockData);
      } catch (error) {
        console.error('Error fetching analytics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [dateRange]);

  // Get trend indicator
  const getTrendIndicator = (value, isPositive = true) => {
    const color = isPositive ? 'text-green-500' : 'text-red-500';
    const Icon = value > 0 ? FaArrowUp : value < 0 ? FaArrowDown : FaEquals;
    
    return (
      <div className={`flex items-center ${color}`}>
        <Icon className="text-sm mr-1" />
        <span className="text-sm font-medium">{Math.abs(value)}%</span>
      </div>
    );
  };

  // Render overview tab
  const renderOverview = () => (
    <div className="space-y-6">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Users</p>
              <p className="text-2xl font-bold text-[#88cc14]">
                {analyticsData.overview.totalUsers?.toLocaleString()}
              </p>
              {getTrendIndicator(12.3)}
            </div>
            <FaUsers className="text-3xl text-[#88cc14]" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Monthly Revenue</p>
              <p className="text-2xl font-bold text-green-500">
                ${analyticsData.overview.totalRevenue?.toLocaleString()}
              </p>
              {getTrendIndicator(18.7)}
            </div>
            <FaDollarSign className="text-3xl text-green-500" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Completion Rate</p>
              <p className="text-2xl font-bold text-blue-500">
                {analyticsData.overview.completionRate}%
              </p>
              {getTrendIndicator(5.2)}
            </div>
            <FaGraduationCap className="text-3xl text-blue-500" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Churn Rate</p>
              <p className="text-2xl font-bold text-orange-500">
                {analyticsData.overview.churnRate}%
              </p>
              {getTrendIndicator(-2.1, false)}
            </div>
            <FaChartLine className="text-3xl text-orange-500" />
          </div>
        </motion.div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Growth Chart */}
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <h3 className="text-lg font-semibold mb-4">User Growth</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={analyticsData.userMetrics?.newUsers}>
              <CartesianGrid strokeDasharray="3 3" stroke={darkMode ? '#374151' : '#E5E7EB'} />
              <XAxis dataKey="date" stroke={darkMode ? '#9CA3AF' : '#6B7280'} />
              <YAxis stroke={darkMode ? '#9CA3AF' : '#6B7280'} />
              <Tooltip 
                contentStyle={{
                  backgroundColor: darkMode ? '#1F2937' : '#FFFFFF',
                  border: `1px solid ${darkMode ? '#374151' : '#E5E7EB'}`,
                  borderRadius: '8px'
                }}
              />
              <Area 
                type="monotone" 
                dataKey="users" 
                stroke="#88cc14" 
                fill="#88cc14" 
                fillOpacity={0.3}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* User Distribution */}
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <h3 className="text-lg font-semibold mb-4">User Distribution</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={analyticsData.userMetrics?.usersByTier}
                cx="50%"
                cy="50%"
                outerRadius={100}
                dataKey="value"
                label={({ name, percentage }) => `${name}: ${percentage}%`}
              >
                {analyticsData.userMetrics?.usersByTier?.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Revenue Chart */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h3 className="text-lg font-semibold mb-4">Revenue Trends</h3>
        <ResponsiveContainer width="100%" height={400}>
          <LineChart data={analyticsData.revenueMetrics?.monthlyRevenue}>
            <CartesianGrid strokeDasharray="3 3" stroke={darkMode ? '#374151' : '#E5E7EB'} />
            <XAxis dataKey="month" stroke={darkMode ? '#9CA3AF' : '#6B7280'} />
            <YAxis stroke={darkMode ? '#9CA3AF' : '#6B7280'} />
            <Tooltip 
              contentStyle={{
                backgroundColor: darkMode ? '#1F2937' : '#FFFFFF',
                border: `1px solid ${darkMode ? '#374151' : '#E5E7EB'}`,
                borderRadius: '8px'
              }}
            />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="revenue" 
              stroke="#10B981" 
              strokeWidth={3}
              name="Revenue ($)"
            />
            <Line 
              type="monotone" 
              dataKey="subscriptions" 
              stroke="#3B82F6" 
              strokeWidth={3}
              name="Subscriptions"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );

  // Render content analytics tab
  const renderContentAnalytics = () => (
    <div className="space-y-6">
      {/* Content Performance Chart */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h3 className="text-lg font-semibold mb-4">Content Performance</h3>
        <ResponsiveContainer width="100%" height={400}>
          <BarChart data={analyticsData.contentMetrics?.contentPerformance}>
            <CartesianGrid strokeDasharray="3 3" stroke={darkMode ? '#374151' : '#E5E7EB'} />
            <XAxis dataKey="date" stroke={darkMode ? '#9CA3AF' : '#6B7280'} />
            <YAxis stroke={darkMode ? '#9CA3AF' : '#6B7280'} />
            <Tooltip 
              contentStyle={{
                backgroundColor: darkMode ? '#1F2937' : '#FFFFFF',
                border: `1px solid ${darkMode ? '#374151' : '#E5E7EB'}`,
                borderRadius: '8px'
              }}
            />
            <Legend />
            <Bar dataKey="views" fill="#3B82F6" name="Views" />
            <Bar dataKey="completions" fill="#10B981" name="Completions" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Popular Content Table */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h3 className="text-lg font-semibold mb-4">Most Popular Content</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                <th className="text-left py-3 px-4">Content</th>
                <th className="text-left py-3 px-4">Views</th>
                <th className="text-left py-3 px-4">Completions</th>
                <th className="text-left py-3 px-4">Completion Rate</th>
              </tr>
            </thead>
            <tbody>
              {analyticsData.contentMetrics?.popularContent?.map((content, index) => (
                <tr key={index} className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                  <td className="py-3 px-4 font-medium">{content.name}</td>
                  <td className="py-3 px-4">{content.views.toLocaleString()}</td>
                  <td className="py-3 px-4">{content.completions.toLocaleString()}</td>
                  <td className="py-3 px-4">
                    <span className="text-green-500 font-medium">
                      {((content.completions / content.views) * 100).toFixed(1)}%
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  // Navigation tabs
  const tabs = [
    { id: 'overview', name: 'Overview', icon: FaChartLine },
    { id: 'users', name: 'User Analytics', icon: FaUsers },
    { id: 'content', name: 'Content Analytics', icon: FaBook },
    { id: 'revenue', name: 'Revenue Analytics', icon: FaDollarSign },
    { id: 'certifications', name: 'Certifications', icon: FaCertificate },
    { id: 'labs', name: 'Virtual Labs', icon: FaLaptop }
  ];

  if (loading) {
    return (
      <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} flex items-center justify-center`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14] mx-auto mb-4"></div>
          <p className={`${darkMode ? 'text-white' : 'text-gray-900'}`}>Loading Analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Header */}
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-b px-6 py-4`}>
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <FaChartLine className="mr-3 text-[#88cc14]" />
              Advanced Analytics Dashboard
            </h1>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Comprehensive business intelligence and performance metrics
            </p>
          </div>
          <div className="flex items-center gap-4">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className={`px-4 py-2 rounded-lg border ${
                darkMode 
                  ? 'bg-[#0B1120] border-gray-600 text-white' 
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:border-[#88cc14]`}
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
            <button className="bg-[#88cc14] text-black px-4 py-2 rounded-lg hover:bg-[#7ab512] flex items-center">
              <FaDownload className="mr-2" />
              Export Report
            </button>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className={`w-64 ${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-r min-h-screen`}>
          <nav className="p-4">
            <div className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center transition-colors ${
                    activeTab === tab.id
                      ? 'bg-[#88cc14] text-black'
                      : darkMode
                        ? 'hover:bg-[#252D4A] text-gray-300'
                        : 'hover:bg-gray-100 text-gray-700'
                  }`}
                >
                  <tab.icon className="mr-3" />
                  {tab.name}
                </button>
              ))}
            </div>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'content' && renderContentAnalytics()}
          {activeTab === 'users' && (
            <div className="text-center py-12">
              <FaUsers className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">User Analytics</h3>
              <p className="text-gray-500 mb-4">Detailed user behavior and engagement analytics</p>
            </div>
          )}
          {activeTab === 'revenue' && (
            <div className="text-center py-12">
              <FaDollarSign className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Revenue Analytics</h3>
              <p className="text-gray-500 mb-4">Financial performance and subscription metrics</p>
            </div>
          )}
          {activeTab === 'certifications' && (
            <div className="text-center py-12">
              <FaCertificate className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Certification Analytics</h3>
              <p className="text-gray-500 mb-4">Certification program performance and trends</p>
            </div>
          )}
          {activeTab === 'labs' && (
            <div className="text-center py-12">
              <FaLaptop className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Virtual Labs Analytics</h3>
              <p className="text-gray-500 mb-4">Lab usage, performance, and resource metrics</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdvancedAnalyticsDashboard;
