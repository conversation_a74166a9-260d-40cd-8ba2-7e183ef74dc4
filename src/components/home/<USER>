import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>ck, FaCrown, FaRocket, FaUsers, FaServer, FaShieldAlt } from 'react-icons/fa';

const PricingSection = () => {
  return (
    <section className="py-16 md:py-24 px-5 bg-gray-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-40 left-10 w-64 h-64 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-10 w-80 h-80 bg-primary/5 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto relative z-10">
        <div className="text-center mb-14">
          <h2 className="section-title text-3xl md:text-4xl font-bold mb-6 text-gray-900">Choose Your Plan</h2>
          <p className="section-description text-gray-600 max-w-2xl mx-auto">
            Select the perfect plan to accelerate your cybersecurity journey
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-7xl mx-auto">
          {/* Free Plan */}
          <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-100 relative hover:border-primary hover:shadow-lg hover:shadow-primary/10 transition-all duration-300">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Free</h3>
            <p className="text-gray-600 mb-6">Best for individuals who want to start learning</p>
            
            <div className="mb-6">
              <span className="text-4xl font-bold text-gray-900">₹0</span>
              <span className="text-gray-500 ml-2">/month</span>
            </div>

            <button 
              className="w-full bg-gray-900 text-white font-bold py-3 px-4 rounded-lg hover:bg-gray-800 transition-colors mb-8"
            >
              Get Started
            </button>

            <div className="space-y-4">
              <div className="flex items-start">
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0">
                  <FaCheck className="text-primary text-xs" />
                </div>
                <span className="text-gray-600">5 Basic Challenges</span>
              </div>
              <div className="flex items-start">
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0">
                  <FaCheck className="text-primary text-xs" />
                </div>
                <span className="text-gray-600">10 Guided Labs</span>
              </div>
              <div className="flex items-start">
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0">
                  <FaCheck className="text-primary text-xs" />
                </div>
                <span className="text-gray-600">Community Forum Access</span>
              </div>
              <div className="flex items-start">
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0">
                  <FaCheck className="text-primary text-xs" />
                </div>
                <span className="text-gray-600">Basic XCerberus AI Assistant</span>
              </div>
            </div>
          </div>

          {/* Premium Plan */}
          <div className="bg-white rounded-2xl shadow-lg p-8 border-2 border-primary relative transform scale-105 hover:shadow-xl hover:shadow-primary/20 transition-all duration-300">
            <div className="absolute top-0 right-0 bg-primary text-black text-sm font-bold px-4 py-1 rounded-bl-lg rounded-tr-xl">
              POPULAR
            </div>
            
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Premium</h3>
            <p className="text-gray-600 mb-6">For dedicated cybersecurity enthusiasts</p>
            
            <div className="mb-6">
              <span className="text-4xl font-bold text-gray-900">₹399</span>
              <span className="text-gray-500 ml-2">/month</span>
            </div>

            <button 
              className="w-full bg-primary text-black font-bold py-3 px-4 rounded-lg hover:bg-primary-hover transition-colors mb-8"
            >
              Get Premium
            </button>

            <div className="space-y-4">
              <div className="flex items-start">
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0">
                  <FaCheck className="text-primary text-xs" />
                </div>
                <span className="text-gray-600">Everything in Free</span>
              </div>
              <div className="flex items-start">
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0">
                  <FaCheck className="text-primary text-xs" />
                </div>
                <span className="text-gray-600">Full access to learning paths</span>
              </div>
              <div className="flex items-start">
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0">
                  <FaCheck className="text-primary text-xs" />
                </div>
                <span className="text-gray-600">Unlimited Attack Box & Kali</span>
              </div>
              <div className="flex items-start">
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0">
                  <FaCheck className="text-primary text-xs" />
                </div>
                <span className="text-gray-600">Private OpenVPN servers</span>
              </div>
              <div className="flex items-start">
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0">
                  <FaCheck className="text-primary text-xs" />
                </div>
                <span className="text-gray-600">Faster machines</span>
              </div>
            </div>
          </div>

          {/* Business Plan */}
          <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-100 relative hover:border-primary hover:shadow-lg hover:shadow-primary/10 transition-all duration-300">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Business</h3>
            <p className="text-gray-600 mb-6">For teams and organizations</p>
            
            <div className="mb-6">
              <span className="text-4xl font-bold text-gray-900">Custom</span>
              <span className="text-gray-500 ml-2">pricing</span>
            </div>

            <button 
              className="w-full bg-gray-900 text-white font-bold py-3 px-4 rounded-lg hover:bg-gray-800 transition-colors mb-8"
            >
              Contact Sales
            </button>

            <div className="space-y-4">
              <div className="flex items-start">
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0">
                  <FaCheck className="text-primary text-xs" />
                </div>
                <span className="text-gray-600">Everything in Premium</span>
              </div>
              <div className="flex items-start">
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0">
                  <FaCheck className="text-primary text-xs" />
                </div>
                <span className="text-gray-600">Custom learning paths</span>
              </div>
              <div className="flex items-start">
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0">
                  <FaCheck className="text-primary text-xs" />
                </div>
                <span className="text-gray-600">Advanced reporting</span>
              </div>
              <div className="flex items-start">
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0">
                  <FaCheck className="text-primary text-xs" />
                </div>
                <span className="text-gray-600">Dedicated support</span>
              </div>
              <div className="flex items-start">
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0">
                  <FaCheck className="text-primary text-xs" />
                </div>
                <span className="text-gray-600">Custom integrations</span>
              </div>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-20">
          <h3 className="text-2xl font-bold text-center mb-10 text-white bg-black py-4 rounded-lg shadow-lg">Frequently Asked Questions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            <div className="bg-black text-white p-6 rounded-lg shadow-md hover:shadow-primary/20 hover:border-primary border border-gray-800 transition-all duration-300">
              <h4 className="font-bold mb-2 text-primary">What makes XCerberus different?</h4>
              <p className="text-gray-300">XCerberus offers a unique blend of guided learning, hands-on labs, and AI-assisted training that adapts to your skill level and learning pace.</p>
            </div>
            <div className="bg-black text-white p-6 rounded-lg shadow-md hover:shadow-primary/20 hover:border-primary border border-gray-800 transition-all duration-300">
              <h4 className="font-bold mb-2 text-primary">Can I switch plans later?</h4>
              <p className="text-gray-300">Yes, you can upgrade or downgrade your plan at any time. Upgrades take effect immediately.</p>
            </div>
            <div className="bg-black text-white p-6 rounded-lg shadow-md hover:shadow-primary/20 hover:border-primary border border-gray-800 transition-all duration-300">
              <h4 className="font-bold mb-2 text-primary">Do you offer refunds?</h4>
              <p className="text-gray-300">Yes, we offer a 30-day money-back guarantee for our Premium plan if you're not satisfied.</p>
            </div>
            <div className="bg-black text-white p-6 rounded-lg shadow-md hover:shadow-primary/20 hover:border-primary border border-gray-800 transition-all duration-300">
              <h4 className="font-bold mb-2 text-primary">What payment methods do you accept?</h4>
              <p className="text-gray-300">We accept all major credit cards, PayPal, and bank transfers for business plans.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;