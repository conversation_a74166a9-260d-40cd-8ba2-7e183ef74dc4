import React from 'react';
import { motion } from 'framer-motion';
import { FaTerminal, FaShieldVirus, FaServer } from 'react-icons/fa';

const features = [
  {
    icon: FaTerminal,
    title: "Real-world Scenarios",
    description: "Practice on simulated environments that mirror actual cyber attacks"
  },
  {
    icon: FaShieldVirus,
    title: "Defense Techniques",
    description: "Learn to protect systems by understanding how they're attacked"
  },
  {
    icon: FaServer,
    title: "Live Machines",
    description: "Access vulnerable systems ready for exploitation and practice"
  }
];

const FeaturesSection = () => {
  return (
    <section className="py-16 md:py-24 px-5">
      <div className="container mx-auto">
        <div className="text-center mb-14">
          <h2 className="section-title text-3xl md:text-4xl font-bold mb-6 text-gray-900">Why Choose Us</h2>
          <p className="section-description text-gray-600 max-w-2xl mx-auto">
            Learn cybersecurity through immersive, hands-on experiences
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 section-gap">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="feature-card p-6 md:p-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ 
                scale: 1.05,
                boxShadow: '0 10px 25px rgba(45, 212, 191, 0.3)',
                borderColor: 'rgb(45, 212, 191)'
              }}
            >
              <div className="relative">
                <div className="absolute -top-1 -left-1 w-12 h-12 bg-primary/10 rounded-lg transform rotate-12 opacity-70"></div>
                <feature.icon className="text-4xl text-primary mb-5 relative z-10" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;