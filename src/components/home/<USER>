import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { FaUserNinja, FaFlag, FaLaptopCode, FaUserSecret, FaShieldAlt, FaGlobe } from 'react-icons/fa';
import InteractiveTerminal from '../InteractiveTerminal';

const stats = [
  { icon: FaUserNinja, value: "1000+", label: "Active Labs" },
  { icon: FaFlag, value: "24/7", label: "Support" },
  { icon: FaLaptopCode, value: "500+", label: "Tutorials" },
  { icon: FaUserSecret, value: "100%", label: "Secure" }
];

const HeroSection = ({ onShowDemo }) => {
  const navigate = useNavigate();

  return (
    <section className="pt-24 lg:pt-32 pb-16 md:pb-24 px-5">
      <div className="container mx-auto">
        <div className="flex flex-col lg:flex-row gap-12 md:gap-16 items-center mb-16">
          {/* Main Content - Always First on Mobile */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="text-center sm:text-left w-full lg:w-1/2 lg:order-1 order-1"
            style={{ order: 1 }}
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-bold mb-8">
              <FaShieldAlt className="mr-2" />
              #1 Cybersecurity Training Platform
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-8 leading-tight text-gray-900 dark:text-white hero-title">
              <div className="flex flex-col sm:flex-row sm:items-center justify-center sm:justify-start gap-2">
                <span>Master</span>
                <span className="text-primary">Cybersecurity</span>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center justify-center sm:justify-start gap-2">
                <span>Through</span>
                <span className="text-primary">Real-World Challenges</span>
              </div>
            </h1>
            <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 mb-10 max-w-2xl mx-auto sm:mx-0 hero-description">
              Join thousands of security professionals already training on XCerberus.
              Learn, practice, and compete in a safe environment.
            </p>
            <div className="flex flex-wrap gap-5 justify-center sm:justify-start">
              <button
                onClick={() => navigate('/learn')}
                className="bg-primary text-black font-bold py-3 px-8 rounded-lg hover:bg-primary-hover transition-colors"
              >
                Start Learning
              </button>
              <button
                onClick={() => navigate('/challenges')}
                className="border-2 border-primary text-primary dark:text-primary font-bold py-3 px-8 rounded-lg hover:bg-primary/10 transition-colors"
              >
                Explore Challenges
              </button>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mt-14">
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{
                    scale: 1.05,
                    boxShadow: '0 0 20px rgba(45, 212, 191, 0.3)'
                  }}
                  className="stat-card text-center p-4 rounded-lg bg-white border border-gray-200 hover:border-primary transition-all duration-300 relative overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-transparent to-primary/5 opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative z-10">
                    <div className="w-12 h-12 mx-auto mb-2 bg-primary/10 rounded-full flex items-center justify-center transform transition-transform duration-300 hover:scale-110">
                      <stat.icon className="text-primary text-xl" />
                    </div>
                    <div className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</div>
                    <div className="text-sm text-gray-600">{stat.label}</div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Interactive Terminal - Second on Mobile */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="w-full lg:w-1/2 lg:order-2 order-2"
            style={{ order: 2 }}
          >
            <div className="bg-gray-900 dark:bg-gray-800 rounded-lg shadow-2xl overflow-hidden">
              <div className="bg-gray-800 dark:bg-gray-900 px-4 py-2 flex items-center justify-between">
                <div className="flex items-center">
                  <FaLaptopCode className="text-primary mr-2" />
                  <div className="text-gray-200 text-sm font-medium">Interactive Terminal</div>
                </div>
                <div className="flex space-x-2">
                  <div className="h-3 w-3 rounded-full bg-red-500 mr-1"></div>
                  <div className="h-3 w-3 rounded-full bg-yellow-500 mr-1"></div>
                  <div className="h-3 w-3 rounded-full bg-green-500 mr-1"></div>
                </div>
              </div>
              <div className="p-0">
                <InteractiveTerminal height="500px" />
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;