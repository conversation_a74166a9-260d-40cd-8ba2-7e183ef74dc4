import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaPlus, FaEdit, FaEye, FaTrash, FaUpload, FaFilter, FaSearch,
  FaBook, FaVideo, FaCode, FaQuestionCircle, FaChartLine,
  FaUsers, FaClock, FaStar, FaTag, FaGlobe
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import contentManagementService from '../../services/contentManagementService';

/**
 * Content Management Dashboard
 * 
 * Comprehensive dashboard for managing dynamic learning materials.
 * Includes content creation, editing, analytics, and media management.
 */
const ContentManagementDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('overview');
  const [content, setContent] = useState([]);
  const [categories, setCategories] = useState([]);
  const [contentTypes, setContentTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedType, setSelectedType] = useState('');

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [contentResult, categoriesResult, typesResult] = await Promise.all([
          contentManagementService.getContent({ published: false }),
          contentManagementService.getCategories(),
          contentManagementService.getContentTypes()
        ]);

        if (contentResult.success) setContent(contentResult.content);
        if (categoriesResult.success) setCategories(categoriesResult.categories);
        if (typesResult.success) setContentTypes(typesResult.contentTypes);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Filter content based on search and filters
  const filteredContent = content.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.excerpt?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !selectedCategory || item.category_id === selectedCategory;
    const matchesType = !selectedType || item.content_type_id === selectedType;
    
    return matchesSearch && matchesCategory && matchesType;
  });

  // Get content type icon
  const getContentTypeIcon = (typeName) => {
    switch (typeName) {
      case 'lesson': return <FaBook className="text-blue-500" />;
      case 'video': return <FaVideo className="text-red-500" />;
      case 'interactive': return <FaCode className="text-green-500" />;
      case 'quiz': return <FaQuestionCircle className="text-purple-500" />;
      default: return <FaBook className="text-gray-500" />;
    }
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-500/20 text-green-500';
      case 'intermediate': return 'bg-yellow-500/20 text-yellow-500';
      case 'advanced': return 'bg-red-500/20 text-red-500';
      case 'expert': return 'bg-purple-500/20 text-purple-500';
      default: return 'bg-gray-500/20 text-gray-500';
    }
  };

  // Render overview tab
  const renderOverview = () => (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Content</p>
              <p className="text-2xl font-bold text-[#88cc14]">{content.length}</p>
            </div>
            <FaBook className="text-3xl text-[#88cc14]" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Published</p>
              <p className="text-2xl font-bold text-blue-500">
                {content.filter(item => item.is_published).length}
              </p>
            </div>
            <FaGlobe className="text-3xl text-blue-500" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Views</p>
              <p className="text-2xl font-bold text-green-500">
                {content.reduce((sum, item) => sum + (item.view_count || 0), 0).toLocaleString()}
              </p>
            </div>
            <FaEye className="text-3xl text-green-500" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Avg Rating</p>
              <p className="text-2xl font-bold text-yellow-500">
                {(content.reduce((sum, item) => sum + (item.average_rating || 0), 0) / content.length || 0).toFixed(1)}
              </p>
            </div>
            <FaStar className="text-3xl text-yellow-500" />
          </div>
        </motion.div>
      </div>

      {/* Quick Actions */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            onClick={() => setActiveTab('create')}
            className="p-4 bg-[#88cc14] text-black rounded-lg hover:bg-[#7ab512] flex flex-col items-center gap-2"
          >
            <FaPlus className="text-xl" />
            <span className="text-sm font-medium">Create Content</span>
          </button>
          
          <button
            onClick={() => setActiveTab('media')}
            className="p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex flex-col items-center gap-2"
          >
            <FaUpload className="text-xl" />
            <span className="text-sm font-medium">Upload Media</span>
          </button>
          
          <button
            onClick={() => setActiveTab('analytics')}
            className="p-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 flex flex-col items-center gap-2"
          >
            <FaChartLine className="text-xl" />
            <span className="text-sm font-medium">View Analytics</span>
          </button>
          
          <button
            onClick={() => setActiveTab('content')}
            className="p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 flex flex-col items-center gap-2"
          >
            <FaEdit className="text-xl" />
            <span className="text-sm font-medium">Manage Content</span>
          </button>
        </div>
      </div>
    </div>
  );

  // Render content management tab
  const renderContentManagement = () => (
    <div className="space-y-6">
      {/* Filters and Search */}
      <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
            <input
              type="text"
              placeholder="Search content..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
                darkMode 
                  ? 'bg-[#0B1120] border-gray-600 text-white' 
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:border-[#88cc14]`}
            />
          </div>
          
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className={`px-4 py-2 rounded-lg border ${
              darkMode 
                ? 'bg-[#0B1120] border-gray-600 text-white' 
                : 'bg-white border-gray-300 text-gray-900'
            } focus:outline-none focus:border-[#88cc14]`}
          >
            <option value="">All Categories</option>
            {categories.map(category => (
              <option key={category.id} value={category.id}>{category.name}</option>
            ))}
          </select>
          
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className={`px-4 py-2 rounded-lg border ${
              darkMode 
                ? 'bg-[#0B1120] border-gray-600 text-white' 
                : 'bg-white border-gray-300 text-gray-900'
            } focus:outline-none focus:border-[#88cc14]`}
          >
            <option value="">All Types</option>
            {contentTypes.map(type => (
              <option key={type.id} value={type.id}>{type.display_name}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Content List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredContent.map(item => (
          <motion.div
            key={item.id}
            whileHover={{ scale: 1.02 }}
            className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-2">
                {getContentTypeIcon(item.content_types?.name)}
                <span className={`px-2 py-1 rounded text-xs ${getDifficultyColor(item.difficulty_level)}`}>
                  {item.difficulty_level}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {item.is_published ? (
                  <span className="px-2 py-1 bg-green-500/20 text-green-500 rounded text-xs">Published</span>
                ) : (
                  <span className="px-2 py-1 bg-yellow-500/20 text-yellow-500 rounded text-xs">Draft</span>
                )}
              </div>
            </div>
            
            <h3 className="text-lg font-semibold mb-2 line-clamp-2">{item.title}</h3>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 line-clamp-3`}>
              {item.excerpt || 'No description available'}
            </p>
            
            <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
              <div className="flex items-center gap-1">
                <FaEye />
                <span>{item.view_count || 0} views</span>
              </div>
              <div className="flex items-center gap-1">
                <FaClock />
                <span>{item.estimated_duration || 0} min</span>
              </div>
              <div className="flex items-center gap-1">
                <FaStar />
                <span>{item.average_rating || 0}</span>
              </div>
            </div>
            
            {item.tags && item.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-4">
                {item.tags.slice(0, 3).map((tag, index) => (
                  <span key={index} className="px-2 py-1 bg-gray-500/20 text-gray-500 rounded text-xs">
                    {tag}
                  </span>
                ))}
                {item.tags.length > 3 && (
                  <span className="px-2 py-1 bg-gray-500/20 text-gray-500 rounded text-xs">
                    +{item.tags.length - 3} more
                  </span>
                )}
              </div>
            )}
            
            <div className="flex gap-2">
              <button className="flex-1 bg-blue-500 text-white px-3 py-2 rounded text-sm hover:bg-blue-600">
                <FaEdit className="inline mr-1" />
                Edit
              </button>
              <button className="flex-1 bg-green-500 text-white px-3 py-2 rounded text-sm hover:bg-green-600">
                <FaEye className="inline mr-1" />
                View
              </button>
              <button className="bg-red-500 text-white px-3 py-2 rounded text-sm hover:bg-red-600">
                <FaTrash />
              </button>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  // Navigation tabs
  const tabs = [
    { id: 'overview', name: 'Overview', icon: FaChartLine },
    { id: 'content', name: 'Content Management', icon: FaBook },
    { id: 'create', name: 'Create Content', icon: FaPlus },
    { id: 'media', name: 'Media Library', icon: FaUpload },
    { id: 'analytics', name: 'Analytics', icon: FaChartLine }
  ];

  if (loading) {
    return (
      <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} flex items-center justify-center`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14] mx-auto mb-4"></div>
          <p className={`${darkMode ? 'text-white' : 'text-gray-900'}`}>Loading Content Management...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Header */}
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-b px-6 py-4`}>
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <FaBook className="mr-3 text-[#88cc14]" />
              Content Management System
            </h1>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Create, manage, and analyze dynamic learning materials
            </p>
          </div>
          <button
            onClick={() => setActiveTab('create')}
            className="bg-[#88cc14] text-black px-4 py-2 rounded-lg hover:bg-[#7ab512] flex items-center"
          >
            <FaPlus className="mr-2" />
            Create Content
          </button>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className={`w-64 ${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-r min-h-screen`}>
          <nav className="p-4">
            <div className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center transition-colors ${
                    activeTab === tab.id
                      ? 'bg-[#88cc14] text-black'
                      : darkMode
                        ? 'hover:bg-[#252D4A] text-gray-300'
                        : 'hover:bg-gray-100 text-gray-700'
                  }`}
                >
                  <tab.icon className="mr-3" />
                  {tab.name}
                </button>
              ))}
            </div>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'content' && renderContentManagement()}
          {activeTab === 'create' && (
            <div className="text-center py-12">
              <FaPlus className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Content Creation</h3>
              <p className="text-gray-500 mb-4">Rich content editor coming soon</p>
            </div>
          )}
          {activeTab === 'media' && (
            <div className="text-center py-12">
              <FaUpload className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Media Library</h3>
              <p className="text-gray-500 mb-4">Media management interface coming soon</p>
            </div>
          )}
          {activeTab === 'analytics' && (
            <div className="text-center py-12">
              <FaChartLine className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Content Analytics</h3>
              <p className="text-gray-500 mb-4">Advanced analytics dashboard coming soon</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContentManagementDashboard;
