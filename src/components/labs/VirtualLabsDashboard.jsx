import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaLaptop, FaPlay, FaStop, FaPause, FaEye, FaCode, FaTerminal,
  FaDocker, FaServer, FaNetworkWired, FaShieldAlt, FaClock,
  FaUsers, FaChartLine, FaDownload, FaUpload, FaSync, FaTrash,
  FaPlus, FaFilter, FaSearch, FaExternalLinkAlt, FaDesktop
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import virtualLabsService from '../../services/virtualLabsService';

/**
 * Virtual Labs Dashboard
 * 
 * Comprehensive dashboard for managing live virtual labs and hands-on environments.
 * Includes lab templates, active instances, and session management.
 */
const VirtualLabsDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [templates, setTemplates] = useState([]);
  const [instances, setInstances] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [launchingLab, setLaunchingLab] = useState(null);

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [templatesResult, instancesResult] = await Promise.all([
          virtualLabsService.getLabTemplates(),
          virtualLabsService.getUserInstances()
        ]);

        if (templatesResult.success) setTemplates(templatesResult.templates);
        if (instancesResult.success) setInstances(instancesResult.instances);
      } catch (error) {
        console.error('Error fetching lab data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Launch lab instance
  const handleLaunchLab = async (templateId) => {
    setLaunchingLab(templateId);
    try {
      const result = await virtualLabsService.launchLab(templateId);
      if (result.success) {
        // Refresh instances
        const instancesResult = await virtualLabsService.getUserInstances();
        if (instancesResult.success) setInstances(instancesResult.instances);
        
        // Auto-connect to lab after a short delay
        setTimeout(() => {
          handleConnectToLab(result.instance.id);
        }, 3000);
      } else {
        alert('Failed to launch lab: ' + result.error);
      }
    } catch (error) {
      console.error('Error launching lab:', error);
      alert('Failed to launch lab');
    } finally {
      setLaunchingLab(null);
    }
  };

  // Connect to lab instance
  const handleConnectToLab = async (instanceId) => {
    try {
      const result = await virtualLabsService.connectToLab(instanceId);
      if (result.success && result.connectionDetails.accessUrl) {
        // Open lab in new window
        window.open(result.connectionDetails.accessUrl, '_blank');
      } else {
        alert('Failed to connect to lab: ' + result.error);
      }
    } catch (error) {
      console.error('Error connecting to lab:', error);
      alert('Failed to connect to lab');
    }
  };

  // Stop lab instance
  const handleStopLab = async (instanceId) => {
    try {
      const result = await virtualLabsService.stopLab(instanceId);
      if (result.success) {
        // Refresh instances
        const instancesResult = await virtualLabsService.getUserInstances();
        if (instancesResult.success) setInstances(instancesResult.instances);
      } else {
        alert('Failed to stop lab: ' + result.error);
      }
    } catch (error) {
      console.error('Error stopping lab:', error);
      alert('Failed to stop lab');
    }
  };

  // Get lab type icon
  const getLabTypeIcon = (labType) => {
    switch (labType) {
      case 'docker': return <FaDocker className="text-blue-500" />;
      case 'vm': return <FaDesktop className="text-purple-500" />;
      case 'cloud': return <FaServer className="text-green-500" />;
      case 'simulation': return <FaNetworkWired className="text-orange-500" />;
      default: return <FaLaptop className="text-gray-500" />;
    }
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-500/20 text-green-500';
      case 'intermediate': return 'bg-yellow-500/20 text-yellow-500';
      case 'advanced': return 'bg-red-500/20 text-red-500';
      case 'expert': return 'bg-purple-500/20 text-purple-500';
      default: return 'bg-gray-500/20 text-gray-500';
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'running': return 'bg-green-500/20 text-green-500';
      case 'starting': return 'bg-yellow-500/20 text-yellow-500';
      case 'stopping': return 'bg-orange-500/20 text-orange-500';
      case 'stopped': return 'bg-gray-500/20 text-gray-500';
      case 'error': return 'bg-red-500/20 text-red-500';
      default: return 'bg-gray-500/20 text-gray-500';
    }
  };

  // Render overview tab
  const renderOverview = () => (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Available Labs</p>
              <p className="text-2xl font-bold text-[#88cc14]">{templates.length}</p>
            </div>
            <FaLaptop className="text-3xl text-[#88cc14]" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Active Instances</p>
              <p className="text-2xl font-bold text-blue-500">
                {instances.filter(i => i.status === 'running').length}
              </p>
            </div>
            <FaPlay className="text-3xl text-blue-500" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Sessions</p>
              <p className="text-2xl font-bold text-green-500">{instances.length}</p>
            </div>
            <FaUsers className="text-3xl text-green-500" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ Scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Avg Duration</p>
              <p className="text-2xl font-bold text-purple-500">45m</p>
            </div>
            <FaClock className="text-3xl text-purple-500" />
          </div>
        </motion.div>
      </div>

      {/* Quick Actions */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            onClick={() => setActiveTab('templates')}
            className="p-4 bg-[#88cc14] text-black rounded-lg hover:bg-[#7ab512] flex flex-col items-center gap-2"
          >
            <FaLaptop className="text-xl" />
            <span className="text-sm font-medium">Browse Labs</span>
          </button>
          
          <button
            onClick={() => setActiveTab('instances')}
            className="p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex flex-col items-center gap-2"
          >
            <FaPlay className="text-xl" />
            <span className="text-sm font-medium">My Instances</span>
          </button>
          
          <button
            onClick={() => setActiveTab('analytics')}
            className="p-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 flex flex-col items-center gap-2"
          >
            <FaChartLine className="text-xl" />
            <span className="text-sm font-medium">Analytics</span>
          </button>
          
          <button
            onClick={() => setActiveTab('templates')}
            className="p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 flex flex-col items-center gap-2"
          >
            <FaPlus className="text-xl" />
            <span className="text-sm font-medium">Create Lab</span>
          </button>
        </div>
      </div>
    </div>
  );

  // Render lab templates tab
  const renderTemplates = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {templates.map(template => (
          <motion.div
            key={template.id}
            whileHover={{ scale: 1.02 }}
            className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-2">
                {getLabTypeIcon(template.lab_type)}
                <span className={`px-2 py-1 rounded text-xs ${getDifficultyColor(template.difficulty_level)}`}>
                  {template.difficulty_level}
                </span>
              </div>
              <span className="px-2 py-1 bg-blue-500/20 text-blue-500 rounded text-xs">
                {template.lab_type}
              </span>
            </div>
            
            <h3 className="text-lg font-semibold mb-2">{template.name}</h3>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 line-clamp-3`}>
              {template.description}
            </p>
            
            <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
              <div className="flex items-center gap-1">
                <FaClock />
                <span>{template.estimated_duration || 60} min</span>
              </div>
              <div className="flex items-center gap-1">
                <FaUsers />
                <span>{template.lab_analytics?.[0]?.total_launches || 0} launches</span>
              </div>
            </div>
            
            {template.learning_objectives && template.learning_objectives.length > 0 && (
              <div className="mb-4">
                <p className="text-sm font-medium mb-2">Learning Objectives:</p>
                <ul className="text-xs text-gray-500 space-y-1">
                  {template.learning_objectives.slice(0, 3).map((objective, index) => (
                    <li key={index}>• {objective}</li>
                  ))}
                </ul>
              </div>
            )}
            
            <div className="flex gap-2">
              <button
                onClick={() => handleLaunchLab(template.id)}
                disabled={launchingLab === template.id}
                className="flex-1 bg-[#88cc14] text-black px-3 py-2 rounded text-sm hover:bg-[#7ab512] disabled:opacity-50 flex items-center justify-center"
              >
                {launchingLab === template.id ? (
                  <>
                    <FaSync className="animate-spin mr-2" />
                    Launching...
                  </>
                ) : (
                  <>
                    <FaPlay className="mr-2" />
                    Launch Lab
                  </>
                )}
              </button>
              <button
                onClick={() => setSelectedTemplate(template)}
                className="bg-blue-500 text-white px-3 py-2 rounded text-sm hover:bg-blue-600"
              >
                <FaEye />
              </button>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  // Render instances tab
  const renderInstances = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6">
        {instances.map(instance => (
          <motion.div
            key={instance.id}
            whileHover={{ scale: 1.01 }}
            className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  {getLabTypeIcon(instance.lab_templates?.lab_type)}
                  <div>
                    <h3 className="text-lg font-semibold">{instance.lab_templates?.name}</h3>
                    <p className="text-sm text-gray-500">Instance: {instance.instance_name}</p>
                  </div>
                </div>
                <span className={`px-3 py-1 rounded text-sm ${getStatusColor(instance.status)}`}>
                  {instance.status}
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                {instance.status === 'running' && (
                  <>
                    <button
                      onClick={() => handleConnectToLab(instance.id)}
                      className="bg-[#88cc14] text-black px-4 py-2 rounded hover:bg-[#7ab512] flex items-center"
                    >
                      <FaExternalLinkAlt className="mr-2" />
                      Connect
                    </button>
                    <button
                      onClick={() => handleStopLab(instance.id)}
                      className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 flex items-center"
                    >
                      <FaStop className="mr-2" />
                      Stop
                    </button>
                  </>
                )}
                
                {instance.status === 'stopped' && (
                  <button
                    onClick={() => handleLaunchLab(instance.template_id)}
                    className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 flex items-center"
                  >
                    <FaPlay className="mr-2" />
                    Restart
                  </button>
                )}
              </div>
            </div>
            
            <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Started:</span>
                <p className="font-medium">{new Date(instance.started_at).toLocaleString()}</p>
              </div>
              <div>
                <span className="text-gray-500">Last Accessed:</span>
                <p className="font-medium">{new Date(instance.last_accessed).toLocaleString()}</p>
              </div>
              <div>
                <span className="text-gray-500">CPU Usage:</span>
                <p className="font-medium">{instance.cpu_usage || 0}%</p>
              </div>
              <div>
                <span className="text-gray-500">Memory Usage:</span>
                <p className="font-medium">{instance.memory_usage || 0}%</p>
              </div>
            </div>
            
            {instance.access_url && (
              <div className="mt-4 p-3 bg-gray-500/10 rounded">
                <p className="text-sm text-gray-500 mb-1">Access URL:</p>
                <p className="text-sm font-mono">{instance.access_url}</p>
              </div>
            )}
          </motion.div>
        ))}
        
        {instances.length === 0 && (
          <div className="text-center py-12">
            <FaLaptop className="text-4xl text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Lab Instances</h3>
            <p className="text-gray-500 mb-4">Launch your first lab to get started</p>
            <button
              onClick={() => setActiveTab('templates')}
              className="bg-[#88cc14] text-black px-6 py-3 rounded-lg hover:bg-[#7ab512]"
            >
              Browse Lab Templates
            </button>
          </div>
        )}
      </div>
    </div>
  );

  // Navigation tabs
  const tabs = [
    { id: 'overview', name: 'Overview', icon: FaChartLine },
    { id: 'templates', name: 'Lab Templates', icon: FaLaptop },
    { id: 'instances', name: 'My Instances', icon: FaPlay },
    { id: 'analytics', name: 'Analytics', icon: FaChartLine },
    { id: 'create', name: 'Create Lab', icon: FaPlus }
  ];

  if (loading) {
    return (
      <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} flex items-center justify-center`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14] mx-auto mb-4"></div>
          <p className={`${darkMode ? 'text-white' : 'text-gray-900'}`}>Loading Virtual Labs...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Header */}
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-b px-6 py-4`}>
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <FaLaptop className="mr-3 text-[#88cc14]" />
              Virtual Labs Dashboard
            </h1>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Launch and manage live virtual environments for hands-on learning
            </p>
          </div>
          <button
            onClick={() => setActiveTab('templates')}
            className="bg-[#88cc14] text-black px-4 py-2 rounded-lg hover:bg-[#7ab512] flex items-center"
          >
            <FaPlay className="mr-2" />
            Launch Lab
          </button>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className={`w-64 ${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-r min-h-screen`}>
          <nav className="p-4">
            <div className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center transition-colors ${
                    activeTab === tab.id
                      ? 'bg-[#88cc14] text-black'
                      : darkMode
                        ? 'hover:bg-[#252D4A] text-gray-300'
                        : 'hover:bg-gray-100 text-gray-700'
                  }`}
                >
                  <tab.icon className="mr-3" />
                  {tab.name}
                </button>
              ))}
            </div>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'templates' && renderTemplates()}
          {activeTab === 'instances' && renderInstances()}
          {activeTab === 'analytics' && (
            <div className="text-center py-12">
              <FaChartLine className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Lab Analytics</h3>
              <p className="text-gray-500 mb-4">Usage statistics and performance metrics</p>
            </div>
          )}
          {activeTab === 'create' && (
            <div className="text-center py-12">
              <FaPlus className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Create Lab Template</h3>
              <p className="text-gray-500 mb-4">Lab creation interface coming soon</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VirtualLabsDashboard;
