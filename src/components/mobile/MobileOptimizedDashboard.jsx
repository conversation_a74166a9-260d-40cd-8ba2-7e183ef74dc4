import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaHome, FaBook, FaLaptop, FaCertificate, FaChartLine, FaUser,
  FaBars, FaTimes, FaSearch, FaBell, FaPlus, FaPlay, FaEye,
  FaGraduationCap, FaTrophy, FaCode, FaShieldAlt, FaRocket
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { Link, useLocation } from 'react-router-dom';

/**
 * Mobile Optimized Dashboard
 * 
 * Touch-friendly, responsive dashboard optimized for mobile devices
 * with swipe gestures, bottom navigation, and mobile-first design.
 */
const MobileOptimizedDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const { user } = useAuth();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('home');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [notifications, setNotifications] = useState(3);

  // Detect mobile device
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Quick access items optimized for mobile
  const quickAccessItems = [
    {
      id: 'content',
      title: 'Learn',
      subtitle: 'Browse content',
      icon: FaBook,
      color: 'bg-blue-500',
      path: '/content-management',
      count: '24 courses'
    },
    {
      id: 'labs',
      title: 'Practice',
      subtitle: 'Virtual labs',
      icon: FaLaptop,
      color: 'bg-green-500',
      path: '/virtual-labs',
      count: '12 labs'
    },
    {
      id: 'assessments',
      title: 'Test',
      subtitle: 'Take quiz',
      icon: FaCode,
      color: 'bg-purple-500',
      path: '/assessments',
      count: '8 available'
    },
    {
      id: 'certs',
      title: 'Certify',
      subtitle: 'Get certified',
      icon: FaCertificate,
      color: 'bg-yellow-500',
      path: '/certifications',
      count: '5 programs'
    }
  ];

  // Recent activity for mobile view
  const recentActivity = [
    {
      id: 1,
      type: 'course',
      title: 'Web Security Basics',
      progress: 75,
      timeAgo: '2 hours ago',
      icon: FaBook
    },
    {
      id: 2,
      type: 'lab',
      title: 'SQL Injection Lab',
      progress: 100,
      timeAgo: '1 day ago',
      icon: FaLaptop
    },
    {
      id: 3,
      type: 'assessment',
      title: 'Network Security Quiz',
      progress: 85,
      timeAgo: '3 days ago',
      icon: FaCode
    }
  ];

  // Bottom navigation items
  const bottomNavItems = [
    { id: 'home', icon: FaHome, label: 'Home', path: '/' },
    { id: 'learn', icon: FaBook, label: 'Learn', path: '/content-management' },
    { id: 'practice', icon: FaLaptop, label: 'Practice', path: '/virtual-labs' },
    { id: 'progress', icon: FaChartLine, label: 'Progress', path: '/analytics-dashboard' },
    { id: 'profile', icon: FaUser, label: 'Profile', path: '/profile' }
  ];

  // Swipe gesture handlers
  const handleTouchStart = (e) => {
    const touch = e.touches[0];
    setTouchStart({ x: touch.clientX, y: touch.clientY });
  };

  const handleTouchEnd = (e) => {
    if (!touchStart) return;
    
    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchStart.x;
    const deltaY = touch.clientY - touchStart.y;
    
    // Horizontal swipe detection
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
      if (deltaX > 0) {
        // Swipe right - open menu
        setIsMenuOpen(true);
      } else {
        // Swipe left - close menu
        setIsMenuOpen(false);
      }
    }
    
    setTouchStart(null);
  };

  const [touchStart, setTouchStart] = useState(null);

  // Render mobile header
  const renderMobileHeader = () => (
    <div className={`sticky top-0 z-40 ${darkMode ? 'bg-[#0B1120]' : 'bg-white'} border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} px-4 py-3`}>
      <div className="flex items-center justify-between">
        <button
          onClick={() => setIsMenuOpen(true)}
          className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <FaBars className="text-xl" />
        </button>
        
        <div className="flex-1 mx-4">
          <div className="relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search courses, labs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
                darkMode 
                  ? 'bg-[#1A1F35] border-gray-600 text-white placeholder-gray-400'
                  : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'
              } focus:outline-none focus:border-[#88cc14]`}
            />
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button className="relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800">
            <FaBell className="text-xl" />
            {notifications > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {notifications}
              </span>
            )}
          </button>
          
          <div className="w-8 h-8 rounded-full bg-[#88cc14] flex items-center justify-center">
            <span className="text-black font-bold text-sm">
              {user?.user_metadata?.full_name?.charAt(0) || 'U'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  // Render quick access grid
  const renderQuickAccess = () => (
    <div className="px-4 py-6">
      <h2 className="text-lg font-bold mb-4">Quick Access</h2>
      <div className="grid grid-cols-2 gap-4">
        {quickAccessItems.map((item) => (
          <Link key={item.id} to={item.path}>
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`p-4 rounded-xl ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'} shadow-sm`}
            >
              <div className="flex items-center justify-between mb-3">
                <div className={`w-12 h-12 rounded-lg ${item.color} flex items-center justify-center`}>
                  <item.icon className="text-white text-xl" />
                </div>
                <FaPlay className="text-gray-400" />
              </div>
              
              <h3 className="font-semibold text-lg">{item.title}</h3>
              <p className="text-sm text-gray-500 mb-2">{item.subtitle}</p>
              <p className="text-xs text-[#88cc14] font-medium">{item.count}</p>
            </motion.div>
          </Link>
        ))}
      </div>
    </div>
  );

  // Render recent activity
  const renderRecentActivity = () => (
    <div className="px-4 py-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-bold">Recent Activity</h2>
        <button className="text-[#88cc14] text-sm font-medium">View All</button>
      </div>
      
      <div className="space-y-3">
        {recentActivity.map((activity) => (
          <motion.div
            key={activity.id}
            whileTap={{ scale: 0.98 }}
            className={`p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center flex-1">
                <div className="w-10 h-10 rounded-lg bg-[#88cc14]/20 flex items-center justify-center mr-3">
                  <activity.icon className="text-[#88cc14]" />
                </div>
                
                <div className="flex-1">
                  <h3 className="font-medium text-sm">{activity.title}</h3>
                  <p className="text-xs text-gray-500">{activity.timeAgo}</p>
                </div>
              </div>
              
              <div className="text-right">
                <div className="text-sm font-medium">{activity.progress}%</div>
                <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full mt-1">
                  <div 
                    className="h-full bg-[#88cc14] rounded-full"
                    style={{ width: `${activity.progress}%` }}
                  />
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  // Render stats cards
  const renderStatsCards = () => (
    <div className="px-4 py-6">
      <h2 className="text-lg font-bold mb-4">Your Progress</h2>
      <div className="grid grid-cols-3 gap-3">
        <div className={`p-3 rounded-lg text-center ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="text-2xl font-bold text-[#88cc14]">12</div>
          <div className="text-xs text-gray-500">Completed</div>
        </div>
        
        <div className={`p-3 rounded-lg text-center ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="text-2xl font-bold text-blue-500">3</div>
          <div className="text-xs text-gray-500">In Progress</div>
        </div>
        
        <div className={`p-3 rounded-lg text-center ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="text-2xl font-bold text-purple-500">2</div>
          <div className="text-xs text-gray-500">Certificates</div>
        </div>
      </div>
    </div>
  );

  // Render bottom navigation
  const renderBottomNav = () => (
    <div className={`fixed bottom-0 left-0 right-0 ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'} px-2 py-2 z-40`}>
      <div className="flex items-center justify-around">
        {bottomNavItems.map((item) => (
          <Link key={item.id} to={item.path}>
            <motion.button
              whileTap={{ scale: 0.9 }}
              onClick={() => setActiveTab(item.id)}
              className={`flex flex-col items-center p-2 rounded-lg ${
                activeTab === item.id ? 'text-[#88cc14]' : 'text-gray-500'
              }`}
            >
              <item.icon className="text-xl mb-1" />
              <span className="text-xs">{item.label}</span>
            </motion.button>
          </Link>
        ))}
      </div>
    </div>
  );

  // Render side menu
  const renderSideMenu = () => (
    <AnimatePresence>
      {isMenuOpen && (
        <>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50"
            onClick={() => setIsMenuOpen(false)}
          />
          
          <motion.div
            initial={{ x: -300 }}
            animate={{ x: 0 }}
            exit={{ x: -300 }}
            className={`fixed left-0 top-0 bottom-0 w-80 ${darkMode ? 'bg-[#0B1120]' : 'bg-white'} z-50 shadow-xl`}
          >
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold">XCerberus</h2>
                <button
                  onClick={() => setIsMenuOpen(false)}
                  className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
                >
                  <FaTimes />
                </button>
              </div>
            </div>
            
            <div className="p-4">
              <div className="space-y-2">
                <Link to="/content-management" onClick={() => setIsMenuOpen(false)}>
                  <div className="flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800">
                    <FaBook className="mr-3 text-[#88cc14]" />
                    <span>Content Management</span>
                  </div>
                </Link>
                
                <Link to="/virtual-labs" onClick={() => setIsMenuOpen(false)}>
                  <div className="flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800">
                    <FaLaptop className="mr-3 text-[#88cc14]" />
                    <span>Virtual Labs</span>
                  </div>
                </Link>
                
                <Link to="/assessments" onClick={() => setIsMenuOpen(false)}>
                  <div className="flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800">
                    <FaCode className="mr-3 text-[#88cc14]" />
                    <span>Assessments</span>
                  </div>
                </Link>
                
                <Link to="/certifications" onClick={() => setIsMenuOpen(false)}>
                  <div className="flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800">
                    <FaCertificate className="mr-3 text-[#88cc14]" />
                    <span>Certifications</span>
                  </div>
                </Link>
                
                <Link to="/analytics-dashboard" onClick={() => setIsMenuOpen(false)}>
                  <div className="flex items-center p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800">
                    <FaChartLine className="mr-3 text-[#88cc14]" />
                    <span>Analytics</span>
                  </div>
                </Link>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );

  if (!isMobile) {
    return null; // Only render on mobile devices
  }

  return (
    <div 
      className={`min-h-screen ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} pb-20`}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
    >
      {renderMobileHeader()}
      {renderSideMenu()}
      
      <div className="overflow-y-auto">
        {renderQuickAccess()}
        {renderStatsCards()}
        {renderRecentActivity()}
      </div>
      
      {renderBottomNav()}
    </div>
  );
};

export default MobileOptimizedDashboard;
