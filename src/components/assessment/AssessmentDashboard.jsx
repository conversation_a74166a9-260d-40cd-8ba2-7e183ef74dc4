import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaQuestionCircle, FaPlay, FaStop, FaClock, FaCheck, FaTimes,
  FaChartLine, FaUsers, FaGraduationCap, FaCode, FaBook, FaEye,
  FaPlus, FaEdit, FaTrash, FaDownload, FaUpload, FaSync, FaAward,
  FaCheckCircle, FaTimesCircle, FaExclamationTriangle, FaSpinner
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import assessmentEngineService from '../../services/assessmentEngineService';

/**
 * Assessment Dashboard
 * 
 * Comprehensive dashboard for managing assessments, automated grading,
 * and performance analytics.
 */
const AssessmentDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [assessments, setAssessments] = useState([]);
  const [userAttempts, setUserAttempts] = useState([]);
  const [selectedAssessment, setSelectedAssessment] = useState(null);
  const [activeAttempt, setActiveAttempt] = useState(null);

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const assessmentsResult = await assessmentEngineService.getAssessments();
        if (assessmentsResult.success) setAssessments(assessmentsResult.assessments);
      } catch (error) {
        console.error('Error fetching assessment data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Start assessment
  const handleStartAssessment = async (assessmentId) => {
    try {
      const result = await assessmentEngineService.startAssessment(assessmentId);
      if (result.success) {
        setActiveAttempt(result.attempt);
        setActiveTab('take-assessment');
      } else {
        alert('Failed to start assessment: ' + result.error);
      }
    } catch (error) {
      console.error('Error starting assessment:', error);
      alert('Failed to start assessment');
    }
  };

  // Get assessment type icon
  const getAssessmentTypeIcon = (typeName) => {
    switch (typeName) {
      case 'quiz': return <FaQuestionCircle className="text-blue-500" />;
      case 'exam': return <FaGraduationCap className="text-purple-500" />;
      case 'practice': return <FaBook className="text-green-500" />;
      case 'certification': return <FaAward className="text-gold-500" />;
      case 'coding': return <FaCode className="text-red-500" />;
      case 'practical': return <FaPlay className="text-orange-500" />;
      default: return <FaQuestionCircle className="text-gray-500" />;
    }
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-500/20 text-green-500';
      case 'intermediate': return 'bg-yellow-500/20 text-yellow-500';
      case 'advanced': return 'bg-red-500/20 text-red-500';
      case 'expert': return 'bg-purple-500/20 text-purple-500';
      default: return 'bg-gray-500/20 text-gray-500';
    }
  };

  // Render overview tab
  const renderOverview = () => (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Available Assessments</p>
              <p className="text-2xl font-bold text-[#88cc14]">{assessments.length}</p>
            </div>
            <FaQuestionCircle className="text-3xl text-[#88cc14]" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Completed</p>
              <p className="text-2xl font-bold text-blue-500">12</p>
            </div>
            <FaCheckCircle className="text-3xl text-blue-500" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Average Score</p>
              <p className="text-2xl font-bold text-green-500">87%</p>
            </div>
            <FaChartLine className="text-3xl text-green-500" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Pass Rate</p>
              <p className="text-2xl font-bold text-purple-500">92%</p>
            </div>
            <FaAward className="text-3xl text-purple-500" />
          </div>
        </motion.div>
      </div>

      {/* Quick Actions */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            onClick={() => setActiveTab('assessments')}
            className="p-4 bg-[#88cc14] text-black rounded-lg hover:bg-[#7ab512] flex flex-col items-center gap-2"
          >
            <FaPlay className="text-xl" />
            <span className="text-sm font-medium">Take Assessment</span>
          </button>
          
          <button
            onClick={() => setActiveTab('results')}
            className="p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex flex-col items-center gap-2"
          >
            <FaChartLine className="text-xl" />
            <span className="text-sm font-medium">View Results</span>
          </button>
          
          <button
            onClick={() => setActiveTab('create')}
            className="p-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 flex flex-col items-center gap-2"
          >
            <FaPlus className="text-xl" />
            <span className="text-sm font-medium">Create Assessment</span>
          </button>
          
          <button
            onClick={() => setActiveTab('analytics')}
            className="p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 flex flex-col items-center gap-2"
          >
            <FaUsers className="text-xl" />
            <span className="text-sm font-medium">Analytics</span>
          </button>
        </div>
      </div>

      {/* Recent Assessments */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h3 className="text-lg font-semibold mb-4">Recent Assessments</h3>
        <div className="space-y-3">
          {[
            { name: 'Web Security Fundamentals Quiz', score: 92, status: 'passed', date: '2024-11-28' },
            { name: 'SQL Injection Assessment', score: 78, status: 'passed', date: '2024-11-25' },
            { name: 'Network Security Exam', score: 65, status: 'failed', date: '2024-11-22' },
            { name: 'Cryptography Practice Test', score: 88, status: 'passed', date: '2024-11-20' }
          ].map((assessment, index) => (
            <div key={index} className={`flex items-center justify-between p-3 rounded border ${darkMode ? 'border-gray-600 bg-[#252D4A]' : 'border-gray-200 bg-gray-50'}`}>
              <div className="flex items-center gap-3">
                <div className={`w-3 h-3 rounded-full ${assessment.status === 'passed' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <div>
                  <h4 className="font-medium">{assessment.name}</h4>
                  <p className="text-sm text-gray-500">{assessment.date}</p>
                </div>
              </div>
              <div className="text-right">
                <p className={`font-bold ${assessment.status === 'passed' ? 'text-green-500' : 'text-red-500'}`}>
                  {assessment.score}%
                </p>
                <p className="text-xs text-gray-500 capitalize">{assessment.status}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Render assessments tab
  const renderAssessments = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {assessments.map(assessment => (
          <motion.div
            key={assessment.id}
            whileHover={{ scale: 1.02 }}
            className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-2">
                {getAssessmentTypeIcon(assessment.assessment_types?.name)}
                <span className="px-2 py-1 bg-blue-500/20 text-blue-500 rounded text-xs">
                  {assessment.assessment_types?.display_name}
                </span>
              </div>
              {assessment.is_proctored && (
                <span className="px-2 py-1 bg-orange-500/20 text-orange-500 rounded text-xs">
                  Proctored
                </span>
              )}
            </div>
            
            <h3 className="text-lg font-semibold mb-2">{assessment.title}</h3>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 line-clamp-3`}>
              {assessment.description || 'No description available'}
            </p>
            
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Questions:</span>
                <span className="font-medium">{assessment.assessment_questions_map?.length || 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Time Limit:</span>
                <span className="font-medium">{assessment.time_limit || 'No limit'} min</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Passing Score:</span>
                <span className="font-medium">{assessment.passing_score}%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Max Attempts:</span>
                <span className="font-medium">{assessment.max_attempts}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
              <div className="flex items-center gap-1">
                <FaUsers />
                <span>{assessment.total_attempts || 0} attempts</span>
              </div>
              <div className="flex items-center gap-1">
                <FaChartLine />
                <span>{assessment.average_score || 0}% avg</span>
              </div>
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={() => handleStartAssessment(assessment.id)}
                className="flex-1 bg-[#88cc14] text-black px-3 py-2 rounded text-sm hover:bg-[#7ab512] flex items-center justify-center"
              >
                <FaPlay className="mr-2" />
                Start Assessment
              </button>
              <button
                onClick={() => setSelectedAssessment(assessment)}
                className="bg-blue-500 text-white px-3 py-2 rounded text-sm hover:bg-blue-600"
              >
                <FaEye />
              </button>
            </div>
          </motion.div>
        ))}
      </div>
      
      {assessments.length === 0 && (
        <div className="text-center py-12">
          <FaQuestionCircle className="text-4xl text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Assessments Available</h3>
          <p className="text-gray-500 mb-4">Check back later for new assessments</p>
        </div>
      )}
    </div>
  );

  // Render take assessment tab
  const renderTakeAssessment = () => (
    <div className="space-y-6">
      {activeAttempt ? (
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold">Assessment in Progress</h3>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-orange-500">
                <FaClock />
                <span className="font-mono">45:30</span>
              </div>
              <button className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                Submit Assessment
              </button>
            </div>
          </div>
          
          <div className="text-center py-12">
            <FaSpinner className="text-4xl text-[#88cc14] mx-auto mb-4 animate-spin" />
            <h3 className="text-lg font-semibold mb-2">Assessment Interface</h3>
            <p className="text-gray-500 mb-4">Interactive assessment interface would be rendered here</p>
            <p className="text-sm text-gray-400">This would include question navigation, answer submission, and real-time progress tracking</p>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <FaQuestionCircle className="text-4xl text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Active Assessment</h3>
          <p className="text-gray-500 mb-4">Start an assessment to begin</p>
          <button
            onClick={() => setActiveTab('assessments')}
            className="bg-[#88cc14] text-black px-6 py-3 rounded-lg hover:bg-[#7ab512]"
          >
            Browse Assessments
          </button>
        </div>
      )}
    </div>
  );

  // Navigation tabs
  const tabs = [
    { id: 'overview', name: 'Overview', icon: FaChartLine },
    { id: 'assessments', name: 'Available Assessments', icon: FaQuestionCircle },
    { id: 'take-assessment', name: 'Take Assessment', icon: FaPlay },
    { id: 'results', name: 'My Results', icon: FaAward },
    { id: 'create', name: 'Create Assessment', icon: FaPlus },
    { id: 'analytics', name: 'Analytics', icon: FaUsers }
  ];

  if (loading) {
    return (
      <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} flex items-center justify-center`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14] mx-auto mb-4"></div>
          <p className={`${darkMode ? 'text-white' : 'text-gray-900'}`}>Loading Assessments...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Header */}
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-b px-6 py-4`}>
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <FaQuestionCircle className="mr-3 text-[#88cc14]" />
              Assessment Dashboard
            </h1>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Advanced assessment engine with automated grading
            </p>
          </div>
          <button
            onClick={() => setActiveTab('assessments')}
            className="bg-[#88cc14] text-black px-4 py-2 rounded-lg hover:bg-[#7ab512] flex items-center"
          >
            <FaPlay className="mr-2" />
            Take Assessment
          </button>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className={`w-64 ${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-r min-h-screen`}>
          <nav className="p-4">
            <div className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center transition-colors ${
                    activeTab === tab.id
                      ? 'bg-[#88cc14] text-black'
                      : darkMode
                        ? 'hover:bg-[#252D4A] text-gray-300'
                        : 'hover:bg-gray-100 text-gray-700'
                  }`}
                >
                  <tab.icon className="mr-3" />
                  {tab.name}
                </button>
              ))}
            </div>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'assessments' && renderAssessments()}
          {activeTab === 'take-assessment' && renderTakeAssessment()}
          {activeTab === 'results' && (
            <div className="text-center py-12">
              <FaAward className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Assessment Results</h3>
              <p className="text-gray-500 mb-4">View your assessment history and performance</p>
            </div>
          )}
          {activeTab === 'create' && (
            <div className="text-center py-12">
              <FaPlus className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Create Assessment</h3>
              <p className="text-gray-500 mb-4">Assessment creation interface coming soon</p>
            </div>
          )}
          {activeTab === 'analytics' && (
            <div className="text-center py-12">
              <FaUsers className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Assessment Analytics</h3>
              <p className="text-gray-500 mb-4">Performance analytics and insights</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AssessmentDashboard;
