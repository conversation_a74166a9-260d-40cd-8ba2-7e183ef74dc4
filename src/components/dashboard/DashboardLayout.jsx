import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FaHome, FaBook, FaCode, FaTrophy, Fa<PERSON>ser, FaCog, FaChartLine } from 'react-icons/fa';

const DashboardLayout = ({ children }) => {
  const location = useLocation();
  
  const sidebarItems = [
    { icon: FaHome, label: 'Dashboard', path: '/dashboard' },
    { icon: FaBook, label: 'Learning Paths', path: '/dashboard/learning-paths' },
    { icon: FaCode, label: 'Challenges', path: '/dashboard/challenges' },
    { icon: FaTrophy, label: 'Achievements', path: '/dashboard/achievements' },
    { icon: FaChartLine, label: 'Progress', path: '/dashboard/progress' },
    { icon: FaUser, label: 'Profile', path: '/dashboard/profile' },
    { icon: FaCog, label: 'Settings', path: '/dashboard/settings' },
  ];

  return (
    <div className="flex h-screen bg-cyber-black text-white">
      {/* Sidebar */}
      <div className="w-64 bg-[#0F172A] border-r border-gray-800 flex flex-col">
        {/* Logo and User */}
        <div className="p-4 border-b border-gray-800">
          <div className="flex items-center justify-center mb-6">
            <h1 className="text-xl font-bold text-primary">Cyber<span className="text-white">Learning</span></h1>
          </div>
          
          <div className="flex flex-col items-center">
            <div className="w-20 h-20 rounded-full bg-gray-800 border-2 border-primary flex items-center justify-center overflow-hidden">
              <FaUser className="text-3xl text-gray-400" />
            </div>
            <h2 className="mt-2 font-bold">Username</h2>
            <p className="text-xs text-gray-400">Beginner</p>
            
            <div className="mt-2 w-full bg-gray-800 rounded-full h-1.5">
              <div className="bg-primary h-1.5 rounded-full" style={{ width: '25%' }}></div>
            </div>
            <p className="text-xs text-gray-400 mt-1">Level 1 • 25 XP</p>
          </div>
        </div>
        
        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto py-4">
          <ul className="space-y-2 px-2">
            {sidebarItems.map((item, index) => (
              <li key={index}>
                <Link
                  to={item.path}
                  className={`flex items-center gap-3 px-4 py-2.5 rounded-lg transition-colors ${
                    location.pathname === item.path
                      ? 'bg-primary text-black font-medium'
                      : 'text-gray-400 hover:bg-gray-800 hover:text-white'
                  }`}
                >
                  <item.icon className="text-lg" />
                  <span>{item.label}</span>
                </Link>
              </li>
            ))}
          </ul>
        </nav>
        
        {/* Footer */}
        <div className="p-4 border-t border-gray-800">
          <div className="bg-gray-800 rounded-lg p-3 text-center">
            <p className="text-xs text-gray-400">Need help?</p>
            <button className="mt-2 text-sm text-primary hover:underline">Contact Support</button>
          </div>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-[#0F172A] border-b border-gray-800 py-4 px-6">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold">Dashboard</h1>
            
            <div className="flex items-center gap-4">
              <button className="text-gray-400 hover:text-white">
                <FaCog />
              </button>
              <div className="h-8 w-8 rounded-full bg-gray-700 flex items-center justify-center">
                <span className="text-sm font-medium">JD</span>
              </div>
            </div>
          </div>
        </header>
        
        {/* Content */}
        <main className="flex-1 overflow-y-auto p-6 bg-cyber-black">
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
