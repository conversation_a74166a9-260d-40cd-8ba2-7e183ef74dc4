import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaStore, FaPlus, FaSearch, FaFilter, FaStar, FaDownload, FaEye,
  FaHeart, FaShoppingCart, FaUser, FaCrown, FaGem, FaAward,
  FaBook, FaLaptop, FaCode, FaCertificate, FaPlay, FaEdit,
  FaChartLine, FaDollarSign, FaUsers, FaTrophy, FaShieldAlt
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';

/**
 * Content Marketplace Dashboard
 * 
 * User-generated content marketplace with monetization,
 * creator tools, and community features.
 */
const ContentMarketplaceDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('browse');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('popular');
  const [priceFilter, setPriceFilter] = useState('all');
  const [marketplaceContent, setMarketplaceContent] = useState([]);
  const [isCreator, setIsCreator] = useState(false);
  const [creatorStats, setCreatorStats] = useState(null);

  // Mock marketplace content
  useEffect(() => {
    const mockContent = [
      {
        id: 1,
        title: 'Advanced SQL Injection Techniques',
        description: 'Comprehensive course covering advanced SQL injection methods and prevention techniques.',
        creator: {
          name: 'Alex Security',
          avatar: '👨‍💻',
          tier: 'gold',
          verified: true,
          rating: 4.9
        },
        type: 'course',
        category: 'web_security',
        price: 4999, // $49.99
        originalPrice: 7999,
        rating: 4.8,
        reviews: 234,
        students: 1250,
        duration: 180, // minutes
        level: 'advanced',
        thumbnail: '/api/placeholder/300/200',
        tags: ['sql injection', 'web security', 'penetration testing'],
        isNew: false,
        isFeatured: true,
        discount: 38
      },
      {
        id: 2,
        title: 'Kubernetes Security Lab Environment',
        description: 'Hands-on lab environment for practicing Kubernetes security configurations.',
        creator: {
          name: 'CloudSec Pro',
          avatar: '☁️',
          tier: 'platinum',
          verified: true,
          rating: 4.95
        },
        type: 'lab',
        category: 'cloud_security',
        price: 2999, // $29.99
        rating: 4.9,
        reviews: 89,
        students: 456,
        duration: 120,
        level: 'intermediate',
        thumbnail: '/api/placeholder/300/200',
        tags: ['kubernetes', 'cloud security', 'containers'],
        isNew: true,
        isFeatured: false
      },
      {
        id: 3,
        title: 'CISSP Practice Assessment Bundle',
        description: 'Complete practice test suite for CISSP certification preparation.',
        creator: {
          name: 'CertMaster',
          avatar: '🎓',
          tier: 'silver',
          verified: true,
          rating: 4.7
        },
        type: 'assessment',
        category: 'certification',
        price: 1999, // $19.99
        rating: 4.6,
        reviews: 567,
        students: 2340,
        duration: 300,
        level: 'expert',
        thumbnail: '/api/placeholder/300/200',
        tags: ['cissp', 'certification', 'practice tests'],
        isNew: false,
        isFeatured: true
      },
      {
        id: 4,
        title: 'Network Penetration Testing Toolkit',
        description: 'Collection of custom scripts and tools for network penetration testing.',
        creator: {
          name: 'PenTest Tools',
          avatar: '🔧',
          tier: 'gold',
          verified: true,
          rating: 4.8
        },
        type: 'tool',
        category: 'penetration_testing',
        price: 3999, // $39.99
        rating: 4.7,
        reviews: 123,
        students: 678,
        duration: 0, // tools don't have duration
        level: 'advanced',
        thumbnail: '/api/placeholder/300/200',
        tags: ['penetration testing', 'network security', 'tools'],
        isNew: false,
        isFeatured: false
      }
    ];
    
    setMarketplaceContent(mockContent);
  }, []);

  // Categories for filtering
  const categories = [
    { id: 'all', name: 'All Categories', icon: FaStore },
    { id: 'web_security', name: 'Web Security', icon: FaShieldAlt },
    { id: 'network_security', name: 'Network Security', icon: FaUsers },
    { id: 'cloud_security', name: 'Cloud Security', icon: FaBook },
    { id: 'penetration_testing', name: 'Penetration Testing', icon: FaCode },
    { id: 'certification', name: 'Certification Prep', icon: FaCertificate },
    { id: 'tools', name: 'Tools & Scripts', icon: FaLaptop }
  ];

  // Content types
  const contentTypes = [
    { id: 'course', name: 'Courses', icon: FaBook, color: 'bg-blue-500' },
    { id: 'lab', name: 'Labs', icon: FaLaptop, color: 'bg-green-500' },
    { id: 'assessment', name: 'Assessments', icon: FaCode, color: 'bg-purple-500' },
    { id: 'tool', name: 'Tools', icon: FaShieldAlt, color: 'bg-orange-500' }
  ];

  // Creator tier badges
  const getTierBadge = (tier) => {
    const tiers = {
      bronze: { icon: FaAward, color: 'text-orange-600', name: 'Bronze' },
      silver: { icon: FaTrophy, color: 'text-gray-500', name: 'Silver' },
      gold: { icon: FaCrown, color: 'text-yellow-500', name: 'Gold' },
      platinum: { icon: FaGem, color: 'text-purple-500', name: 'Platinum' }
    };
    return tiers[tier] || tiers.bronze;
  };

  // Filter content based on search and filters
  const filteredContent = marketplaceContent.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    
    const matchesPrice = priceFilter === 'all' ||
                        (priceFilter === 'free' && item.price === 0) ||
                        (priceFilter === 'paid' && item.price > 0) ||
                        (priceFilter === 'under_25' && item.price < 2500) ||
                        (priceFilter === 'under_50' && item.price < 5000);
    
    return matchesSearch && matchesCategory && matchesPrice;
  });

  // Sort content
  const sortedContent = [...filteredContent].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.students - a.students;
      case 'rating':
        return b.rating - a.rating;
      case 'newest':
        return b.isNew ? 1 : -1;
      case 'price_low':
        return a.price - b.price;
      case 'price_high':
        return b.price - a.price;
      default:
        return 0;
    }
  });

  // Render content card
  const renderContentCard = (item) => (
    <motion.div
      key={item.id}
      whileHover={{ y: -4 }}
      className={`rounded-lg overflow-hidden ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'} shadow-sm hover:shadow-lg transition-all`}
    >
      {/* Thumbnail */}
      <div className="relative">
        <div className="w-full h-48 bg-gradient-to-br from-[#88cc14] to-[#7ab512] flex items-center justify-center">
          <span className="text-4xl">📚</span>
        </div>
        
        {/* Badges */}
        <div className="absolute top-3 left-3 flex gap-2">
          {item.isNew && (
            <span className="px-2 py-1 bg-green-500 text-white text-xs rounded-full">New</span>
          )}
          {item.isFeatured && (
            <span className="px-2 py-1 bg-purple-500 text-white text-xs rounded-full">Featured</span>
          )}
          {item.discount && (
            <span className="px-2 py-1 bg-red-500 text-white text-xs rounded-full">-{item.discount}%</span>
          )}
        </div>
        
        {/* Content type */}
        <div className="absolute top-3 right-3">
          {contentTypes.find(type => type.id === item.type) && (
            <div className={`w-8 h-8 rounded-full ${contentTypes.find(type => type.id === item.type).color} flex items-center justify-center`}>
              {React.createElement(contentTypes.find(type => type.id === item.type).icon, { className: 'text-white text-sm' })}
            </div>
          )}
        </div>
        
        {/* Quick actions */}
        <div className="absolute bottom-3 right-3 flex gap-2">
          <button className="w-8 h-8 rounded-full bg-black/50 text-white flex items-center justify-center hover:bg-black/70">
            <FaEye className="text-sm" />
          </button>
          <button className="w-8 h-8 rounded-full bg-black/50 text-white flex items-center justify-center hover:bg-black/70">
            <FaHeart className="text-sm" />
          </button>
        </div>
      </div>
      
      {/* Content */}
      <div className="p-4">
        {/* Creator info */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            <span className="text-2xl mr-2">{item.creator.avatar}</span>
            <div>
              <div className="flex items-center">
                <span className="text-sm font-medium">{item.creator.name}</span>
                {item.creator.verified && (
                  <FaShieldAlt className="ml-1 text-blue-500 text-xs" />
                )}
                {React.createElement(getTierBadge(item.creator.tier).icon, {
                  className: `ml-1 ${getTierBadge(item.creator.tier).color} text-xs`
                })}
              </div>
              <div className="flex items-center text-xs text-gray-500">
                <FaStar className="text-yellow-500 mr-1" />
                {item.creator.rating}
              </div>
            </div>
          </div>
        </div>
        
        {/* Title and description */}
        <h3 className="font-semibold text-lg mb-2 line-clamp-2">{item.title}</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">{item.description}</p>
        
        {/* Stats */}
        <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
          <div className="flex items-center">
            <FaStar className="text-yellow-500 mr-1" />
            <span>{item.rating}</span>
            <span className="mx-1">•</span>
            <span>({item.reviews})</span>
          </div>
          <div className="flex items-center">
            <FaUsers className="mr-1" />
            <span>{item.students.toLocaleString()}</span>
          </div>
        </div>
        
        {/* Tags */}
        <div className="flex flex-wrap gap-1 mb-3">
          {item.tags.slice(0, 3).map((tag, index) => (
            <span key={index} className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded">
              {tag}
            </span>
          ))}
        </div>
        
        {/* Price and action */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {item.originalPrice && item.originalPrice > item.price && (
              <span className="text-sm text-gray-500 line-through mr-2">
                ${(item.originalPrice / 100).toFixed(2)}
              </span>
            )}
            <span className="text-lg font-bold text-[#88cc14]">
              {item.price === 0 ? 'Free' : `$${(item.price / 100).toFixed(2)}`}
            </span>
          </div>
          
          <button className="bg-[#88cc14] text-black px-4 py-2 rounded-lg hover:bg-[#7ab512] flex items-center">
            <FaShoppingCart className="mr-2" />
            {item.price === 0 ? 'Get' : 'Buy'}
          </button>
        </div>
      </div>
    </motion.div>
  );

  // Render creator dashboard
  const renderCreatorDashboard = () => (
    <div className="space-y-6">
      {/* Creator stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Revenue</p>
              <p className="text-2xl font-bold text-[#88cc14]">$2,450</p>
            </div>
            <FaDollarSign className="text-3xl text-[#88cc14]" />
          </div>
        </div>
        
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Content Items</p>
              <p className="text-2xl font-bold text-blue-500">12</p>
            </div>
            <FaBook className="text-3xl text-blue-500" />
          </div>
        </div>
        
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Students</p>
              <p className="text-2xl font-bold text-green-500">3,240</p>
            </div>
            <FaUsers className="text-3xl text-green-500" />
          </div>
        </div>
        
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Avg Rating</p>
              <p className="text-2xl font-bold text-purple-500">4.8</p>
            </div>
            <FaStar className="text-3xl text-purple-500" />
          </div>
        </div>
      </div>
      
      {/* Quick actions */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h3 className="text-lg font-semibold mb-4">Creator Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="p-4 bg-[#88cc14] text-black rounded-lg hover:bg-[#7ab512] flex flex-col items-center">
            <FaPlus className="text-xl mb-2" />
            <span className="text-sm">Create Content</span>
          </button>
          
          <button className="p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex flex-col items-center">
            <FaChartLine className="text-xl mb-2" />
            <span className="text-sm">Analytics</span>
          </button>
          
          <button className="p-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 flex flex-col items-center">
            <FaDollarSign className="text-xl mb-2" />
            <span className="text-sm">Earnings</span>
          </button>
          
          <button className="p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 flex flex-col items-center">
            <FaEdit className="text-xl mb-2" />
            <span className="text-sm">Manage Content</span>
          </button>
        </div>
      </div>
    </div>
  );

  // Navigation tabs
  const tabs = [
    { id: 'browse', name: 'Browse Marketplace', icon: FaStore },
    { id: 'my_purchases', name: 'My Purchases', icon: FaShoppingCart },
    { id: 'creator', name: 'Creator Dashboard', icon: FaUser },
    { id: 'earnings', name: 'Earnings', icon: FaDollarSign }
  ];

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Header */}
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-b px-6 py-4`}>
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <FaStore className="mr-3 text-[#88cc14]" />
              Content Marketplace
            </h1>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Discover and create cybersecurity learning content
            </p>
          </div>
          <button className="bg-[#88cc14] text-black px-4 py-2 rounded-lg hover:bg-[#7ab512] flex items-center">
            <FaPlus className="mr-2" />
            Create Content
          </button>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className={`w-64 ${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-r min-h-screen`}>
          <nav className="p-4">
            <div className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center transition-colors ${
                    activeTab === tab.id
                      ? 'bg-[#88cc14] text-black'
                      : darkMode
                        ? 'hover:bg-[#252D4A] text-gray-300'
                        : 'hover:bg-gray-100 text-gray-700'
                  }`}
                >
                  <tab.icon className="mr-3" />
                  {tab.name}
                </button>
              ))}
            </div>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {activeTab === 'browse' && (
            <div className="space-y-6">
              {/* Filters */}
              <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  {/* Search */}
                  <div className="relative">
                    <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search content..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
                        darkMode 
                          ? 'bg-[#252D4A] border-gray-600 text-white placeholder-gray-400'
                          : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'
                      }`}
                    />
                  </div>
                  
                  {/* Category filter */}
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className={`px-4 py-2 rounded-lg border ${
                      darkMode 
                        ? 'bg-[#252D4A] border-gray-600 text-white'
                        : 'bg-gray-50 border-gray-300 text-gray-900'
                    }`}
                  >
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>{category.name}</option>
                    ))}
                  </select>
                  
                  {/* Price filter */}
                  <select
                    value={priceFilter}
                    onChange={(e) => setPriceFilter(e.target.value)}
                    className={`px-4 py-2 rounded-lg border ${
                      darkMode 
                        ? 'bg-[#252D4A] border-gray-600 text-white'
                        : 'bg-gray-50 border-gray-300 text-gray-900'
                    }`}
                  >
                    <option value="all">All Prices</option>
                    <option value="free">Free</option>
                    <option value="under_25">Under $25</option>
                    <option value="under_50">Under $50</option>
                    <option value="paid">Paid</option>
                  </select>
                  
                  {/* Sort */}
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className={`px-4 py-2 rounded-lg border ${
                      darkMode 
                        ? 'bg-[#252D4A] border-gray-600 text-white'
                        : 'bg-gray-50 border-gray-300 text-gray-900'
                    }`}
                  >
                    <option value="popular">Most Popular</option>
                    <option value="rating">Highest Rated</option>
                    <option value="newest">Newest</option>
                    <option value="price_low">Price: Low to High</option>
                    <option value="price_high">Price: High to Low</option>
                  </select>
                </div>
              </div>
              
              {/* Content grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {sortedContent.map(renderContentCard)}
              </div>
              
              {sortedContent.length === 0 && (
                <div className="text-center py-12">
                  <FaStore className="text-4xl text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No content found</h3>
                  <p className="text-gray-500">Try adjusting your search or filters</p>
                </div>
              )}
            </div>
          )}
          
          {activeTab === 'creator' && renderCreatorDashboard()}
          
          {activeTab === 'my_purchases' && (
            <div className="text-center py-12">
              <FaShoppingCart className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">My Purchases</h3>
              <p className="text-gray-500">Your purchased content will appear here</p>
            </div>
          )}
          
          {activeTab === 'earnings' && (
            <div className="text-center py-12">
              <FaDollarSign className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Earnings Dashboard</h3>
              <p className="text-gray-500">Track your creator earnings and payouts</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContentMarketplaceDashboard;
