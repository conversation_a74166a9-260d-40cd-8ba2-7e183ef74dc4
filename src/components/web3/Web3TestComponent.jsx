import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaCheckCircle, FaTimesCircle, FaSpinner } from 'react-icons/fa';
import { useWeb3 } from '../../contexts/Web3Context';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

/**
 * Web3 Test Component
 * 
 * Simple component to test all Web3 functionality
 * and verify everything is working correctly.
 */
const Web3TestComponent = () => {
  const { darkMode } = useGlobalTheme();
  const {
    isConnected,
    connectWallet,
    tokenBalance,
    certificateCount,
    stakingRewards,
    earnTokens,
    stakeTokens,
    claimStakingRewards,
    issueCertificate,
    getUserCertificates
  } = useWeb3();

  const [testResults, setTestResults] = useState({});
  const [isRunningTests, setIsRunningTests] = useState(false);

  const runAllTests = async () => {
    setIsRunningTests(true);
    const results = {};

    try {
      // Test 1: Wallet Connection
      console.log('Testing wallet connection...');
      if (!isConnected) {
        await connectWallet();
      }
      results.walletConnection = isConnected ? 'pass' : 'fail';

      // Test 2: Token Balance
      console.log('Testing token balance...');
      results.tokenBalance = parseFloat(tokenBalance) > 0 ? 'pass' : 'fail';

      // Test 3: Earn Tokens
      console.log('Testing earn tokens...');
      const earnResult = await earnTokens(10, 'Test reward');
      results.earnTokens = earnResult.success ? 'pass' : 'fail';

      // Test 4: Stake Tokens
      console.log('Testing stake tokens...');
      const stakeResult = await stakeTokens(5);
      results.stakeTokens = stakeResult.success ? 'pass' : 'fail';

      // Test 5: Claim Rewards
      console.log('Testing claim rewards...');
      const claimResult = await claimStakingRewards();
      results.claimRewards = claimResult.success ? 'pass' : 'fail';

      // Test 6: Issue Certificate
      console.log('Testing certificate issuance...');
      const certResult = await issueCertificate({
        recipient: '0x1234567890123456789012345678901234567890',
        certificationType: 'test',
        title: 'Test Certificate',
        description: 'Test certificate for Web3 functionality',
        skillsProven: 'Web3 Testing',
        expiryDate: 0,
        metadataURI: 'https://example.com/metadata',
        skillLevel: 75
      });
      results.issueCertificate = certResult.success ? 'pass' : 'fail';

      // Test 7: Get Certificates
      console.log('Testing get certificates...');
      const getCertsResult = await getUserCertificates();
      results.getCertificates = getCertsResult.success ? 'pass' : 'fail';

      console.log('All tests completed!', results);
      setTestResults(results);

    } catch (error) {
      console.error('Test failed:', error);
      results.error = error.message;
      setTestResults(results);
    } finally {
      setIsRunningTests(false);
    }
  };

  const getTestIcon = (result) => {
    if (result === 'pass') return <FaCheckCircle className="text-green-500" />;
    if (result === 'fail') return <FaTimesCircle className="text-red-500" />;
    return <FaSpinner className="animate-spin text-yellow-500" />;
  };

  const tests = [
    { id: 'walletConnection', name: 'Wallet Connection', description: 'Connect to Web3 wallet' },
    { id: 'tokenBalance', name: 'Token Balance', description: 'Check XCYBER token balance' },
    { id: 'earnTokens', name: 'Earn Tokens', description: 'Earn XCYBER tokens for activities' },
    { id: 'stakeTokens', name: 'Stake Tokens', description: 'Stake tokens for rewards' },
    { id: 'claimRewards', name: 'Claim Rewards', description: 'Claim staking rewards' },
    { id: 'issueCertificate', name: 'Issue Certificate', description: 'Create NFT certificate' },
    { id: 'getCertificates', name: 'Get Certificates', description: 'Retrieve user certificates' }
  ];

  return (
    <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold">Web3 Functionality Test</h3>
          <p className="text-sm text-gray-500">Test all Web3 features to ensure they're working correctly</p>
        </div>
        
        <button
          onClick={runAllTests}
          disabled={isRunningTests}
          className="bg-[#88cc14] text-black px-6 py-2 rounded-lg hover:bg-[#7ab512] disabled:opacity-50 flex items-center"
        >
          {isRunningTests ? (
            <>
              <FaSpinner className="animate-spin mr-2" />
              Running Tests...
            </>
          ) : (
            'Run All Tests'
          )}
        </button>
      </div>

      {/* Current Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'}`}>
          <div className="text-sm text-gray-500">Connection Status</div>
          <div className={`font-bold ${isConnected ? 'text-green-500' : 'text-red-500'}`}>
            {isConnected ? 'Connected' : 'Disconnected'}
          </div>
        </div>
        
        <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'}`}>
          <div className="text-sm text-gray-500">XCYBER Balance</div>
          <div className="font-bold text-[#88cc14]">{tokenBalance}</div>
        </div>
        
        <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#252D4A]' : 'bg-gray-50'}`}>
          <div className="text-sm text-gray-500">NFT Certificates</div>
          <div className="font-bold text-purple-500">{certificateCount}</div>
        </div>
      </div>

      {/* Test Results */}
      <div className="space-y-3">
        <h4 className="font-semibold mb-3">Test Results:</h4>
        
        {tests.map((test) => (
          <motion.div
            key={test.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className={`flex items-center justify-between p-3 rounded-lg border ${
              darkMode ? 'border-gray-600 bg-[#252D4A]' : 'border-gray-200 bg-gray-50'
            }`}
          >
            <div className="flex-1">
              <div className="font-medium">{test.name}</div>
              <div className="text-sm text-gray-500">{test.description}</div>
            </div>
            
            <div className="flex items-center">
              {testResults[test.id] && (
                <span className={`mr-2 text-sm ${
                  testResults[test.id] === 'pass' ? 'text-green-500' : 'text-red-500'
                }`}>
                  {testResults[test.id] === 'pass' ? 'PASS' : 'FAIL'}
                </span>
              )}
              {getTestIcon(testResults[test.id])}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Error Display */}
      {testResults.error && (
        <div className="mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
          <div className="font-medium text-red-500">Test Error:</div>
          <div className="text-sm text-red-400">{testResults.error}</div>
        </div>
      )}

      {/* Success Message */}
      {Object.keys(testResults).length > 0 && !testResults.error && (
        <div className="mt-4 p-4 bg-green-500/20 border border-green-500/30 rounded-lg">
          <div className="font-medium text-green-500">
            Tests Completed! 
            {Object.values(testResults).filter(r => r === 'pass').length} / {tests.length} passed
          </div>
        </div>
      )}
    </div>
  );
};

export default Web3TestComponent;
