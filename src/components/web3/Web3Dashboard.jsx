import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaWallet, FaCoins, FaCertificate, FaVoteYea, FaStore, FaGem,
  FaChartLine, FaUsers, FaTrophy, FaRocket, FaShieldAlt, FaFire,
  FaExternalLinkAlt, FaCopy, FaCheck, FaSpinner, FaPlus, FaEye
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useWeb3 } from '../../contexts/Web3Context';
import Web3TestComponent from './Web3TestComponent';

/**
 * Web3 Dashboard
 * 
 * Comprehensive Web3 interface for XCerberus platform featuring:
 * - Wallet connection and management
 * - Token balance and staking
 * - NFT certificates display
 * - DAO governance participation
 * - Marketplace interactions
 */
const Web3Dashboard = () => {
  const { darkMode } = useGlobalTheme();
  const {
    account,
    isConnected,
    isConnecting,
    connectWallet,
    disconnectWallet,
    tokenBalance,
    certificateCount,
    stakingRewards,
    earnTokens,
    stakeTokens,
    claimStakingRewards,
    getUserCertificates,
    getVotingPower
  } = useWeb3();

  const [activeTab, setActiveTab] = useState('overview');
  const [userCertificates, setUserCertificates] = useState([]);
  const [votingPower, setVotingPower] = useState('0');
  const [loading, setLoading] = useState(false);
  const [copied, setCopied] = useState(false);
  const [stakeAmount, setStakeAmount] = useState('');

  // Load user data when connected
  useEffect(() => {
    if (isConnected && account) {
      loadUserData();
    }
  }, [isConnected, account]);

  const loadUserData = async () => {
    setLoading(true);
    try {
      // Load certificates
      const certsResult = await getUserCertificates();
      if (certsResult.success) {
        setUserCertificates(certsResult.certificates);
      }

      // Load voting power
      const powerResult = await getVotingPower();
      if (powerResult.success) {
        setVotingPower(powerResult.votingPower);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyAddress = () => {
    navigator.clipboard.writeText(account);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleStakeTokens = async () => {
    if (!stakeAmount || parseFloat(stakeAmount) <= 0) return;
    
    setLoading(true);
    try {
      const result = await stakeTokens(stakeAmount);
      if (result.success) {
        setStakeAmount('');
        // Refresh data will happen automatically via context
      } else {
        alert('Failed to stake tokens: ' + result.error);
      }
    } catch (error) {
      console.error('Error staking tokens:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClaimRewards = async () => {
    setLoading(true);
    try {
      const result = await claimStakingRewards();
      if (result.success) {
        // Refresh data will happen automatically via context
      } else {
        alert('Failed to claim rewards: ' + result.error);
      }
    } catch (error) {
      console.error('Error claiming rewards:', error);
    } finally {
      setLoading(false);
    }
  };

  // Render wallet connection
  const renderWalletConnection = () => (
    <div className="text-center py-12">
      <div className="w-24 h-24 bg-gradient-to-r from-[#88cc14] to-[#7ab512] rounded-full flex items-center justify-center mx-auto mb-6">
        <FaWallet className="text-4xl text-black" />
      </div>
      
      <h2 className="text-2xl font-bold mb-4">Connect Your Wallet</h2>
      <p className="text-gray-500 mb-8 max-w-md mx-auto">
        Connect your Web3 wallet to access decentralized features, earn XCYBER tokens, 
        and manage your NFT certificates.
      </p>
      
      <button
        onClick={connectWallet}
        disabled={isConnecting}
        className="bg-[#88cc14] text-black px-8 py-3 rounded-lg hover:bg-[#7ab512] disabled:opacity-50 flex items-center mx-auto"
      >
        {isConnecting ? (
          <>
            <FaSpinner className="animate-spin mr-2" />
            Connecting...
          </>
        ) : (
          <>
            <FaWallet className="mr-2" />
            Connect Wallet
          </>
        )}
      </button>
      
      <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center">
          <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
            <FaCoins className="text-blue-500" />
          </div>
          <p className="text-sm">Earn XCYBER</p>
        </div>
        
        <div className="text-center">
          <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
            <FaCertificate className="text-purple-500" />
          </div>
          <p className="text-sm">NFT Certificates</p>
        </div>
        
        <div className="text-center">
          <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
            <FaVoteYea className="text-green-500" />
          </div>
          <p className="text-sm">DAO Governance</p>
        </div>
        
        <div className="text-center">
          <div className="w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
            <FaStore className="text-orange-500" />
          </div>
          <p className="text-sm">Marketplace</p>
        </div>
      </div>
    </div>
  );

  // Render overview tab
  const renderOverview = () => (
    <div className="space-y-6">
      {/* Wallet Info */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Wallet Overview</h3>
          <button
            onClick={disconnectWallet}
            className="text-red-500 hover:text-red-600 text-sm"
          >
            Disconnect
          </button>
        </div>
        
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm text-gray-500">Address:</span>
          <div className="flex items-center">
            <span className="font-mono text-sm">
              {account?.slice(0, 6)}...{account?.slice(-4)}
            </span>
            <button
              onClick={handleCopyAddress}
              className="ml-2 text-gray-400 hover:text-gray-600"
            >
              {copied ? <FaCheck className="text-green-500" /> : <FaCopy />}
            </button>
          </div>
        </div>
      </div>

      {/* Token Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">XCYBER Balance</p>
              <p className="text-2xl font-bold text-[#88cc14]">
                {parseFloat(tokenBalance).toFixed(2)}
              </p>
            </div>
            <FaCoins className="text-3xl text-[#88cc14]" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">NFT Certificates</p>
              <p className="text-2xl font-bold text-purple-500">{certificateCount}</p>
            </div>
            <FaCertificate className="text-3xl text-purple-500" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Staking Rewards</p>
              <p className="text-2xl font-bold text-blue-500">
                {parseFloat(stakingRewards).toFixed(4)}
              </p>
            </div>
            <FaGem className="text-3xl text-blue-500" />
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Voting Power</p>
              <p className="text-2xl font-bold text-green-500">
                {parseFloat(votingPower).toFixed(0)}
              </p>
            </div>
            <FaVoteYea className="text-3xl text-green-500" />
          </div>
        </motion.div>
      </div>

      {/* Quick Actions */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            onClick={() => setActiveTab('staking')}
            className="p-4 bg-[#88cc14] text-black rounded-lg hover:bg-[#7ab512] flex flex-col items-center"
          >
            <FaGem className="text-xl mb-2" />
            <span className="text-sm">Stake Tokens</span>
          </button>
          
          <button
            onClick={() => setActiveTab('certificates')}
            className="p-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 flex flex-col items-center"
          >
            <FaCertificate className="text-xl mb-2" />
            <span className="text-sm">View NFTs</span>
          </button>
          
          <button
            onClick={() => setActiveTab('governance')}
            className="p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex flex-col items-center"
          >
            <FaVoteYea className="text-xl mb-2" />
            <span className="text-sm">DAO Vote</span>
          </button>
          
          <button
            onClick={() => setActiveTab('marketplace')}
            className="p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 flex flex-col items-center"
          >
            <FaStore className="text-xl mb-2" />
            <span className="text-sm">Marketplace</span>
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h3 className="text-lg font-semibold mb-4">Recent Web3 Activity</h3>
        <div className="space-y-3">
          {[
            { type: 'earn', description: 'Earned 50 XCYBER for completing course', time: '2 hours ago', icon: FaCoins, color: 'text-[#88cc14]' },
            { type: 'certificate', description: 'Received Web Security Expert NFT', time: '1 day ago', icon: FaCertificate, color: 'text-purple-500' },
            { type: 'stake', description: 'Staked 100 XCYBER tokens', time: '3 days ago', icon: FaGem, color: 'text-blue-500' },
            { type: 'vote', description: 'Voted on DAO Proposal #12', time: '1 week ago', icon: FaVoteYea, color: 'text-green-500' }
          ].map((activity, index) => (
            <div key={index} className={`flex items-center p-3 rounded border ${darkMode ? 'border-gray-600 bg-[#252D4A]' : 'border-gray-200 bg-gray-50'}`}>
              <div className={`w-10 h-10 rounded-lg bg-gray-500/20 flex items-center justify-center mr-3`}>
                <activity.icon className={`${activity.color}`} />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">{activity.description}</p>
                <p className="text-xs text-gray-500">{activity.time}</p>
              </div>
              <FaExternalLinkAlt className="text-gray-400 text-sm" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Render staking tab
  const renderStaking = () => (
    <div className="space-y-6">
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h3 className="text-lg font-semibold mb-4">Stake XCYBER Tokens</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium mb-2">Amount to Stake</label>
            <div className="relative">
              <input
                type="number"
                value={stakeAmount}
                onChange={(e) => setStakeAmount(e.target.value)}
                placeholder="Enter amount"
                className={`w-full p-3 rounded-lg border ${
                  darkMode 
                    ? 'bg-[#252D4A] border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:border-[#88cc14]`}
              />
              <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-500">
                XCYBER
              </span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Available: {parseFloat(tokenBalance).toFixed(2)} XCYBER
            </p>
            
            <button
              onClick={handleStakeTokens}
              disabled={loading || !stakeAmount || parseFloat(stakeAmount) <= 0}
              className="w-full mt-4 bg-[#88cc14] text-black py-3 rounded-lg hover:bg-[#7ab512] disabled:opacity-50 flex items-center justify-center"
            >
              {loading ? (
                <>
                  <FaSpinner className="animate-spin mr-2" />
                  Staking...
                </>
              ) : (
                <>
                  <FaGem className="mr-2" />
                  Stake Tokens
                </>
              )}
            </button>
          </div>
          
          <div>
            <h4 className="font-medium mb-3">Staking Rewards</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Pending Rewards:</span>
                <span className="font-medium">{parseFloat(stakingRewards).toFixed(4)} XCYBER</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">APY:</span>
                <span className="font-medium text-green-500">10%</span>
              </div>
              
              <button
                onClick={handleClaimRewards}
                disabled={loading || parseFloat(stakingRewards) <= 0}
                className="w-full bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 disabled:opacity-50 flex items-center justify-center"
              >
                {loading ? (
                  <>
                    <FaSpinner className="animate-spin mr-2" />
                    Claiming...
                  </>
                ) : (
                  <>
                    <FaFire className="mr-2" />
                    Claim Rewards
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Render certificates tab
  const renderCertificates = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">My NFT Certificates</h3>
        <span className="text-sm text-gray-500">{userCertificates.length} certificates</span>
      </div>
      
      {userCertificates.length === 0 ? (
        <div className="text-center py-12">
          <FaCertificate className="text-4xl text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Certificates Yet</h3>
          <p className="text-gray-500 mb-4">Complete courses and assessments to earn NFT certificates</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {userCertificates.map((cert, index) => (
            <motion.div
              key={cert.tokenId}
              whileHover={{ scale: 1.02 }}
              className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
            >
              <div className="flex items-center justify-between mb-4">
                <span className="px-2 py-1 bg-purple-500/20 text-purple-500 rounded text-xs">
                  {cert.certificationType}
                </span>
                <span className="text-xs text-gray-500">#{cert.tokenId}</span>
              </div>
              
              <h4 className="font-semibold mb-2">{cert.title}</h4>
              <p className="text-sm text-gray-500 mb-4 line-clamp-2">{cert.description}</p>
              
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-500">Skill Level:</span>
                  <span className="font-medium">{cert.skillLevel}/100</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Issued:</span>
                  <span className="font-medium">
                    {new Date(cert.issueDate * 1000).toLocaleDateString()}
                  </span>
                </div>
              </div>
              
              <div className="mt-4 flex gap-2">
                <button className="flex-1 bg-blue-500 text-white py-2 rounded text-sm hover:bg-blue-600 flex items-center justify-center">
                  <FaEye className="mr-1" />
                  View
                </button>
                <button className="flex-1 bg-green-500 text-white py-2 rounded text-sm hover:bg-green-600 flex items-center justify-center">
                  <FaExternalLinkAlt className="mr-1" />
                  Share
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );

  // Navigation tabs
  const tabs = [
    { id: 'overview', name: 'Overview', icon: FaChartLine },
    { id: 'staking', name: 'Staking', icon: FaGem },
    { id: 'certificates', name: 'NFT Certificates', icon: FaCertificate },
    { id: 'governance', name: 'DAO Governance', icon: FaVoteYea },
    { id: 'marketplace', name: 'Web3 Marketplace', icon: FaStore },
    { id: 'test', name: 'Test Features', icon: FaShieldAlt }
  ];

  if (!isConnected) {
    return (
      <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-b px-6 py-4`}>
          <h1 className="text-2xl font-bold flex items-center">
            <FaRocket className="mr-3 text-[#88cc14]" />
            Web3 Dashboard
          </h1>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Decentralized cybersecurity education platform
          </p>
        </div>
        
        <div className="p-6">
          {renderWalletConnection()}
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Header */}
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-b px-6 py-4`}>
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <FaRocket className="mr-3 text-[#88cc14]" />
              Web3 Dashboard
            </h1>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Decentralized cybersecurity education platform
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <p className="text-sm text-gray-500">XCYBER Balance</p>
              <p className="font-bold text-[#88cc14]">{parseFloat(tokenBalance).toFixed(2)}</p>
            </div>
            <div className="w-10 h-10 bg-gradient-to-r from-[#88cc14] to-[#7ab512] rounded-full flex items-center justify-center">
              <FaWallet className="text-black" />
            </div>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className={`w-64 ${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-r min-h-screen`}>
          <nav className="p-4">
            <div className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center transition-colors ${
                    activeTab === tab.id
                      ? 'bg-[#88cc14] text-black'
                      : darkMode
                        ? 'hover:bg-[#252D4A] text-gray-300'
                        : 'hover:bg-gray-100 text-gray-700'
                  }`}
                >
                  <tab.icon className="mr-3" />
                  {tab.name}
                </button>
              ))}
            </div>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'staking' && renderStaking()}
          {activeTab === 'certificates' && renderCertificates()}
          {activeTab === 'governance' && (
            <div className="text-center py-12">
              <FaVoteYea className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">DAO Governance</h3>
              <p className="text-gray-500 mb-4">Participate in platform governance and vote on proposals</p>
            </div>
          )}
          {activeTab === 'marketplace' && (
            <div className="text-center py-12">
              <FaStore className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Web3 Marketplace</h3>
              <p className="text-gray-500 mb-4">Buy and sell content using XCYBER tokens</p>
            </div>
          )}
          {activeTab === 'test' && <Web3TestComponent />}
        </div>
      </div>
    </div>
  );
};

export default Web3Dashboard;
