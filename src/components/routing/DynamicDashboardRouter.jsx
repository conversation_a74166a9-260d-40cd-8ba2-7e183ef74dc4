import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useSubscription } from '../../contexts/SubscriptionContext';

/**
 * DynamicDashboardRouter Component
 * 
 * Automatically routes users to the appropriate dashboard based on their role and subscription level.
 * This ensures users always see the most relevant dashboard for their access level.
 */
const DynamicDashboardRouter = () => {
  const navigate = useNavigate();
  const { user, profile } = useAuth();
  const { subscriptionLevel } = useSubscription();

  useEffect(() => {
    const routeToAppropriatedashboard = () => {
      // For development: Temporarily disable restrictions (as requested)
      const isDevelopmentMode = true;

      if (isDevelopmentMode) {
        // In development mode, allow access to any dashboard
        // Default to simplified dashboard for testing
        console.log('🔧 Development Mode: Access restrictions disabled');
        return;
      }

      // Production routing logic (will be enabled later)
      if (!user) {
        navigate('/login');
        return;
      }

      // Super Admin - Full platform management
      if (profile?.role === 'super_admin') {
        navigate('/super-admin');
        return;
      }

      // Admin - Administrative functions
      if (profile?.role === 'admin') {
        navigate('/admin-modern');
        return;
      }

      // Business Users - Team collaboration and advanced features
      if (subscriptionLevel === 'Business') {
        navigate('/enhanced-dashboard');
        return;
      }

      // Premium Users - Advanced features and AI assistance
      if (subscriptionLevel === 'Premium') {
        navigate('/enhanced-dashboard');
        return;
      }

      // Free Users - Basic dashboard
      navigate('/simplified-dashboard');
    };

    routeToAppropriatedashboard();
  }, [user, profile, subscriptionLevel, navigate]);

  return (
    <div className="min-h-screen bg-[#0B1120] flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14] mx-auto mb-4"></div>
        <p className="text-white">Routing to your dashboard...</p>
      </div>
    </div>
  );
};

export default DynamicDashboardRouter;
