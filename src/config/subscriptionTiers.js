/**
 * Subscription tiers configuration
 * This file defines the different subscription tiers and what features are available in each
 */

const SUBSCRIPTION_TIERS = {
  FREE: 'free',
  PREMIUM: 'premium',
  BUSINESS: 'business'
};

// Features available in each subscription tier
const TIER_FEATURES = {
  [SUBSCRIPTION_TIERS.FREE]: {
    name: 'Free',
    price: 0,
    learnModules: {
      access: 'limited', // limited, full
      availableModules: 3, // Number of free modules
      totalModules: 50,
    },
    challenges: {
      access: 'limited',
      availableChallenges: 5, // Number of free challenges
      totalChallenges: 150,
    },
    startHack: {
      access: 'none', // none, limited, full
    },
    dashboard: {
      type: 'basic', // basic, advanced, premium
    },
    features: [
      'Access to 3 basic learning modules',
      '5 beginner challenges',
      'Community forum access',
      'Basic progress tracking',
    ],
  },
  [SUBSCRIPTION_TIERS.PREMIUM]: {
    name: 'Premium',
    price: 399,
    learnModules: {
      access: 'full',
      availableModules: 50, // All modules
      totalModules: 50,
    },
    challenges: {
      access: 'limited',
      availableChallenges: 100, // Most challenges
      totalChallenges: 150,
      // Some challenges require coins
      coinPurchaseRequired: true,
    },
    startHack: {
      access: 'full',
    },
    dashboard: {
      type: 'advanced', // basic, advanced, premium
    },
    features: [
      'Full access to all 50 learning modules',
      '100 challenges across all difficulty levels',
      'Complete access to Start Hack simulations',
      'Advanced dashboard with detailed analytics',
      'Priority community support',
      'Monthly webinars and workshops',
    ],
  },
  [SUBSCRIPTION_TIERS.BUSINESS]: {
    name: 'Business',
    price: 999,
    learnModules: {
      access: 'full',
      availableModules: 50, // All modules
      totalModules: 50,
    },
    challenges: {
      access: 'full',
      availableChallenges: 150, // All challenges
      totalChallenges: 150,
      // No coin purchase required
      coinPurchaseRequired: false,
    },
    startHack: {
      access: 'full',
    },
    dashboard: {
      type: 'premium', // basic, advanced, premium
    },
    features: [
      'Everything in Premium',
      'Unlimited access to all challenges',
      'Team management dashboard',
      'Custom learning paths for teams',
      'Dedicated account manager',
      'Private training sessions',
      'Custom challenge development',
      'Advanced reporting and analytics',
    ],
  },
};

// Virtual currency configuration
const COIN_PACKAGES = [
  {
    id: 'small',
    name: 'Small Pack',
    coins: 100,
    price: 9.99,
    popular: false,
  },
  {
    id: 'medium',
    name: 'Medium Pack',
    coins: 500,
    price: 39.99,
    popular: true,
    bonus: 50, // Bonus coins
  },
  {
    id: 'large',
    name: 'Large Pack',
    coins: 1200,
    price: 79.99,
    popular: false,
    bonus: 200, // Bonus coins
  },
];

// Challenge costs in coins
const CHALLENGE_COSTS = {
  easy: 0, // Free
  medium: 50, // 50 coins
  hard: 100, // 100 coins
  expert: 200, // 200 coins
};

export {
  SUBSCRIPTION_TIERS,
  TIER_FEATURES,
  COIN_PACKAGES,
  CHALLENGE_COSTS,
};
