import { supabase } from '../lib/supabase';

/**
 * Enhanced AI Service
 * 
 * Advanced AI assistant with context awareness, personalized learning,
 * and intelligent tutoring capabilities.
 */
class EnhancedAIService {
  constructor() {
    this.conversationHistory = new Map();
    this.userContext = new Map();
    this.learningPatterns = new Map();
  }

  /**
   * Initialize user context for personalized AI assistance
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User context
   */
  async initializeUserContext(userId) {
    try {
      // Get user profile and learning history
      const [profileResult, progressResult, attemptsResult] = await Promise.all([
        supabase.from('profiles').select('*').eq('id', userId).single(),
        supabase.from('user_progress').select('*').eq('user_id', userId),
        supabase.from('assessment_attempts').select('*').eq('user_id', userId).order('created_at', { ascending: false }).limit(10)
      ]);

      const context = {
        profile: profileResult.data || {},
        learningProgress: progressResult.data || [],
        recentAttempts: attemptsResult.data || [],
        skillLevel: this.calculateSkillLevel(progressResult.data, attemptsResult.data),
        learningStyle: this.detectLearningStyle(progressResult.data),
        weakAreas: this.identifyWeakAreas(attemptsResult.data),
        strongAreas: this.identifyStrongAreas(attemptsResult.data),
        lastActive: new Date().toISOString()
      };

      this.userContext.set(userId, context);
      return context;
    } catch (error) {
      console.error('Error initializing user context:', error);
      return {};
    }
  }

  /**
   * Enhanced chat with context awareness
   * @param {string} userId - User ID
   * @param {string} message - User message
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} AI response with context
   */
  async enhancedChat(userId, message, options = {}) {
    try {
      // Get or initialize user context
      let context = this.userContext.get(userId);
      if (!context) {
        context = await this.initializeUserContext(userId);
      }

      // Analyze message intent and context
      const messageAnalysis = this.analyzeMessage(message, context);
      
      // Get conversation history
      const history = this.conversationHistory.get(userId) || [];
      
      // Generate contextual response
      const response = await this.generateContextualResponse(message, context, messageAnalysis, history);
      
      // Update conversation history
      history.push(
        { role: 'user', content: message, timestamp: new Date().toISOString() },
        { role: 'assistant', content: response.content, timestamp: new Date().toISOString() }
      );
      
      // Keep only last 20 messages
      if (history.length > 20) {
        history.splice(0, history.length - 20);
      }
      
      this.conversationHistory.set(userId, history);
      
      // Update learning patterns
      this.updateLearningPatterns(userId, message, response);
      
      return {
        content: response.content,
        suggestions: response.suggestions,
        resources: response.resources,
        nextSteps: response.nextSteps,
        context: {
          intent: messageAnalysis.intent,
          confidence: messageAnalysis.confidence,
          skillLevel: context.skillLevel,
          personalizedTips: this.getPersonalizedTips(context)
        },
        success: true
      };
    } catch (error) {
      console.error('Error in enhanced chat:', error);
      return {
        content: "I'm having trouble processing your request right now. Please try again.",
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Analyze message intent and extract context
   * @param {string} message - User message
   * @param {Object} context - User context
   * @returns {Object} Message analysis
   */
  analyzeMessage(message, context) {
    const lowerMessage = message.toLowerCase();
    
    // Intent classification
    const intents = {
      question: /\b(what|how|why|when|where|explain|tell me)\b/,
      help: /\b(help|assist|support|stuck|confused)\b/,
      learning: /\b(learn|study|practice|understand|master)\b/,
      assessment: /\b(test|quiz|exam|assessment|evaluate)\b/,
      lab: /\b(lab|hands-on|practice|environment|virtual)\b/,
      certification: /\b(cert|certification|credential|badge)\b/,
      code: /\b(code|script|command|syntax|programming)\b/,
      vulnerability: /\b(vulnerability|exploit|attack|penetration|security)\b/
    };

    let detectedIntent = 'general';
    let confidence = 0.5;

    for (const [intent, pattern] of Object.entries(intents)) {
      if (pattern.test(lowerMessage)) {
        detectedIntent = intent;
        confidence = 0.8;
        break;
      }
    }

    // Extract technical terms
    const technicalTerms = this.extractTechnicalTerms(message);
    
    // Determine complexity level needed
    const complexityLevel = this.determineComplexityLevel(message, context);

    return {
      intent: detectedIntent,
      confidence,
      technicalTerms,
      complexityLevel,
      requiresCode: /\b(code|script|command|example)\b/.test(lowerMessage),
      requiresLab: /\b(hands-on|practice|lab|try)\b/.test(lowerMessage)
    };
  }

  /**
   * Generate contextual AI response
   * @param {string} message - User message
   * @param {Object} context - User context
   * @param {Object} analysis - Message analysis
   * @param {Array} history - Conversation history
   * @returns {Promise<Object>} AI response
   */
  async generateContextualResponse(message, context, analysis, history) {
    // This would integrate with actual AI service (OpenAI, Claude, etc.)
    // For now, we'll create intelligent mock responses based on context
    
    const responses = this.getContextualResponses(message, context, analysis);
    
    return {
      content: responses.main,
      suggestions: responses.suggestions,
      resources: responses.resources,
      nextSteps: responses.nextSteps
    };
  }

  /**
   * Get contextual responses based on user context and intent
   * @param {string} message - User message
   * @param {Object} context - User context
   * @param {Object} analysis - Message analysis
   * @returns {Object} Response components
   */
  getContextualResponses(message, context, analysis) {
    const skillLevel = context.skillLevel || 'beginner';
    const weakAreas = context.weakAreas || [];
    const strongAreas = context.strongAreas || [];

    // Base response templates by intent and skill level
    const responseTemplates = {
      question: {
        beginner: "Great question! Let me explain this in simple terms...",
        intermediate: "That's an important concept. Here's how it works...",
        advanced: "Excellent question. Let's dive into the technical details...",
        expert: "That's a sophisticated question. Here's the comprehensive answer..."
      },
      help: {
        beginner: "I'm here to help! Let's start with the basics...",
        intermediate: "I can definitely help you with that. Based on your progress...",
        advanced: "Let me provide some advanced guidance...",
        expert: "Here's some expert-level assistance..."
      },
      learning: {
        beginner: "Perfect! Let's create a learning path that builds your foundation...",
        intermediate: "Great initiative! Based on your current skills...",
        advanced: "Excellent! Let's focus on advanced concepts...",
        expert: "Let's explore cutting-edge techniques..."
      }
    };

    const baseResponse = responseTemplates[analysis.intent]?.[skillLevel] || 
                       "I understand you're asking about cybersecurity. Let me help you with that.";

    // Add personalized context
    let personalizedResponse = baseResponse;
    
    if (weakAreas.length > 0) {
      personalizedResponse += ` I notice you might want to strengthen your ${weakAreas[0]} skills, so I'll include some relevant examples.`;
    }
    
    if (strongAreas.length > 0) {
      personalizedResponse += ` Given your strong ${strongAreas[0]} background, you might find the advanced aspects particularly interesting.`;
    }

    // Generate suggestions based on context
    const suggestions = this.generateSuggestions(analysis, context);
    
    // Generate relevant resources
    const resources = this.generateResources(analysis, context);
    
    // Generate next steps
    const nextSteps = this.generateNextSteps(analysis, context);

    return {
      main: personalizedResponse + this.getDetailedResponse(message, analysis, context),
      suggestions,
      resources,
      nextSteps
    };
  }

  /**
   * Generate detailed response based on message content
   * @param {string} message - User message
   * @param {Object} analysis - Message analysis
   * @param {Object} context - User context
   * @returns {string} Detailed response
   */
  getDetailedResponse(message, analysis, context) {
    const lowerMessage = message.toLowerCase();
    
    // SQL Injection responses
    if (lowerMessage.includes('sql injection')) {
      return `\n\nSQL injection is a critical web application vulnerability where attackers insert malicious SQL code into application queries. Here's what you need to know:

**How it works:**
1. Attacker finds input field that connects to database
2. Inserts malicious SQL code instead of expected data
3. Database executes the malicious code
4. Attacker gains unauthorized access to data

**Example vulnerable code:**
\`\`\`sql
SELECT * FROM users WHERE username = '${analysis.complexityLevel === 'beginner' ? '[user_input]' : '$_POST[username]'}' AND password = '${analysis.complexityLevel === 'beginner' ? '[password_input]' : '$_POST[password]'}'
\`\`\`

**Prevention methods:**
- Use parameterized queries/prepared statements
- Input validation and sanitization
- Principle of least privilege for database accounts
- Regular security testing

${context.skillLevel === 'beginner' ? 'Would you like me to show you a hands-on lab where you can practice identifying SQL injection vulnerabilities?' : 'I can recommend some advanced SQL injection techniques and bypass methods if you\'re interested.'}`;
    }

    // XSS responses
    if (lowerMessage.includes('xss') || lowerMessage.includes('cross-site scripting')) {
      return `\n\nCross-Site Scripting (XSS) is a vulnerability that allows attackers to inject malicious scripts into web pages viewed by other users.

**Types of XSS:**
1. **Stored XSS**: Malicious script stored on server
2. **Reflected XSS**: Script reflected back in response
3. **DOM-based XSS**: Vulnerability in client-side code

**Example attack:**
\`\`\`html
<script>alert('XSS Attack!');</script>
\`\`\`

**Prevention:**
- Output encoding/escaping
- Content Security Policy (CSP)
- Input validation
- Use secure frameworks

${analysis.requiresLab ? 'I can guide you to our XSS lab environment where you can practice these attacks safely.' : ''}`;
    }

    // Network security responses
    if (lowerMessage.includes('network') && lowerMessage.includes('security')) {
      return `\n\nNetwork security involves protecting the integrity, confidentiality, and availability of network infrastructure and data.

**Key concepts:**
- Firewalls and network segmentation
- Intrusion Detection/Prevention Systems (IDS/IPS)
- VPNs and encrypted communications
- Network monitoring and logging
- Zero-trust architecture

**Common attacks:**
- Man-in-the-middle attacks
- DDoS attacks
- Network sniffing
- ARP spoofing

${context.skillLevel !== 'beginner' ? 'Advanced topics include SDN security, network forensics, and threat hunting techniques.' : ''}`;
    }

    return "\n\nI'd be happy to provide more specific information. What particular aspect would you like to explore further?";
  }

  /**
   * Generate contextual suggestions
   * @param {Object} analysis - Message analysis
   * @param {Object} context - User context
   * @returns {Array} Suggestions
   */
  generateSuggestions(analysis, context) {
    const suggestions = [];
    
    if (analysis.intent === 'learning') {
      suggestions.push("Try our hands-on lab environment");
      suggestions.push("Take a practice assessment");
      suggestions.push("Join a study group");
    }
    
    if (analysis.requiresCode) {
      suggestions.push("View code examples");
      suggestions.push("Practice in our coding environment");
    }
    
    if (context.weakAreas?.length > 0) {
      suggestions.push(`Focus on ${context.weakAreas[0]} fundamentals`);
    }
    
    return suggestions;
  }

  /**
   * Generate relevant resources
   * @param {Object} analysis - Message analysis
   * @param {Object} context - User context
   * @returns {Array} Resources
   */
  generateResources(analysis, context) {
    const resources = [];
    
    if (analysis.technicalTerms.length > 0) {
      resources.push({
        title: "Technical Documentation",
        type: "documentation",
        url: "/docs/" + analysis.technicalTerms[0]
      });
    }
    
    if (analysis.requiresLab) {
      resources.push({
        title: "Hands-on Lab Environment",
        type: "lab",
        url: "/virtual-labs"
      });
    }
    
    resources.push({
      title: "Related Learning Path",
      type: "course",
      url: "/learning-paths"
    });
    
    return resources;
  }

  /**
   * Generate next steps
   * @param {Object} analysis - Message analysis
   * @param {Object} context - User context
   * @returns {Array} Next steps
   */
  generateNextSteps(analysis, context) {
    const steps = [];
    
    if (analysis.intent === 'learning') {
      steps.push("Complete the related assessment");
      steps.push("Practice in virtual lab");
      steps.push("Review additional resources");
    }
    
    if (context.skillLevel === 'beginner') {
      steps.push("Master the fundamentals first");
      steps.push("Take the prerequisite course");
    }
    
    return steps;
  }

  /**
   * Calculate user skill level based on progress and attempts
   * @param {Array} progress - User progress data
   * @param {Array} attempts - Assessment attempts
   * @returns {string} Skill level
   */
  calculateSkillLevel(progress, attempts) {
    if (!attempts || attempts.length === 0) return 'beginner';
    
    const avgScore = attempts.reduce((sum, attempt) => sum + (attempt.score_percentage || 0), 0) / attempts.length;
    const completedCourses = progress?.filter(p => p.completion_percentage >= 80).length || 0;
    
    if (avgScore >= 90 && completedCourses >= 10) return 'expert';
    if (avgScore >= 80 && completedCourses >= 5) return 'advanced';
    if (avgScore >= 70 && completedCourses >= 2) return 'intermediate';
    return 'beginner';
  }

  /**
   * Detect learning style based on user behavior
   * @param {Array} progress - User progress data
   * @returns {string} Learning style
   */
  detectLearningStyle(progress) {
    // Analyze user behavior patterns to detect learning style
    // This would be more sophisticated with actual data analysis
    return 'visual'; // visual, auditory, kinesthetic, reading
  }

  /**
   * Identify weak areas from assessment attempts
   * @param {Array} attempts - Assessment attempts
   * @returns {Array} Weak areas
   */
  identifyWeakAreas(attempts) {
    // Analyze assessment performance to identify weak areas
    return ['web security', 'cryptography']; // Mock data
  }

  /**
   * Identify strong areas from assessment attempts
   * @param {Array} attempts - Assessment attempts
   * @returns {Array} Strong areas
   */
  identifyStrongAreas(attempts) {
    // Analyze assessment performance to identify strong areas
    return ['network security', 'incident response']; // Mock data
  }

  /**
   * Extract technical terms from message
   * @param {string} message - User message
   * @returns {Array} Technical terms
   */
  extractTechnicalTerms(message) {
    const technicalTerms = [
      'sql injection', 'xss', 'csrf', 'buffer overflow', 'privilege escalation',
      'metasploit', 'nmap', 'wireshark', 'burp suite', 'owasp',
      'firewall', 'ids', 'ips', 'vpn', 'encryption', 'hash', 'ssl', 'tls'
    ];
    
    const found = [];
    const lowerMessage = message.toLowerCase();
    
    technicalTerms.forEach(term => {
      if (lowerMessage.includes(term)) {
        found.push(term);
      }
    });
    
    return found;
  }

  /**
   * Determine complexity level needed for response
   * @param {string} message - User message
   * @param {Object} context - User context
   * @returns {string} Complexity level
   */
  determineComplexityLevel(message, context) {
    const advancedKeywords = ['advanced', 'expert', 'complex', 'sophisticated', 'enterprise'];
    const basicKeywords = ['basic', 'simple', 'beginner', 'introduction', 'fundamentals'];
    
    const lowerMessage = message.toLowerCase();
    
    if (advancedKeywords.some(keyword => lowerMessage.includes(keyword))) {
      return 'advanced';
    }
    
    if (basicKeywords.some(keyword => lowerMessage.includes(keyword))) {
      return 'basic';
    }
    
    return context.skillLevel || 'intermediate';
  }

  /**
   * Get personalized tips based on user context
   * @param {Object} context - User context
   * @returns {Array} Personalized tips
   */
  getPersonalizedTips(context) {
    const tips = [];
    
    if (context.weakAreas?.includes('web security')) {
      tips.push("💡 Try our interactive web security labs to strengthen your skills");
    }
    
    if (context.skillLevel === 'beginner') {
      tips.push("🎯 Focus on mastering one concept at a time for better retention");
    }
    
    if (context.recentAttempts?.length > 0) {
      const lastScore = context.recentAttempts[0]?.score_percentage;
      if (lastScore < 70) {
        tips.push("📚 Review the fundamentals before attempting advanced topics");
      }
    }
    
    return tips;
  }

  /**
   * Update learning patterns based on interaction
   * @param {string} userId - User ID
   * @param {string} message - User message
   * @param {Object} response - AI response
   */
  updateLearningPatterns(userId, message, response) {
    const patterns = this.learningPatterns.get(userId) || {
      commonQuestions: [],
      preferredTopics: [],
      interactionCount: 0
    };
    
    patterns.interactionCount++;
    patterns.commonQuestions.push(message);
    
    // Keep only last 50 questions
    if (patterns.commonQuestions.length > 50) {
      patterns.commonQuestions.shift();
    }
    
    this.learningPatterns.set(userId, patterns);
  }

  /**
   * Get learning analytics for user
   * @param {string} userId - User ID
   * @returns {Object} Learning analytics
   */
  getLearningAnalytics(userId) {
    const context = this.userContext.get(userId);
    const patterns = this.learningPatterns.get(userId);
    
    return {
      skillLevel: context?.skillLevel || 'beginner',
      strongAreas: context?.strongAreas || [],
      weakAreas: context?.weakAreas || [],
      interactionCount: patterns?.interactionCount || 0,
      learningStyle: context?.learningStyle || 'unknown',
      recommendations: this.generateRecommendations(context, patterns)
    };
  }

  /**
   * Generate learning recommendations
   * @param {Object} context - User context
   * @param {Object} patterns - Learning patterns
   * @returns {Array} Recommendations
   */
  generateRecommendations(context, patterns) {
    const recommendations = [];
    
    if (context?.weakAreas?.length > 0) {
      recommendations.push({
        type: 'skill_improvement',
        title: `Improve ${context.weakAreas[0]} skills`,
        action: 'Take focused course',
        priority: 'high'
      });
    }
    
    if (patterns?.interactionCount > 10) {
      recommendations.push({
        type: 'assessment',
        title: 'Take skill assessment',
        action: 'Evaluate current knowledge',
        priority: 'medium'
      });
    }
    
    return recommendations;
  }
}

// Export singleton instance
export default new EnhancedAIService();
