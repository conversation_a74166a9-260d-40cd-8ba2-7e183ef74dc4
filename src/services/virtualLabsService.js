import { supabase } from '../lib/supabase';

/**
 * Virtual Labs Service
 * 
 * Comprehensive service for managing live virtual labs and hands-on environments.
 * Handles lab creation, instance management, and user interactions.
 */
class VirtualLabsService {

  /**
   * Get available lab templates
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Lab templates list
   */
  async getLabTemplates(options = {}) {
    try {
      const {
        category,
        difficulty,
        labType,
        accessLevel,
        page = 1,
        limit = 20
      } = options;

      let query = supabase
        .from('lab_templates')
        .select(`
          *,
          content_categories(name, slug),
          lab_resources(id, resource_type, resource_name),
          lab_analytics(
            total_launches, successful_completions, 
            average_duration, completion_rate
          )
        `)
        .eq('is_active', true);

      // Apply filters
      if (category) {
        query = query.eq('content_categories.slug', category);
      }

      if (difficulty) {
        query = query.eq('difficulty_level', difficulty);
      }

      if (labType) {
        query = query.eq('lab_type', labType);
      }

      if (accessLevel) {
        query = query.eq('access_level', accessLevel);
      }

      query = query
        .order('created_at', { ascending: false })
        .range((page - 1) * limit, page * limit - 1);

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        templates: data || [],
        pagination: {
          page,
          limit,
          total: count,
          totalPages: Math.ceil(count / limit)
        },
        success: true
      };
    } catch (error) {
      console.error('Error fetching lab templates:', error);
      return {
        templates: [],
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get lab template by ID
   * @param {string} id - Template ID
   * @returns {Promise<Object>} Template details
   */
  async getLabTemplate(id) {
    try {
      const { data, error } = await supabase
        .from('lab_templates')
        .select(`
          *,
          content_categories(name, slug, description),
          lab_resources(
            id, resource_type, resource_name, 
            description, file_path, download_url
          ),
          lab_analytics(*)
        `)
        .eq('id', id)
        .eq('is_active', true)
        .single();

      if (error) throw error;

      return {
        template: data,
        success: true
      };
    } catch (error) {
      console.error('Error fetching lab template:', error);
      return {
        template: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Launch a new lab instance
   * @param {string} templateId - Template ID
   * @returns {Promise<Object>} Instance details
   */
  async launchLab(templateId) {
    try {
      const user = await supabase.auth.getUser();
      if (!user.data.user) throw new Error('User not authenticated');

      // Check if user has access to this lab type
      const { data: template } = await supabase
        .from('lab_templates')
        .select('access_level, max_concurrent_instances')
        .eq('id', templateId)
        .single();

      if (!template) throw new Error('Lab template not found');

      // Check concurrent instances limit
      const { data: activeInstances } = await supabase
        .from('lab_instances')
        .select('id')
        .eq('template_id', templateId)
        .eq('user_id', user.data.user.id)
        .in('status', ['starting', 'running']);

      if (activeInstances && activeInstances.length >= template.max_concurrent_instances) {
        throw new Error(`Maximum concurrent instances (${template.max_concurrent_instances}) reached`);
      }

      // Create lab instance using database function
      const { data: instanceId, error } = await supabase
        .rpc('create_lab_instance', {
          template_id: templateId,
          user_id: user.data.user.id
        });

      if (error) throw error;

      // Simulate container/VM creation (in real implementation, this would call Docker/K8s API)
      await this.simulateInstanceCreation(instanceId);

      // Get the created instance
      const { data: instance } = await supabase
        .from('lab_instances')
        .select('*')
        .eq('id', instanceId)
        .single();

      return {
        instance,
        success: true
      };
    } catch (error) {
      console.error('Error launching lab:', error);
      return {
        instance: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Simulate instance creation (placeholder for real container orchestration)
   * @param {string} instanceId - Instance ID
   */
  async simulateInstanceCreation(instanceId) {
    try {
      // Simulate startup delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate mock access details
      const accessUrl = `https://lab-${instanceId.substring(0, 8)}.xceberus.com`;
      const vncUrl = `${accessUrl}/vnc`;
      const sshPort = Math.floor(Math.random() * 10000) + 20000;

      // Update instance with access details
      await supabase
        .from('lab_instances')
        .update({
          status: 'running',
          access_url: accessUrl,
          vnc_url: vncUrl,
          ssh_port: sshPort,
          internal_ip: '10.0.0.' + Math.floor(Math.random() * 254 + 1),
          external_ip: '203.0.113.' + Math.floor(Math.random() * 254 + 1)
        })
        .eq('id', instanceId);

    } catch (error) {
      console.error('Error simulating instance creation:', error);
      
      // Mark instance as error
      await supabase
        .from('lab_instances')
        .update({ status: 'error' })
        .eq('id', instanceId);
    }
  }

  /**
   * Get user's lab instances
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Instances list
   */
  async getUserInstances(options = {}) {
    try {
      const { status, page = 1, limit = 20 } = options;
      const user = await supabase.auth.getUser();
      
      if (!user.data.user) throw new Error('User not authenticated');

      let query = supabase
        .from('lab_instances')
        .select(`
          *,
          lab_templates(name, description, lab_type, difficulty_level),
          lab_sessions(id, started_at, ended_at, completion_percentage)
        `)
        .eq('user_id', user.data.user.id);

      if (status) {
        query = query.eq('status', status);
      }

      query = query
        .order('created_at', { ascending: false })
        .range((page - 1) * limit, page * limit - 1);

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        instances: data || [],
        pagination: {
          page,
          limit,
          total: count,
          totalPages: Math.ceil(count / limit)
        },
        success: true
      };
    } catch (error) {
      console.error('Error fetching user instances:', error);
      return {
        instances: [],
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Connect to lab instance
   * @param {string} instanceId - Instance ID
   * @returns {Promise<Object>} Connection details
   */
  async connectToLab(instanceId) {
    try {
      const user = await supabase.auth.getUser();
      if (!user.data.user) throw new Error('User not authenticated');

      // Get instance details
      const { data: instance, error } = await supabase
        .from('lab_instances')
        .select('*')
        .eq('id', instanceId)
        .eq('user_id', user.data.user.id)
        .single();

      if (error) throw error;

      if (instance.status !== 'running') {
        throw new Error(`Lab instance is ${instance.status}, cannot connect`);
      }

      // Create or get existing session
      let session = await this.getOrCreateSession(instanceId);

      // Update last accessed time
      await supabase
        .from('lab_instances')
        .update({ last_accessed: new Date().toISOString() })
        .eq('id', instanceId);

      return {
        instance,
        session,
        connectionDetails: {
          accessUrl: instance.access_url,
          vncUrl: instance.vnc_url,
          sshPort: instance.ssh_port,
          sessionToken: session.session_token
        },
        success: true
      };
    } catch (error) {
      console.error('Error connecting to lab:', error);
      return {
        instance: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get or create lab session
   * @param {string} instanceId - Instance ID
   * @returns {Promise<Object>} Session details
   */
  async getOrCreateSession(instanceId) {
    try {
      const user = await supabase.auth.getUser();
      
      // Check for existing active session
      const { data: existingSession } = await supabase
        .from('lab_sessions')
        .select('*')
        .eq('instance_id', instanceId)
        .eq('user_id', user.data.user.id)
        .is('ended_at', null)
        .single();

      if (existingSession) {
        return existingSession;
      }

      // Create new session
      const sessionToken = this.generateSessionToken();
      
      const { data: newSession, error } = await supabase
        .from('lab_sessions')
        .insert({
          instance_id: instanceId,
          user_id: user.data.user.id,
          session_token: sessionToken
        })
        .select()
        .single();

      if (error) throw error;

      return newSession;
    } catch (error) {
      console.error('Error creating lab session:', error);
      throw error;
    }
  }

  /**
   * Track lab activity
   * @param {string} sessionId - Session ID
   * @param {string} activityType - Activity type
   * @param {Object} activityData - Activity data
   * @returns {Promise<Object>} Tracking result
   */
  async trackActivity(sessionId, activityType, activityData) {
    try {
      const { data, error } = await supabase
        .rpc('track_lab_activity', {
          session_id: sessionId,
          activity_type: activityType,
          activity_data: activityData.data || {},
          working_directory: activityData.workingDirectory,
          exit_code: activityData.exitCode,
          output_text: activityData.output
        });

      if (error) throw error;

      return {
        activityId: data,
        success: true
      };
    } catch (error) {
      console.error('Error tracking lab activity:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Stop lab instance
   * @param {string} instanceId - Instance ID
   * @returns {Promise<Object>} Stop result
   */
  async stopLab(instanceId) {
    try {
      const user = await supabase.auth.getUser();
      
      // Update instance status
      const { data, error } = await supabase
        .from('lab_instances')
        .update({
          status: 'stopped',
          updated_at: new Date().toISOString()
        })
        .eq('id', instanceId)
        .eq('user_id', user.data.user.id)
        .select()
        .single();

      if (error) throw error;

      // End active sessions
      await supabase
        .from('lab_sessions')
        .update({
          ended_at: new Date().toISOString(),
          duration: supabase.sql`EXTRACT(EPOCH FROM (NOW() - started_at))`
        })
        .eq('instance_id', instanceId)
        .is('ended_at', null);

      return {
        instance: data,
        success: true
      };
    } catch (error) {
      console.error('Error stopping lab:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get lab session details
   * @param {string} sessionId - Session ID
   * @returns {Promise<Object>} Session details
   */
  async getSession(sessionId) {
    try {
      const { data, error } = await supabase
        .from('lab_sessions')
        .select(`
          *,
          lab_instances(
            id, instance_name, status, access_url, vnc_url,
            lab_templates(name, instructions, learning_objectives)
          ),
          lab_activities(
            id, activity_type, activity_data, timestamp,
            is_successful, output_text
          )
        `)
        .eq('id', sessionId)
        .single();

      if (error) throw error;

      return {
        session: data,
        success: true
      };
    } catch (error) {
      console.error('Error fetching session:', error);
      return {
        session: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get lab analytics
   * @param {string} templateId - Template ID
   * @returns {Promise<Object>} Analytics data
   */
  async getLabAnalytics(templateId) {
    try {
      const { data: analytics, error } = await supabase
        .from('lab_analytics')
        .select('*')
        .eq('template_id', templateId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      // Get recent sessions for trends
      const { data: recentSessions } = await supabase
        .from('lab_sessions')
        .select(`
          completion_percentage, duration, started_at,
          lab_instances!inner(template_id)
        `)
        .eq('lab_instances.template_id', templateId)
        .not('ended_at', 'is', null)
        .order('started_at', { ascending: false })
        .limit(100);

      const trends = this.calculateUsageTrends(recentSessions || []);

      return {
        analytics: analytics || {
          total_launches: 0,
          successful_completions: 0,
          average_duration: 0,
          completion_rate: 0
        },
        trends,
        success: true
      };
    } catch (error) {
      console.error('Error fetching lab analytics:', error);
      return {
        analytics: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Calculate usage trends from session data
   * @param {Array} sessions - Session data
   * @returns {Object} Trends data
   */
  calculateUsageTrends(sessions) {
    if (!sessions || sessions.length === 0) {
      return { usageTrend: [], completionTrend: [] };
    }

    // Group by day
    const dailyData = new Map();
    
    sessions.forEach(session => {
      const date = new Date(session.started_at).toISOString().split('T')[0];
      if (!dailyData.has(date)) {
        dailyData.set(date, { 
          sessions: 0, 
          completions: 0, 
          totalDuration: 0,
          completionPercentages: []
        });
      }
      
      const dayData = dailyData.get(date);
      dayData.sessions++;
      dayData.totalDuration += session.duration || 0;
      dayData.completionPercentages.push(session.completion_percentage || 0);
      
      if (session.completion_percentage >= 80) {
        dayData.completions++;
      }
    });

    // Convert to trend arrays
    const usageTrend = Array.from(dailyData.entries()).map(([date, data]) => ({
      date,
      sessions: data.sessions,
      averageDuration: data.totalDuration / data.sessions / 60, // in minutes
      completionRate: (data.completions / data.sessions) * 100
    }));

    return {
      usageTrend: usageTrend.sort((a, b) => a.date.localeCompare(b.date))
    };
  }

  /**
   * Generate session token
   * @returns {string} Session token
   */
  generateSessionToken() {
    return 'lab_' + Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  /**
   * Cleanup expired instances
   * @returns {Promise<Object>} Cleanup result
   */
  async cleanupExpiredInstances() {
    try {
      const { data, error } = await supabase
        .rpc('cleanup_expired_instances');

      if (error) throw error;

      return {
        cleanedUp: data,
        success: true
      };
    } catch (error) {
      console.error('Error cleaning up expired instances:', error);
      return {
        cleanedUp: 0,
        success: false,
        error: error.message
      };
    }
  }
}

// Export singleton instance
export default new VirtualLabsService();
