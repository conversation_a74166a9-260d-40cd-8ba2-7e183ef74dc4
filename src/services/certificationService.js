import { supabase } from '../lib/supabase';

/**
 * Certification Service
 * 
 * Comprehensive service for managing certifications, tracking progress,
 * and handling industry recognition.
 */
class CertificationService {

  /**
   * Get available certification programs
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Programs list
   */
  async getCertificationPrograms(options = {}) {
    try {
      const {
        category,
        level,
        active = true,
        page = 1,
        limit = 20
      } = options;

      let query = supabase
        .from('certification_programs')
        .select(`
          *,
          certification_requirements(
            id, requirement_type, requirement_name, 
            minimum_score, is_optional
          ),
          certification_analytics(
            total_enrolled, total_certified, pass_rate, average_score
          )
        `);

      if (active) {
        query = query.eq('is_active', true);
      }

      if (category) {
        query = query.eq('category', category);
      }

      if (level) {
        query = query.eq('level', level);
      }

      query = query
        .order('created_at', { ascending: false })
        .range((page - 1) * limit, page * limit - 1);

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        programs: data || [],
        pagination: {
          page,
          limit,
          total: count,
          totalPages: Math.ceil(count / limit)
        },
        success: true
      };
    } catch (error) {
      console.error('Error fetching certification programs:', error);
      return {
        programs: [],
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get certification program by ID
   * @param {string} id - Program ID
   * @returns {Promise<Object>} Program details
   */
  async getCertificationProgram(id) {
    try {
      const { data, error } = await supabase
        .from('certification_programs')
        .select(`
          *,
          certification_requirements(
            id, requirement_type, requirement_name, description,
            minimum_score, weight, is_optional, sort_order
          ),
          certification_analytics(*)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      // Sort requirements by order
      if (data.certification_requirements) {
        data.certification_requirements.sort((a, b) => a.sort_order - b.sort_order);
      }

      return {
        program: data,
        success: true
      };
    } catch (error) {
      console.error('Error fetching certification program:', error);
      return {
        program: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get user's certification progress
   * @param {string} programId - Program ID
   * @param {string} userId - User ID (optional, defaults to current user)
   * @returns {Promise<Object>} Progress details
   */
  async getCertificationProgress(programId, userId = null) {
    try {
      const user = userId || (await supabase.auth.getUser()).data.user?.id;
      if (!user) throw new Error('User not authenticated');

      // Get progress records
      const { data: progress, error: progressError } = await supabase
        .from('certification_progress')
        .select(`
          *,
          certification_requirements(
            requirement_name, requirement_type, description,
            minimum_score, weight, is_optional
          )
        `)
        .eq('user_id', user)
        .eq('program_id', programId);

      if (progressError) throw progressError;

      // Check eligibility
      const { data: eligibility, error: eligibilityError } = await supabase
        .rpc('check_certification_eligibility', {
          user_id: user,
          program_id: programId
        });

      if (eligibilityError) throw eligibilityError;

      return {
        progress: progress || [],
        eligibility,
        success: true
      };
    } catch (error) {
      console.error('Error fetching certification progress:', error);
      return {
        progress: [],
        eligibility: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Enroll in certification program
   * @param {string} programId - Program ID
   * @returns {Promise<Object>} Enrollment result
   */
  async enrollInProgram(programId) {
    try {
      const user = await supabase.auth.getUser();
      if (!user.data.user) throw new Error('User not authenticated');

      // Get program requirements
      const { data: requirements } = await supabase
        .from('certification_requirements')
        .select('*')
        .eq('program_id', programId);

      // Create progress records for all requirements
      const progressRecords = requirements.map(req => ({
        user_id: user.data.user.id,
        program_id: programId,
        requirement_id: req.id,
        status: 'not_started'
      }));

      const { data, error } = await supabase
        .from('certification_progress')
        .upsert(progressRecords, { 
          onConflict: 'user_id,program_id,requirement_id',
          ignoreDuplicates: true 
        })
        .select();

      if (error) throw error;

      return {
        enrollment: data,
        success: true
      };
    } catch (error) {
      console.error('Error enrolling in program:', error);
      return {
        enrollment: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update progress for a requirement
   * @param {string} programId - Program ID
   * @param {string} requirementId - Requirement ID
   * @param {Object} progressData - Progress data
   * @returns {Promise<Object>} Update result
   */
  async updateProgress(programId, requirementId, progressData) {
    try {
      const user = await supabase.auth.getUser();
      if (!user.data.user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('certification_progress')
        .upsert({
          user_id: user.data.user.id,
          program_id: programId,
          requirement_id: requirementId,
          ...progressData,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      return {
        progress: data,
        success: true
      };
    } catch (error) {
      console.error('Error updating progress:', error);
      return {
        progress: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Award certification to user
   * @param {string} userId - User ID
   * @param {string} programId - Program ID
   * @param {number} finalScore - Final score
   * @returns {Promise<Object>} Award result
   */
  async awardCertification(userId, programId, finalScore) {
    try {
      const { data: certificationId, error } = await supabase
        .rpc('award_certification', {
          user_id: userId,
          program_id: programId,
          final_score: finalScore
        });

      if (error) throw error;

      // Get the created certification
      const { data: certification } = await supabase
        .from('user_certifications')
        .select(`
          *,
          certification_programs(name, code, badge_image_url)
        `)
        .eq('id', certificationId)
        .single();

      return {
        certification,
        success: true
      };
    } catch (error) {
      console.error('Error awarding certification:', error);
      return {
        certification: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get user's certifications
   * @param {string} userId - User ID (optional, defaults to current user)
   * @returns {Promise<Object>} Certifications list
   */
  async getUserCertifications(userId = null) {
    try {
      const user = userId || (await supabase.auth.getUser()).data.user?.id;
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('user_certifications')
        .select(`
          *,
          certification_programs(
            name, code, description, level, category,
            badge_image_url, validity_period_months
          )
        `)
        .eq('user_id', user)
        .order('issued_date', { ascending: false });

      if (error) throw error;

      return {
        certifications: data || [],
        success: true
      };
    } catch (error) {
      console.error('Error fetching user certifications:', error);
      return {
        certifications: [],
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Verify certification
   * @param {string} certificateNumber - Certificate number
   * @param {string} verificationCode - Verification code
   * @returns {Promise<Object>} Verification result
   */
  async verifyCertification(certificateNumber, verificationCode) {
    try {
      const { data, error } = await supabase
        .from('user_certifications')
        .select(`
          *,
          certification_programs(name, code, level, category),
          profiles:user_id(username, full_name)
        `)
        .eq('certificate_number', certificateNumber)
        .eq('verification_code', verificationCode)
        .eq('status', 'active')
        .single();

      if (error) {
        return {
          valid: false,
          certification: null,
          success: true,
          message: 'Certificate not found or invalid'
        };
      }

      // Check if certificate is expired
      const isExpired = new Date(data.expiry_date) < new Date();

      return {
        valid: !isExpired,
        certification: data,
        expired: isExpired,
        success: true
      };
    } catch (error) {
      console.error('Error verifying certification:', error);
      return {
        valid: false,
        certification: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Schedule certification exam
   * @param {string} programId - Program ID
   * @param {string} assessmentId - Assessment ID
   * @param {Date} scheduledDate - Scheduled date
   * @returns {Promise<Object>} Scheduling result
   */
  async scheduleExam(programId, assessmentId, scheduledDate) {
    try {
      const user = await supabase.auth.getUser();
      if (!user.data.user) throw new Error('User not authenticated');

      // Generate session code
      const sessionCode = this.generateSessionCode();

      const { data, error } = await supabase
        .from('certification_exam_sessions')
        .insert({
          user_id: user.data.user.id,
          program_id: programId,
          assessment_id: assessmentId,
          session_code: sessionCode,
          scheduled_date: scheduledDate,
          status: 'scheduled'
        })
        .select()
        .single();

      if (error) throw error;

      return {
        examSession: data,
        success: true
      };
    } catch (error) {
      console.error('Error scheduling exam:', error);
      return {
        examSession: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get certification analytics
   * @param {string} programId - Program ID
   * @returns {Promise<Object>} Analytics data
   */
  async getCertificationAnalytics(programId) {
    try {
      const { data: analytics, error } = await supabase
        .from('certification_analytics')
        .select('*')
        .eq('program_id', programId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      // Get recent certifications for trends
      const { data: recentCertifications } = await supabase
        .from('user_certifications')
        .select('issued_date, final_score, grade')
        .eq('program_id', programId)
        .order('issued_date', { ascending: false })
        .limit(100);

      const trends = this.calculateCertificationTrends(recentCertifications || []);

      return {
        analytics: analytics || {
          total_enrolled: 0,
          total_certified: 0,
          pass_rate: 0,
          average_score: 0
        },
        trends,
        success: true
      };
    } catch (error) {
      console.error('Error fetching certification analytics:', error);
      return {
        analytics: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Add continuing education activity
   * @param {string} certificationId - Certification ID
   * @param {Object} activityData - Activity data
   * @returns {Promise<Object>} Activity result
   */
  async addContinuingEducation(certificationId, activityData) {
    try {
      const user = await supabase.auth.getUser();
      if (!user.data.user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('continuing_education')
        .insert({
          user_id: user.data.user.id,
          certification_id: certificationId,
          ...activityData
        })
        .select()
        .single();

      if (error) throw error;

      return {
        activity: data,
        success: true
      };
    } catch (error) {
      console.error('Error adding continuing education:', error);
      return {
        activity: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Calculate certification trends
   * @param {Array} certifications - Certification data
   * @returns {Object} Trends data
   */
  calculateCertificationTrends(certifications) {
    if (!certifications || certifications.length === 0) {
      return { issuanceTrend: [], scoreTrend: [] };
    }

    // Group by month
    const monthlyData = new Map();
    
    certifications.forEach(cert => {
      const month = new Date(cert.issued_date).toISOString().substring(0, 7); // YYYY-MM
      if (!monthlyData.has(month)) {
        monthlyData.set(month, { 
          issued: 0, 
          scores: [],
          grades: { 'A+': 0, 'A': 0, 'B+': 0, 'B': 0, 'C+': 0, 'C': 0 }
        });
      }
      
      const monthData = monthlyData.get(month);
      monthData.issued++;
      monthData.scores.push(cert.final_score);
      if (cert.grade) {
        monthData.grades[cert.grade] = (monthData.grades[cert.grade] || 0) + 1;
      }
    });

    // Convert to trend arrays
    const issuanceTrend = Array.from(monthlyData.entries()).map(([month, data]) => ({
      month,
      issued: data.issued,
      averageScore: data.scores.reduce((sum, score) => sum + score, 0) / data.scores.length
    }));

    return {
      issuanceTrend: issuanceTrend.sort((a, b) => a.month.localeCompare(b.month))
    };
  }

  /**
   * Generate session code
   * @returns {string} Session code
   */
  generateSessionCode() {
    return 'EXAM-' + Math.random().toString(36).substring(2, 8).toUpperCase();
  }

  /**
   * Get expiring certifications
   * @param {number} daysAhead - Days to look ahead
   * @returns {Promise<Object>} Expiring certifications
   */
  async getExpiringCertifications(daysAhead = 90) {
    try {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + daysAhead);

      const { data, error } = await supabase
        .from('user_certifications')
        .select(`
          *,
          certification_programs(name, code),
          profiles:user_id(username, email, full_name)
        `)
        .eq('status', 'active')
        .gte('expiry_date', new Date().toISOString().split('T')[0])
        .lte('expiry_date', futureDate.toISOString().split('T')[0])
        .order('expiry_date', { ascending: true });

      if (error) throw error;

      return {
        expiringCertifications: data || [],
        success: true
      };
    } catch (error) {
      console.error('Error fetching expiring certifications:', error);
      return {
        expiringCertifications: [],
        success: false,
        error: error.message
      };
    }
  }
}

// Export singleton instance
export default new CertificationService();
