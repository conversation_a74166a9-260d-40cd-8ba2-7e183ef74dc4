import { supabase } from '../lib/supabase';

/**
 * Content Management Service
 * 
 * Comprehensive service for managing dynamic learning materials.
 * Handles content creation, editing, publishing, and analytics.
 */
class ContentManagementService {

  /**
   * Get all content with filtering and pagination
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Content list with metadata
   */
  async getContent(options = {}) {
    try {
      const {
        category,
        contentType,
        accessLevel,
        difficulty,
        tags,
        search,
        published = true,
        page = 1,
        limit = 20,
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = options;

      let query = supabase
        .from('learning_content')
        .select(`
          *,
          content_categories(name, slug),
          content_types(name, display_name),
          profiles:author_id(username, avatar_url),
          content_blocks(id, block_type, sort_order),
          media_assets:content_media(
            media_assets(id, filename, cdn_url, file_type)
          )
        `);

      // Apply filters
      if (published) {
        query = query.eq('is_published', true);
      }

      if (category) {
        query = query.eq('content_categories.slug', category);
      }

      if (contentType) {
        query = query.eq('content_types.name', contentType);
      }

      if (accessLevel) {
        query = query.eq('access_level', accessLevel);
      }

      if (difficulty) {
        query = query.eq('difficulty_level', difficulty);
      }

      if (tags && tags.length > 0) {
        query = query.overlaps('tags', tags);
      }

      if (search) {
        query = query.or(`title.ilike.%${search}%, excerpt.ilike.%${search}%`);
      }

      // Apply sorting and pagination
      query = query
        .order(sortBy, { ascending: sortOrder === 'asc' })
        .range((page - 1) * limit, page * limit - 1);

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        content: data || [],
        pagination: {
          page,
          limit,
          total: count,
          totalPages: Math.ceil(count / limit)
        },
        success: true
      };
    } catch (error) {
      console.error('Error fetching content:', error);
      return {
        content: [],
        pagination: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get single content item with full details
   * @param {string} id - Content ID or slug
   * @returns {Promise<Object>} Content details
   */
  async getContentById(id) {
    try {
      const { data, error } = await supabase
        .from('learning_content')
        .select(`
          *,
          content_categories(name, slug, description),
          content_types(name, display_name, schema_definition),
          profiles:author_id(username, avatar_url, bio),
          content_blocks(
            id, block_type, block_data, sort_order
          ),
          content_media(
            media_assets(id, filename, cdn_url, file_type, alt_text, caption)
          )
        `)
        .or(`id.eq.${id}, slug.eq.${id}`)
        .single();

      if (error) throw error;

      // Track content view
      await this.trackContentEvent(data.id, 'view');

      // Increment view count
      await supabase
        .from('learning_content')
        .update({ view_count: data.view_count + 1 })
        .eq('id', data.id);

      return {
        content: data,
        success: true
      };
    } catch (error) {
      console.error('Error fetching content by ID:', error);
      return {
        content: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create new content
   * @param {Object} contentData - Content data
   * @returns {Promise<Object>} Created content
   */
  async createContent(contentData) {
    try {
      const {
        title,
        contentTypeId,
        categoryId,
        contentData: content,
        metadata = {},
        difficultyLevel = 'beginner',
        estimatedDuration,
        prerequisites = [],
        learningObjectives = [],
        tags = [],
        accessLevel = 'free',
        excerpt,
        featuredImageUrl
      } = contentData;

      // Generate slug from title
      const slug = title.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');

      const { data, error } = await supabase
        .from('learning_content')
        .insert({
          title,
          slug,
          content_type_id: contentTypeId,
          category_id: categoryId,
          content_data: content,
          metadata,
          difficulty_level: difficultyLevel,
          estimated_duration: estimatedDuration,
          prerequisites,
          learning_objectives: learningObjectives,
          tags,
          access_level: accessLevel,
          excerpt,
          featured_image_url: featuredImageUrl,
          author_id: (await supabase.auth.getUser()).data.user?.id
        })
        .select()
        .single();

      if (error) throw error;

      return {
        content: data,
        success: true
      };
    } catch (error) {
      console.error('Error creating content:', error);
      return {
        content: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update existing content
   * @param {string} id - Content ID
   * @param {Object} updates - Content updates
   * @returns {Promise<Object>} Updated content
   */
  async updateContent(id, updates) {
    try {
      const { data, error } = await supabase
        .from('learning_content')
        .update({
          ...updates,
          last_modified_by: (await supabase.auth.getUser()).data.user?.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return {
        content: data,
        success: true
      };
    } catch (error) {
      console.error('Error updating content:', error);
      return {
        content: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Publish content
   * @param {string} id - Content ID
   * @returns {Promise<Object>} Publication result
   */
  async publishContent(id) {
    try {
      const { data, error } = await supabase
        .from('learning_content')
        .update({
          is_published: true,
          publish_date: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return {
        content: data,
        success: true
      };
    } catch (error) {
      console.error('Error publishing content:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Add content block to content
   * @param {string} contentId - Content ID
   * @param {Object} blockData - Block data
   * @returns {Promise<Object>} Created block
   */
  async addContentBlock(contentId, blockData) {
    try {
      const { data, error } = await supabase
        .from('content_blocks')
        .insert({
          content_id: contentId,
          ...blockData
        })
        .select()
        .single();

      if (error) throw error;

      return {
        block: data,
        success: true
      };
    } catch (error) {
      console.error('Error adding content block:', error);
      return {
        block: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Upload media asset
   * @param {File} file - File to upload
   * @param {Object} metadata - File metadata
   * @returns {Promise<Object>} Upload result
   */
  async uploadMedia(file, metadata = {}) {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `content-media/${fileName}`;

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('media-assets')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('media-assets')
        .getPublicUrl(filePath);

      // Save media record
      const { data, error } = await supabase
        .from('media_assets')
        .insert({
          filename: fileName,
          original_filename: file.name,
          file_type: fileExt,
          file_size: file.size,
          mime_type: file.type,
          storage_path: filePath,
          cdn_url: publicUrl,
          ...metadata,
          upload_by: (await supabase.auth.getUser()).data.user?.id
        })
        .select()
        .single();

      if (error) throw error;

      return {
        media: data,
        success: true
      };
    } catch (error) {
      console.error('Error uploading media:', error);
      return {
        media: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Track content events for analytics
   * @param {string} contentId - Content ID
   * @param {string} eventType - Event type
   * @param {Object} eventData - Additional event data
   */
  async trackContentEvent(contentId, eventType, eventData = {}) {
    try {
      const user = await supabase.auth.getUser();
      
      await supabase
        .from('content_analytics')
        .insert({
          content_id: contentId,
          user_id: user.data.user?.id,
          event_type: eventType,
          event_data: eventData,
          session_id: sessionStorage.getItem('session_id') || 'anonymous'
        });
    } catch (error) {
      console.error('Error tracking content event:', error);
    }
  }

  /**
   * Get content analytics
   * @param {string} contentId - Content ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Analytics data
   */
  async getContentAnalytics(contentId, options = {}) {
    try {
      const { timeframe = '30d' } = options;
      
      let startDate = new Date();
      switch (timeframe) {
        case '7d':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(startDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(startDate.getDate() - 90);
          break;
      }

      const { data, error } = await supabase
        .from('content_analytics')
        .select('event_type, timestamp, event_data')
        .eq('content_id', contentId)
        .gte('timestamp', startDate.toISOString())
        .order('timestamp', { ascending: false });

      if (error) throw error;

      // Process analytics data
      const analytics = {
        totalViews: data.filter(event => event.event_type === 'view').length,
        totalStarts: data.filter(event => event.event_type === 'start').length,
        totalCompletions: data.filter(event => event.event_type === 'complete').length,
        engagementRate: 0,
        completionRate: 0,
        timeSeriesData: this.processTimeSeriesData(data)
      };

      analytics.engagementRate = analytics.totalViews > 0 
        ? (analytics.totalStarts / analytics.totalViews * 100).toFixed(2)
        : 0;

      analytics.completionRate = analytics.totalStarts > 0
        ? (analytics.totalCompletions / analytics.totalStarts * 100).toFixed(2)
        : 0;

      return {
        analytics,
        success: true
      };
    } catch (error) {
      console.error('Error fetching content analytics:', error);
      return {
        analytics: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Process time series data for charts
   * @param {Array} data - Raw analytics data
   * @returns {Array} Processed time series data
   */
  processTimeSeriesData(data) {
    const timeSeriesMap = new Map();
    
    data.forEach(event => {
      const date = new Date(event.timestamp).toISOString().split('T')[0];
      if (!timeSeriesMap.has(date)) {
        timeSeriesMap.set(date, { date, views: 0, starts: 0, completions: 0 });
      }
      
      const dayData = timeSeriesMap.get(date);
      switch (event.event_type) {
        case 'view':
          dayData.views++;
          break;
        case 'start':
          dayData.starts++;
          break;
        case 'complete':
          dayData.completions++;
          break;
      }
    });

    return Array.from(timeSeriesMap.values()).sort((a, b) => a.date.localeCompare(b.date));
  }

  /**
   * Get content categories
   * @returns {Promise<Object>} Categories list
   */
  async getCategories() {
    try {
      const { data, error } = await supabase
        .from('content_categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) throw error;

      return {
        categories: data || [],
        success: true
      };
    } catch (error) {
      console.error('Error fetching categories:', error);
      return {
        categories: [],
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get content types
   * @returns {Promise<Object>} Content types list
   */
  async getContentTypes() {
    try {
      const { data, error } = await supabase
        .from('content_types')
        .select('*')
        .eq('is_active', true);

      if (error) throw error;

      return {
        contentTypes: data || [],
        success: true
      };
    } catch (error) {
      console.error('Error fetching content types:', error);
      return {
        contentTypes: [],
        success: false,
        error: error.message
      };
    }
  }
}

}

// Export singleton instance
export default new ContentManagementService();
