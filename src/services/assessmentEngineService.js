import { supabase } from '../lib/supabase';

/**
 * Assessment Engine Service
 * 
 * Comprehensive service for managing assessments, automated grading,
 * and performance analytics.
 */
class AssessmentEngineService {

  /**
   * Get all assessments with filtering
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Assessments list
   */
  async getAssessments(options = {}) {
    try {
      const {
        type,
        published = true,
        available = true,
        page = 1,
        limit = 20
      } = options;

      let query = supabase
        .from('assessments')
        .select(`
          *,
          assessment_types(name, display_name),
          profiles:created_by(username, avatar_url),
          assessment_questions_map(
            assessment_questions(id, question_type, points)
          )
        `);

      if (published) {
        query = query.eq('is_published', true);
      }

      if (available) {
        const now = new Date().toISOString();
        query = query
          .or(`available_from.is.null,available_from.lte.${now}`)
          .or(`available_until.is.null,available_until.gte.${now}`);
      }

      if (type) {
        query = query.eq('assessment_types.name', type);
      }

      query = query
        .order('created_at', { ascending: false })
        .range((page - 1) * limit, page * limit - 1);

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        assessments: data || [],
        pagination: {
          page,
          limit,
          total: count,
          totalPages: Math.ceil(count / limit)
        },
        success: true
      };
    } catch (error) {
      console.error('Error fetching assessments:', error);
      return {
        assessments: [],
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get assessment by ID with questions
   * @param {string} id - Assessment ID
   * @returns {Promise<Object>} Assessment details
   */
  async getAssessmentById(id) {
    try {
      const { data, error } = await supabase
        .from('assessments')
        .select(`
          *,
          assessment_types(name, display_name, grading_method),
          assessment_questions_map(
            sort_order,
            points_override,
            assessment_questions(
              id, question_type, question_text, question_data,
              points, explanation, hints
            )
          )
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      // Sort questions by order
      if (data.assessment_questions_map) {
        data.assessment_questions_map.sort((a, b) => a.sort_order - b.sort_order);
      }

      return {
        assessment: data,
        success: true
      };
    } catch (error) {
      console.error('Error fetching assessment:', error);
      return {
        assessment: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Start assessment attempt
   * @param {string} assessmentId - Assessment ID
   * @returns {Promise<Object>} Attempt details
   */
  async startAssessment(assessmentId) {
    try {
      const user = await supabase.auth.getUser();
      if (!user.data.user) throw new Error('User not authenticated');

      // Check existing attempts
      const { data: existingAttempts } = await supabase
        .from('assessment_attempts')
        .select('attempt_number')
        .eq('assessment_id', assessmentId)
        .eq('user_id', user.data.user.id)
        .order('attempt_number', { ascending: false });

      // Get assessment details for max attempts check
      const { data: assessment } = await supabase
        .from('assessments')
        .select('max_attempts, title')
        .eq('id', assessmentId)
        .single();

      const nextAttemptNumber = (existingAttempts?.[0]?.attempt_number || 0) + 1;

      if (nextAttemptNumber > assessment.max_attempts) {
        throw new Error(`Maximum attempts (${assessment.max_attempts}) exceeded`);
      }

      // Create new attempt
      const { data, error } = await supabase
        .from('assessment_attempts')
        .insert({
          assessment_id: assessmentId,
          user_id: user.data.user.id,
          attempt_number: nextAttemptNumber,
          status: 'in_progress'
        })
        .select()
        .single();

      if (error) throw error;

      return {
        attempt: data,
        success: true
      };
    } catch (error) {
      console.error('Error starting assessment:', error);
      return {
        attempt: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Submit question response
   * @param {string} attemptId - Attempt ID
   * @param {string} questionId - Question ID
   * @param {Object} responseData - Response data
   * @returns {Promise<Object>} Response result
   */
  async submitResponse(attemptId, questionId, responseData) {
    try {
      // Insert or update response
      const { data, error } = await supabase
        .from('assessment_responses')
        .upsert({
          attempt_id: attemptId,
          question_id: questionId,
          response_data: responseData.data || {},
          response_text: responseData.text || null,
          time_spent: responseData.timeSpent || 0
        })
        .select()
        .single();

      if (error) throw error;

      // Trigger auto-grading
      const gradingResult = await this.autoGradeResponse(data.id);

      return {
        response: data,
        graded: gradingResult.success,
        success: true
      };
    } catch (error) {
      console.error('Error submitting response:', error);
      return {
        response: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Submit complete assessment
   * @param {string} attemptId - Attempt ID
   * @returns {Promise<Object>} Submission result
   */
  async submitAssessment(attemptId) {
    try {
      // Update attempt status
      const { data, error } = await supabase
        .from('assessment_attempts')
        .update({
          status: 'submitted',
          submitted_at: new Date().toISOString()
        })
        .eq('id', attemptId)
        .select()
        .single();

      if (error) throw error;

      // Calculate final score
      await this.calculateAttemptScore(attemptId);

      // Get updated attempt with score
      const { data: finalAttempt } = await supabase
        .from('assessment_attempts')
        .select('*')
        .eq('id', attemptId)
        .single();

      return {
        attempt: finalAttempt,
        success: true
      };
    } catch (error) {
      console.error('Error submitting assessment:', error);
      return {
        attempt: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Auto-grade a response
   * @param {string} responseId - Response ID
   * @returns {Promise<Object>} Grading result
   */
  async autoGradeResponse(responseId) {
    try {
      const { data, error } = await supabase
        .rpc('auto_grade_response', { response_id: responseId });

      if (error) throw error;

      return {
        graded: data,
        success: true
      };
    } catch (error) {
      console.error('Error auto-grading response:', error);
      return {
        graded: false,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Calculate attempt score
   * @param {string} attemptId - Attempt ID
   * @returns {Promise<Object>} Calculation result
   */
  async calculateAttemptScore(attemptId) {
    try {
      const { error } = await supabase
        .rpc('calculate_attempt_score', { attempt_id: attemptId });

      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Error calculating attempt score:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get user's assessment attempts
   * @param {string} assessmentId - Assessment ID
   * @param {string} userId - User ID (optional, defaults to current user)
   * @returns {Promise<Object>} Attempts list
   */
  async getUserAttempts(assessmentId, userId = null) {
    try {
      const user = userId || (await supabase.auth.getUser()).data.user?.id;
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('assessment_attempts')
        .select(`
          *,
          assessment_responses(
            id, question_id, is_correct, points_earned, max_points
          )
        `)
        .eq('assessment_id', assessmentId)
        .eq('user_id', user)
        .order('attempt_number', { ascending: false });

      if (error) throw error;

      return {
        attempts: data || [],
        success: true
      };
    } catch (error) {
      console.error('Error fetching user attempts:', error);
      return {
        attempts: [],
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get assessment analytics
   * @param {string} assessmentId - Assessment ID
   * @returns {Promise<Object>} Analytics data
   */
  async getAssessmentAnalytics(assessmentId) {
    try {
      // Get basic assessment stats
      const { data: assessment } = await supabase
        .from('assessments')
        .select('total_attempts, average_score, completion_rate')
        .eq('id', assessmentId)
        .single();

      // Get question-level analytics
      const { data: questionAnalytics } = await supabase
        .from('assessment_analytics')
        .select('*')
        .eq('assessment_id', assessmentId);

      // Get recent attempts for trends
      const { data: recentAttempts } = await supabase
        .from('assessment_attempts')
        .select('score_percentage, submitted_at, passed')
        .eq('assessment_id', assessmentId)
        .eq('status', 'graded')
        .order('submitted_at', { ascending: false })
        .limit(100);

      // Calculate trends
      const trends = this.calculateTrends(recentAttempts);

      return {
        analytics: {
          ...assessment,
          questionAnalytics: questionAnalytics || [],
          trends
        },
        success: true
      };
    } catch (error) {
      console.error('Error fetching assessment analytics:', error);
      return {
        analytics: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Calculate performance trends
   * @param {Array} attempts - Recent attempts data
   * @returns {Object} Trends data
   */
  calculateTrends(attempts) {
    if (!attempts || attempts.length === 0) {
      return { scoresTrend: [], passRateTrend: [] };
    }

    // Group by day
    const dailyData = new Map();
    
    attempts.forEach(attempt => {
      const date = new Date(attempt.submitted_at).toISOString().split('T')[0];
      if (!dailyData.has(date)) {
        dailyData.set(date, { scores: [], passed: 0, total: 0 });
      }
      
      const dayData = dailyData.get(date);
      dayData.scores.push(attempt.score_percentage);
      dayData.total++;
      if (attempt.passed) dayData.passed++;
    });

    // Convert to trend arrays
    const scoresTrend = Array.from(dailyData.entries()).map(([date, data]) => ({
      date,
      averageScore: data.scores.reduce((sum, score) => sum + score, 0) / data.scores.length,
      attempts: data.total
    }));

    const passRateTrend = Array.from(dailyData.entries()).map(([date, data]) => ({
      date,
      passRate: (data.passed / data.total) * 100,
      attempts: data.total
    }));

    return {
      scoresTrend: scoresTrend.sort((a, b) => a.date.localeCompare(b.date)),
      passRateTrend: passRateTrend.sort((a, b) => a.date.localeCompare(b.date))
    };
  }

  /**
   * Create new assessment
   * @param {Object} assessmentData - Assessment data
   * @returns {Promise<Object>} Created assessment
   */
  async createAssessment(assessmentData) {
    try {
      const user = await supabase.auth.getUser();
      
      const { data, error } = await supabase
        .from('assessments')
        .insert({
          ...assessmentData,
          created_by: user.data.user?.id
        })
        .select()
        .single();

      if (error) throw error;

      return {
        assessment: data,
        success: true
      };
    } catch (error) {
      console.error('Error creating assessment:', error);
      return {
        assessment: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Add question to assessment
   * @param {string} assessmentId - Assessment ID
   * @param {string} questionId - Question ID
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Result
   */
  async addQuestionToAssessment(assessmentId, questionId, options = {}) {
    try {
      const { data, error } = await supabase
        .from('assessment_questions_map')
        .insert({
          assessment_id: assessmentId,
          question_id: questionId,
          sort_order: options.sortOrder || 0,
          points_override: options.pointsOverride,
          is_required: options.isRequired !== false
        })
        .select()
        .single();

      if (error) throw error;

      return {
        mapping: data,
        success: true
      };
    } catch (error) {
      console.error('Error adding question to assessment:', error);
      return {
        mapping: null,
        success: false,
        error: error.message
      };
    }
  }
}

// Export singleton instance
export default new AssessmentEngineService();
