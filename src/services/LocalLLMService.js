/**
 * Local LLM Service
 * 
 * Service for interacting with locally deployed LLMs via Ollama
 * and custom reasoning engines for enhanced cybersecurity education.
 */

class LocalLLMService {
  constructor() {
    this.baseURL = import.meta.env.VITE_LLM_API_URL || 'http://localhost:8080';
    this.ollamaURL = import.meta.env.VITE_OLLAMA_URL || 'http://localhost:11434';
    this.reasoningURL = import.meta.env.VITE_REASONING_URL || 'http://localhost:8082';
    this.embeddingURL = import.meta.env.VITE_EMBEDDING_URL || 'http://localhost:8081';
    
    this.models = {
      general: 'llama3.1:8b',
      reasoning: 'llama3.1:70b',
      coding: 'codellama:13b',
      cybersecurity: 'qwen2.5:32b',
      thinking: 'qwen2.5:32b-instruct'
    };
    
    this.conversationHistory = new Map();
    this.initializeService();
  }

  /**
   * Initialize the LLM service
   */
  async initializeService() {
    try {
      await this.checkServiceHealth();
      await this.loadAvailableModels();
      console.log('🤖 Local LLM Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize LLM service:', error);
    }
  }

  /**
   * Check health of all LLM services
   */
  async checkServiceHealth() {
    const services = [
      { name: 'LLM Gateway', url: `${this.baseURL}/health` },
      { name: 'Ollama', url: `${this.ollamaURL}/api/tags` },
      { name: 'Reasoning Engine', url: `${this.reasoningURL}/health` },
      { name: 'Embedding Service', url: `${this.embeddingURL}/health` }
    ];

    const healthStatus = {};
    
    for (const service of services) {
      try {
        const response = await fetch(service.url);
        healthStatus[service.name] = response.ok ? 'healthy' : 'unhealthy';
      } catch (error) {
        healthStatus[service.name] = 'offline';
      }
    }

    return healthStatus;
  }

  /**
   * Load available models from Ollama
   */
  async loadAvailableModels() {
    try {
      const response = await fetch(`${this.ollamaURL}/api/tags`);
      const data = await response.json();
      this.availableModels = data.models || [];
      return this.availableModels;
    } catch (error) {
      console.error('Failed to load available models:', error);
      return [];
    }
  }

  /**
   * Generate response using specified model
   */
  async generateResponse(prompt, options = {}) {
    const {
      model = this.models.general,
      temperature = 0.7,
      maxTokens = 2048,
      stream = false,
      context = null,
      systemPrompt = null
    } = options;

    try {
      const payload = {
        model,
        prompt,
        options: {
          temperature,
          num_predict: maxTokens,
          top_p: 0.9,
          top_k: 40
        },
        stream,
        system: systemPrompt || this.getSystemPrompt(context)
      };

      const response = await fetch(`${this.ollamaURL}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (stream) {
        return this.handleStreamResponse(response);
      } else {
        const data = await response.json();
        return {
          response: data.response,
          model: data.model,
          context: data.context,
          done: data.done
        };
      }
    } catch (error) {
      console.error('Failed to generate response:', error);
      throw error;
    }
  }

  /**
   * Enhanced reasoning with chain-of-thought
   */
  async generateReasoningResponse(prompt, options = {}) {
    const {
      maxSteps = 5,
      temperature = 0.1,
      context = 'cybersecurity'
    } = options;

    try {
      const response = await fetch(`${this.reasoningURL}/api/reason`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          prompt,
          maxSteps,
          temperature,
          context,
          model: this.models.reasoning
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return {
        finalAnswer: data.finalAnswer,
        reasoningSteps: data.reasoningSteps,
        confidence: data.confidence,
        sources: data.sources
      };
    } catch (error) {
      console.error('Failed to generate reasoning response:', error);
      throw error;
    }
  }

  /**
   * Cybersecurity-specific AI assistant
   */
  async getCybersecurityAdvice(query, userLevel = 'intermediate') {
    const systemPrompt = `You are an expert cybersecurity instructor and mentor. 
    You're helping a ${userLevel} level student learn cybersecurity concepts.
    
    Guidelines:
    - Provide accurate, practical cybersecurity advice
    - Include real-world examples and scenarios
    - Suggest hands-on labs or exercises when appropriate
    - Explain complex concepts in an understandable way
    - Always emphasize ethical hacking and responsible disclosure
    - Include relevant tools, techniques, and best practices
    
    Focus areas: Network Security, Web Application Security, Malware Analysis, 
    Digital Forensics, Incident Response, Penetration Testing, Blue Team Operations`;

    return await this.generateResponse(query, {
      model: this.models.cybersecurity,
      systemPrompt,
      temperature: 0.3,
      context: 'cybersecurity'
    });
  }

  /**
   * Code analysis and security review
   */
  async analyzeCode(code, language = 'javascript') {
    const systemPrompt = `You are a cybersecurity expert specializing in secure code review.
    Analyze the provided ${language} code for security vulnerabilities, best practices, and improvements.
    
    Focus on:
    - Common vulnerabilities (OWASP Top 10)
    - Input validation and sanitization
    - Authentication and authorization flaws
    - Cryptographic issues
    - Business logic vulnerabilities
    - Performance and efficiency
    
    Provide specific recommendations with code examples.`;

    return await this.generateResponse(`Please analyze this ${language} code:\n\n${code}`, {
      model: this.models.coding,
      systemPrompt,
      temperature: 0.2
    });
  }

  /**
   * Generate learning path recommendations
   */
  async generateLearningPath(userProfile) {
    const {
      currentLevel,
      interests,
      goals,
      timeAvailable,
      preferredLearningStyle
    } = userProfile;

    const prompt = `Create a personalized cybersecurity learning path for a user with:
    - Current Level: ${currentLevel}
    - Interests: ${interests.join(', ')}
    - Goals: ${goals}
    - Time Available: ${timeAvailable}
    - Learning Style: ${preferredLearningStyle}
    
    Provide a structured learning path with specific courses, labs, and milestones.`;

    return await this.generateReasoningResponse(prompt, {
      context: 'education',
      maxSteps: 3
    });
  }

  /**
   * Interactive lab assistance
   */
  async getLabAssistance(labContext, userQuestion) {
    const systemPrompt = `You are a cybersecurity lab instructor providing guidance.
    
    Lab Context: ${labContext.labName}
    Lab Type: ${labContext.labType}
    Current Step: ${labContext.currentStep}
    Tools Available: ${labContext.tools?.join(', ') || 'Standard lab tools'}
    
    Provide helpful guidance without giving away the complete solution.
    Encourage learning through discovery and hands-on practice.`;

    return await this.generateResponse(userQuestion, {
      model: this.models.cybersecurity,
      systemPrompt,
      temperature: 0.4
    });
  }

  /**
   * Threat intelligence and analysis
   */
  async analyzeThreat(threatData) {
    const prompt = `Analyze this cybersecurity threat data and provide insights:
    
    ${JSON.stringify(threatData, null, 2)}
    
    Provide analysis including:
    - Threat classification and severity
    - Attack vectors and techniques
    - Potential impact and risks
    - Mitigation strategies
    - IOCs (Indicators of Compromise)
    - Recommended defensive measures`;

    return await this.generateReasoningResponse(prompt, {
      context: 'threat_analysis',
      maxSteps: 4
    });
  }

  /**
   * Generate embeddings for semantic search
   */
  async generateEmbeddings(text) {
    try {
      const response = await fetch(`${this.embeddingURL}/api/embed`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ text })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.embedding;
    } catch (error) {
      console.error('Failed to generate embeddings:', error);
      throw error;
    }
  }

  /**
   * Semantic search in knowledge base
   */
  async semanticSearch(query, limit = 5) {
    try {
      const response = await fetch(`${this.embeddingURL}/api/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query, limit })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.results;
    } catch (error) {
      console.error('Failed to perform semantic search:', error);
      throw error;
    }
  }

  /**
   * Conversation management
   */
  startConversation(userId, context = {}) {
    const conversationId = `${userId}_${Date.now()}`;
    this.conversationHistory.set(conversationId, {
      messages: [],
      context,
      startTime: Date.now()
    });
    return conversationId;
  }

  addToConversation(conversationId, message, role = 'user') {
    const conversation = this.conversationHistory.get(conversationId);
    if (conversation) {
      conversation.messages.push({
        role,
        content: message,
        timestamp: Date.now()
      });
    }
  }

  getConversationHistory(conversationId) {
    return this.conversationHistory.get(conversationId);
  }

  /**
   * Get appropriate system prompt based on context
   */
  getSystemPrompt(context) {
    const prompts = {
      cybersecurity: `You are an expert cybersecurity instructor and mentor. Provide accurate, practical advice with real-world examples.`,
      coding: `You are a secure coding expert. Focus on security best practices and vulnerability prevention.`,
      education: `You are an educational AI assistant specializing in cybersecurity learning paths and skill development.`,
      threat_analysis: `You are a threat intelligence analyst. Provide detailed analysis of cybersecurity threats and incidents.`,
      default: `You are a helpful AI assistant specializing in cybersecurity education and training.`
    };

    return prompts[context] || prompts.default;
  }

  /**
   * Handle streaming responses
   */
  async handleStreamResponse(response) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let result = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);
              if (data.response) {
                result += data.response;
              }
            } catch (e) {
              // Skip invalid JSON lines
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    return { response: result, done: true };
  }

  /**
   * Get service statistics
   */
  async getServiceStats() {
    try {
      const response = await fetch(`${this.baseURL}/api/stats`);
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Failed to get service stats:', error);
      return null;
    }
  }
}

// Create singleton instance
const localLLMService = new LocalLLMService();

export default localLLMService;
