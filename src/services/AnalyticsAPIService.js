/**
 * Analytics API Service
 * 
 * Comprehensive API service for analytics data collection,
 * processing, and real-time dashboard updates.
 */

class AnalyticsAPIService {
  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    this.endpoints = {
      events: '/api/analytics/events',
      metrics: '/api/analytics/metrics',
      realtime: '/api/analytics/realtime',
      dashboard: '/api/analytics/dashboard',
      users: '/api/analytics/users',
      learning: '/api/analytics/learning',
      web3: '/api/analytics/web3',
      performance: '/api/analytics/performance',
      export: '/api/analytics/export'
    };
    this.initializeWebSocket();
  }

  /**
   * Initialize WebSocket for real-time updates
   */
  initializeWebSocket() {
    try {
      const wsURL = this.baseURL.replace('http', 'ws') + '/ws/analytics';
      this.websocket = new WebSocket(wsURL);
      
      this.websocket.onopen = () => {
        console.log('📡 Analytics WebSocket connected');
        window.analyticsWebSocket = this.websocket;
      };
      
      this.websocket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        this.handleRealTimeUpdate(data);
      };
      
      this.websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
      
      this.websocket.onclose = () => {
        console.log('WebSocket disconnected, attempting reconnect...');
        setTimeout(() => this.initializeWebSocket(), 5000);
      };
    } catch (error) {
      console.error('Failed to initialize WebSocket:', error);
    }
  }

  /**
   * Send event to analytics API
   */
  async logEvent(event) {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.events}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        },
        body: JSON.stringify(event)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to log event:', error);
      throw error;
    }
  }

  /**
   * Get real-time metrics
   */
  async getRealTimeMetrics() {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.realtime}`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to get real-time metrics:', error);
      throw error;
    }
  }

  /**
   * Get comprehensive dashboard analytics
   */
  async getDashboardAnalytics(timeRange = '24h') {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.dashboard}?range=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return {
        overview: data.overview,
        userActivity: data.userActivity,
        platformUsage: data.platformUsage,
        learningMetrics: data.learningMetrics,
        securityDomains: data.securityDomains,
        web3Activity: data.web3Activity,
        performanceMetrics: data.performanceMetrics,
        geographicDistribution: data.geographicDistribution,
        deviceAnalytics: data.deviceAnalytics,
        conversionFunnels: data.conversionFunnels
      };
    } catch (error) {
      console.error('Failed to get dashboard analytics:', error);
      throw error;
    }
  }

  /**
   * Get user behavior analytics
   */
  async getUserAnalytics(userId = null, timeRange = '7d') {
    try {
      const url = userId 
        ? `${this.baseURL}${this.endpoints.users}/${userId}?range=${timeRange}`
        : `${this.baseURL}${this.endpoints.users}?range=${timeRange}`;

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to get user analytics:', error);
      throw error;
    }
  }

  /**
   * Get learning progress analytics
   */
  async getLearningAnalytics(timeRange = '30d') {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.learning}?range=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return {
        courseCompletions: data.courseCompletions,
        labActivities: data.labActivities,
        assessmentPerformance: data.assessmentPerformance,
        skillProgression: data.skillProgression,
        learningPaths: data.learningPaths,
        timeSpentLearning: data.timeSpentLearning,
        difficultyProgression: data.difficultyProgression,
        retentionRates: data.retentionRates
      };
    } catch (error) {
      console.error('Failed to get learning analytics:', error);
      throw error;
    }
  }

  /**
   * Get Web3 activity analytics
   */
  async getWeb3Analytics(timeRange = '30d') {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.web3}?range=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return {
        walletConnections: data.walletConnections,
        tokenTransactions: data.tokenTransactions,
        nftActivity: data.nftActivity,
        stakingMetrics: data.stakingMetrics,
        daoParticipation: data.daoParticipation,
        marketplaceActivity: data.marketplaceActivity,
        gasUsage: data.gasUsage,
        tokenDistribution: data.tokenDistribution
      };
    } catch (error) {
      console.error('Failed to get Web3 analytics:', error);
      throw error;
    }
  }

  /**
   * Get performance analytics
   */
  async getPerformanceAnalytics(timeRange = '24h') {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.performance}?range=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return {
        pageLoadTimes: data.pageLoadTimes,
        apiResponseTimes: data.apiResponseTimes,
        errorRates: data.errorRates,
        uptime: data.uptime,
        throughput: data.throughput,
        resourceUsage: data.resourceUsage,
        cacheHitRates: data.cacheHitRates,
        cdnPerformance: data.cdnPerformance
      };
    } catch (error) {
      console.error('Failed to get performance analytics:', error);
      throw error;
    }
  }

  /**
   * Get security domain distribution
   */
  async getSecurityDomainAnalytics(timeRange = '30d') {
    try {
      const response = await fetch(`${this.baseURL}/api/analytics/security-domains?range=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return {
        fundamentals: data.fundamentals,
        blueTeam: data.blueTeam,
        redTeam: data.redTeam,
        purpleTeam: data.purpleTeam,
        forensics: data.forensics,
        malwareAnalysis: data.malwareAnalysis,
        networkSecurity: data.networkSecurity,
        webSecurity: data.webSecurity,
        cloudSecurity: data.cloudSecurity,
        iot: data.iot,
        trends: data.trends,
        skillGaps: data.skillGaps
      };
    } catch (error) {
      console.error('Failed to get security domain analytics:', error);
      throw error;
    }
  }

  /**
   * Export analytics data
   */
  async exportAnalytics(format = 'json', timeRange = '30d', filters = {}) {
    try {
      const queryParams = new URLSearchParams({
        format,
        range: timeRange,
        ...filters
      });

      const response = await fetch(`${this.baseURL}${this.endpoints.export}?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (format === 'json') {
        return await response.json();
      } else {
        return await response.blob();
      }
    } catch (error) {
      console.error('Failed to export analytics:', error);
      throw error;
    }
  }

  /**
   * Create custom analytics query
   */
  async customQuery(query) {
    try {
      const response = await fetch(`${this.baseURL}/api/analytics/query`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        },
        body: JSON.stringify(query)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to execute custom query:', error);
      throw error;
    }
  }

  /**
   * Handle real-time updates from WebSocket
   */
  handleRealTimeUpdate(data) {
    // Dispatch event for dashboard components to listen
    window.dispatchEvent(new CustomEvent('analytics_realtime_update', {
      detail: data
    }));

    // Update any global state if needed
    if (window.analyticsStore) {
      window.analyticsStore.updateRealTimeData(data);
    }
  }

  /**
   * Get authentication token
   */
  getAuthToken() {
    return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
  }

  /**
   * Batch send events for better performance
   */
  async batchLogEvents(events) {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.events}/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        },
        body: JSON.stringify({ events })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to batch log events:', error);
      throw error;
    }
  }

  /**
   * Get analytics health status
   */
  async getHealthStatus() {
    try {
      const response = await fetch(`${this.baseURL}/api/analytics/health`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to get health status:', error);
      throw error;
    }
  }

  /**
   * Configure analytics settings
   */
  async updateSettings(settings) {
    try {
      const response = await fetch(`${this.baseURL}/api/analytics/settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        },
        body: JSON.stringify(settings)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to update settings:', error);
      throw error;
    }
  }
}

// Create singleton instance
const analyticsAPI = new AnalyticsAPIService();

export default analyticsAPI;
