import { supabase } from '../lib/supabase';

/**
 * Dynamic Dashboard Service
 * 
 * Handles all database-driven dashboard content management.
 * Provides functions to fetch, update, and manage dashboard configurations and content.
 */
class DynamicDashboardService {
  
  /**
   * Get dashboard configuration and content blocks
   * @param {string} dashboardId - The dashboard identifier
   * @param {string} userAccessLevel - User's access level (free, premium, business, admin, super_admin)
   * @returns {Promise<Object>} Dashboard configuration and content blocks
   */
  async getDashboardContent(dashboardId, userAccessLevel = 'free') {
    try {
      // Get dashboard configuration
      const { data: config, error: configError } = await supabase
        .from('dashboard_configurations')
        .select('*')
        .eq('dashboard_id', dashboardId)
        .eq('is_active', true)
        .single();

      if (configError) throw configError;

      // Get content blocks using the database function
      const { data: contentBlocks, error: blocksError } = await supabase
        .rpc('get_dashboard_content', {
          dashboard_name: dashboardId,
          user_access_level: userAccessLevel
        });

      if (blocksError) throw blocksError;

      return {
        configuration: config,
        contentBlocks: contentBlocks || [],
        success: true
      };
    } catch (error) {
      console.error('Error fetching dashboard content:', error);
      return {
        configuration: null,
        contentBlocks: [],
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Track dashboard usage analytics
   * @param {string} dashboardId - The dashboard identifier
   * @param {string} eventType - Type of event (view, click, interaction)
   * @param {Object} eventData - Additional event data
   */
  async trackDashboardUsage(dashboardId, eventType, eventData = {}) {
    try {
      await supabase.rpc('track_dashboard_usage', {
        dashboard_name: dashboardId,
        event_type_param: eventType,
        event_data_param: eventData
      });
    } catch (error) {
      console.error('Error tracking dashboard usage:', error);
    }
  }

  /**
   * Get platform statistics for super admin dashboard
   * @returns {Promise<Object>} Platform statistics
   */
  async getPlatformStatistics() {
    try {
      // Get user statistics
      const { data: users, error: usersError } = await supabase
        .from('profiles')
        .select('subscription_tier, created_at, last_sign_in_at');

      if (usersError) throw usersError;

      // Get subscription tracking data
      const { data: subscriptions, error: subsError } = await supabase
        .from('subscription_expiry_tracking')
        .select('*');

      if (subsError) throw subsError;

      // Get dashboard analytics
      const { data: analytics, error: analyticsError } = await supabase
        .from('dashboard_analytics')
        .select('dashboard_id, event_type, timestamp')
        .gte('timestamp', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()); // Last 30 days

      if (analyticsError) throw analyticsError;

      // Calculate statistics
      const totalUsers = users?.length || 0;
      const freeUsers = users?.filter(u => !u.subscription_tier || u.subscription_tier === 'free').length || 0;
      const premiumUsers = users?.filter(u => u.subscription_tier === 'premium').length || 0;
      const businessUsers = users?.filter(u => u.subscription_tier === 'business').length || 0;

      // Calculate expiring subscriptions (next 30 days)
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      
      const expiringSubscriptions = subscriptions?.filter(sub => {
        if (!sub.end_date) return false;
        const endDate = new Date(sub.end_date);
        return endDate <= thirtyDaysFromNow && endDate > new Date() && sub.is_active;
      }).length || 0;

      const activeSubscriptions = subscriptions?.filter(s => s.is_active).length || 0;

      // Dashboard usage statistics
      const dashboardUsage = {};
      analytics?.forEach(analytic => {
        if (!dashboardUsage[analytic.dashboard_id]) {
          dashboardUsage[analytic.dashboard_id] = 0;
        }
        dashboardUsage[analytic.dashboard_id]++;
      });

      return {
        totalUsers,
        freeUsers,
        premiumUsers,
        businessUsers,
        totalRevenue: activeSubscriptions * 399, // Simplified calculation
        activeSubscriptions,
        expiringSubscriptions,
        dashboardUsage,
        success: true
      };
    } catch (error) {
      console.error('Error fetching platform statistics:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get users with expiring subscriptions
   * @param {number} daysAhead - Number of days to look ahead (default: 30)
   * @returns {Promise<Array>} Users with expiring subscriptions
   */
  async getExpiringSubscriptions(daysAhead = 30) {
    try {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + daysAhead);

      const { data, error } = await supabase
        .from('subscription_expiry_tracking')
        .select(`
          *,
          profiles:user_id (
            username,
            email,
            subscription_tier
          )
        `)
        .eq('is_active', true)
        .gte('end_date', new Date().toISOString())
        .lte('end_date', futureDate.toISOString())
        .order('end_date', { ascending: true });

      if (error) throw error;

      return {
        expiringSubscriptions: data || [],
        success: true
      };
    } catch (error) {
      console.error('Error fetching expiring subscriptions:', error);
      return {
        expiringSubscriptions: [],
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send renewal notifications to users with expiring subscriptions
   * @param {Array} userIds - Array of user IDs to send notifications to
   * @returns {Promise<Object>} Result of notification sending
   */
  async sendRenewalNotifications(userIds) {
    try {
      // Create notification record
      const { data, error } = await supabase
        .from('platform_notifications')
        .insert({
          title: 'Subscription Renewal Reminder',
          message: 'Your subscription is expiring soon. Renew now to continue enjoying premium features.',
          notification_type: 'warning',
          target_audience: 'expiring_users',
          sent_at: new Date().toISOString()
        });

      if (error) throw error;

      // In a real implementation, this would integrate with an email service
      console.log(`Renewal notifications sent to ${userIds.length} users`);

      return {
        success: true,
        notificationsSent: userIds.length
      };
    } catch (error) {
      console.error('Error sending renewal notifications:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create a new white-label instance
   * @param {Object} instanceData - White-label instance configuration
   * @returns {Promise<Object>} Created instance data
   */
  async createWhiteLabelInstance(instanceData) {
    try {
      const { data, error } = await supabase
        .from('whitelabel_instances')
        .insert({
          instance_name: instanceData.name,
          domain: instanceData.domain,
          branding: instanceData.branding || {},
          configuration: instanceData.configuration || {},
          owner_id: instanceData.ownerId
        })
        .select()
        .single();

      if (error) throw error;

      return {
        instance: data,
        success: true
      };
    } catch (error) {
      console.error('Error creating white-label instance:', error);
      return {
        instance: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get all white-label instances
   * @returns {Promise<Object>} List of white-label instances
   */
  async getWhiteLabelInstances() {
    try {
      const { data, error } = await supabase
        .from('whitelabel_instances')
        .select(`
          *,
          profiles:owner_id (
            username,
            email
          )
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return {
        instances: data || [],
        success: true
      };
    } catch (error) {
      console.error('Error fetching white-label instances:', error);
      return {
        instances: [],
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update dashboard configuration
   * @param {string} dashboardId - Dashboard identifier
   * @param {Object} updates - Configuration updates
   * @returns {Promise<Object>} Update result
   */
  async updateDashboardConfiguration(dashboardId, updates) {
    try {
      const { data, error } = await supabase
        .from('dashboard_configurations')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('dashboard_id', dashboardId)
        .select()
        .single();

      if (error) throw error;

      return {
        configuration: data,
        success: true
      };
    } catch (error) {
      console.error('Error updating dashboard configuration:', error);
      return {
        configuration: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Add or update content block
   * @param {string} dashboardId - Dashboard identifier
   * @param {Object} blockData - Content block data
   * @returns {Promise<Object>} Update result
   */
  async updateContentBlock(dashboardId, blockData) {
    try {
      const { data, error } = await supabase
        .from('dashboard_content_blocks')
        .upsert({
          dashboard_id: dashboardId,
          ...blockData,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      return {
        contentBlock: data,
        success: true
      };
    } catch (error) {
      console.error('Error updating content block:', error);
      return {
        contentBlock: null,
        success: false,
        error: error.message
      };
    }
  }
}

// Export singleton instance
export default new DynamicDashboardService();
