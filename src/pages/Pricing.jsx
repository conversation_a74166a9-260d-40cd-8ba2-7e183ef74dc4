import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaCheck, FaTimes, FaCrown, FaUsers, FaServer, FaRocket, FaShieldAlt, FaBuilding, FaArrowRight } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { useSubscription } from '../contexts/SubscriptionContext';
import Button from '../components/ui/Button';

function Pricing() {
  const navigate = useNavigate();
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedTier, setSelectedTier] = useState(null);
  const { subscriptionLevel, upgradeSubscription, subscriptionTiers, tierFeatures } = useSubscription();

  // Filter out the 'none' tier and sort by price
  const tiers = Object.values(tierFeatures)
    .filter(tier => tier && tier.name)
    .sort((a, b) => a.price - b.price);

  const handleUpgrade = async (tier) => {
    setSelectedTier(tier.name.toLowerCase());
    setIsProcessing(true);

    try {
      await upgradeSubscription(tier.name.toLowerCase());
      // Redirect to dashboard after successful upgrade
      navigate('/simplified-dashboard');
    } catch (error) {
      console.error('Upgrade failed:', error);
      // You could show an error toast here
    } finally {
      setIsProcessing(false);
    }
  };

  const getTierIcon = (tierName) => {
    switch (tierName.toLowerCase()) {
      case 'free':
        return null;
      case 'premium':
        return <FaCrown className="text-[#88cc14] text-xl" />;
      case 'business':
        return <FaBuilding className="text-[#88cc14] text-xl" />;
      default:
        return <FaRocket className="text-[#88cc14] text-xl" />;
    }
  };

  const isCurrentTier = (tierName) => {
    return subscriptionLevel === tierName.toLowerCase();
  };

  return (
    <div className="min-h-screen bg-[#0B1120] py-16 px-4">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white">
            Choose Your <span className="text-[#88cc14]">Subscription</span> Plan
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Select the plan that best fits your cybersecurity learning journey. Upgrade anytime as your skills grow.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-16">
          {[
            { icon: FaUsers, value: '10,000+', label: 'Active Users' },
            { icon: FaServer, value: '150+', label: 'Challenges' },
            { icon: FaRocket, value: '50+', label: 'Learning Modules' },
            { icon: FaShieldAlt, value: '24/7', label: 'Support' }
          ].map((stat, index) => (
            <div key={index} className="bg-[#1A1F35] rounded-lg p-4 border border-gray-800">
              <stat.icon className="text-[#88cc14] text-2xl mx-auto mb-2" />
              <div className="text-xl font-bold text-white">{stat.value}</div>
              <div className="text-gray-400 text-sm">{stat.label}</div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {tiers.map((tier, index) => (
            <motion.div
              key={tier.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`bg-[#1A1F35] rounded-xl overflow-hidden border ${
                tier.name.toLowerCase() === 'premium'
                  ? 'border-[#88cc14] shadow-lg shadow-[#88cc14]/10'
                  : 'border-gray-800'
              }`}
            >
              {/* Popular badge */}
              {tier.name.toLowerCase() === 'premium' && (
                <div className="bg-[#88cc14] text-black text-center py-1 font-bold text-sm">
                  MOST POPULAR
                </div>
              )}

              <div className="p-6">
                {/* Tier header */}
                <div className="flex items-center gap-2 mb-2">
                  {getTierIcon(tier.name)}
                  <h2 className="text-2xl font-bold text-white">{tier.name}</h2>
                </div>

                {/* Price */}
                <div className="mb-6">
                  <div className="flex items-end gap-1">
                    <span className="text-4xl font-bold text-white">${tier.price}</span>
                    {tier.price > 0 && <span className="text-gray-400 mb-1">/month</span>}
                  </div>
                  {tier.price > 0 && (
                    <p className="text-gray-400 text-sm mt-1">Billed monthly, cancel anytime</p>
                  )}
                </div>

                {/* Features */}
                <ul className="space-y-3 mb-8">
                  {tier.features.map((feature, i) => (
                    <li key={i} className="flex items-start gap-2">
                      <FaCheck className="text-[#88cc14] mt-1 flex-shrink-0" />
                      <span className="text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* Action button */}
                {isCurrentTier(tier.name) ? (
                  <Button
                    variant="outline"
                    fullWidth
                    disabled
                  >
                    Current Plan
                  </Button>
                ) : (
                  <Button
                    variant={tier.name.toLowerCase() === 'premium' ? 'primary' : 'secondary'}
                    fullWidth
                    onClick={() => handleUpgrade(tier)}
                    disabled={isProcessing && selectedTier === tier.name.toLowerCase()}
                    icon={tier.name.toLowerCase() === 'premium' ? <FaArrowRight /> : null}
                    iconPosition="right"
                  >
                    {isProcessing && selectedTier === tier.name.toLowerCase()
                      ? 'Processing...'
                      : tier.price === 0
                        ? 'Get Started'
                        : `Upgrade to ${tier.name}`}
                  </Button>
                )}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Feature Comparison Table */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-white text-center mb-10">Feature Comparison</h2>

          <div className="overflow-x-auto">
            <table className="w-full bg-[#1A1F35] rounded-lg border border-gray-800">
              <thead>
                <tr className="border-b border-gray-800">
                  <th className="px-6 py-4 text-left text-white">Feature</th>
                  <th className="px-6 py-4 text-center text-white">Free</th>
                  <th className="px-6 py-4 text-center text-white">Premium</th>
                  <th className="px-6 py-4 text-center text-white">Business</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Learning Modules</td>
                  <td className="px-6 py-4 text-center text-gray-300">3</td>
                  <td className="px-6 py-4 text-center text-gray-300">All 50</td>
                  <td className="px-6 py-4 text-center text-gray-300">All 50</td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Challenges</td>
                  <td className="px-6 py-4 text-center text-gray-300">5</td>
                  <td className="px-6 py-4 text-center text-gray-300">100</td>
                  <td className="px-6 py-4 text-center text-gray-300">All 150</td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Start Hack</td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaTimes className="mx-auto text-red-500" /></td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaCheck className="mx-auto text-[#88cc14]" /></td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaCheck className="mx-auto text-[#88cc14]" /></td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Coin Purchase Required</td>
                  <td className="px-6 py-4 text-center text-gray-300">N/A</td>
                  <td className="px-6 py-4 text-center text-gray-300">For some challenges</td>
                  <td className="px-6 py-4 text-center text-gray-300">Never</td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Dashboard</td>
                  <td className="px-6 py-4 text-center text-gray-300">Basic</td>
                  <td className="px-6 py-4 text-center text-gray-300">Advanced</td>
                  <td className="px-6 py-4 text-center text-gray-300">Premium</td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Team Management</td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaTimes className="mx-auto text-red-500" /></td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaTimes className="mx-auto text-red-500" /></td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaCheck className="mx-auto text-[#88cc14]" /></td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Support</td>
                  <td className="px-6 py-4 text-center text-gray-300">Community</td>
                  <td className="px-6 py-4 text-center text-gray-300">Priority</td>
                  <td className="px-6 py-4 text-center text-gray-300">Dedicated</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-white text-center mb-10">Frequently Asked Questions</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-[#1A1F35] p-6 rounded-xl border border-gray-800">
              <h3 className="text-xl font-bold text-white mb-3">Can I change plans later?</h3>
              <p className="text-gray-300">
                Yes, you can upgrade or downgrade your subscription at any time. Changes take effect immediately.
              </p>
            </div>

            <div className="bg-[#1A1F35] p-6 rounded-xl border border-gray-800">
              <h3 className="text-xl font-bold text-white mb-3">How do coins work?</h3>
              <p className="text-gray-300">
                Coins are our virtual currency that can be used to unlock premium challenges. Premium subscribers get access to most challenges, but some special challenges require coins.
              </p>
            </div>

            <div className="bg-[#1A1F35] p-6 rounded-xl border border-gray-800">
              <h3 className="text-xl font-bold text-white mb-3">What payment methods do you accept?</h3>
              <p className="text-gray-300">
                We accept all major credit cards, PayPal, and cryptocurrency payments.
              </p>
            </div>

            <div className="bg-[#1A1F35] p-6 rounded-xl border border-gray-800">
              <h3 className="text-xl font-bold text-white mb-3">Is there a refund policy?</h3>
              <p className="text-gray-300">
                Yes, we offer a 7-day money-back guarantee if you're not satisfied with your subscription.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Pricing;