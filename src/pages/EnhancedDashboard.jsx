import React, { useState, useEffect } from 'react';
import { Routes, Route, useNavigate } from 'react-router-dom';
import DashboardLayout from '../components/dashboard/DashboardLayout';
import DashboardHome from '../components/dashboard/DashboardHome';
import LearningPaths from '../components/dashboard/LearningPaths';
import ModuleView from '../components/dashboard/ModuleView';
import ProgressTracker from '../components/dashboard/ProgressTracker';
import AILearningAssistant from '../components/dashboard/AILearningAssistant';
import { FaLightbulb, FaCrown, FaRocket } from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';

const EnhancedDashboard = () => {
  const [isAIAssistantOpen, setIsAIAssistantOpen] = useState(false);
  const { darkMode } = useGlobalTheme();
  const navigate = useNavigate();

  // 🔧 DEVELOPMENT MODE: Force premium access for testing
  useEffect(() => {
    console.log('🚀 Enhanced Dashboard: Premium features enabled for development');
    console.log('📍 Current URL:', window.location.pathname);
    console.log('✅ All premium features are accessible');
  }, []);

  return (
    <>
      {/* Premium Dashboard Header */}
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-b px-6 py-3`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <FaCrown className="text-[#88cc14] text-xl" />
              <h1 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Enhanced Dashboard
              </h1>
              <span className="px-2 py-1 bg-blue-500 text-white text-xs rounded-full">Premium</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              🔧 Development Mode - All Features Enabled
            </span>
            <FaRocket className="text-[#88cc14]" />
          </div>
        </div>
      </div>

      <DashboardLayout>
        <Routes>
          <Route path="/" element={<DashboardHome />} />
          <Route path="/learning-paths" element={<LearningPaths />} />
          <Route path="/learning-paths/:pathId/modules/:moduleId" element={<ModuleView />} />
          <Route path="/progress" element={<ProgressTracker />} />
          <Route path="*" element={<DashboardHome />} />
        </Routes>

        {/* AI Assistant Button - Enhanced for Premium */}
        <button
          onClick={() => setIsAIAssistantOpen(true)}
          className={`fixed bottom-6 right-6 w-14 h-14 rounded-full bg-gradient-to-r from-[#88cc14] to-[#7ab512] text-black flex items-center justify-center shadow-lg hover:scale-110 transition-all duration-200 ${
            darkMode ? 'shadow-gray-900' : 'shadow-gray-400'
          }`}
          title="AI Learning Assistant (Premium Feature)"
        >
          <FaLightbulb className="text-xl animate-pulse" />
        </button>

        {/* Premium Features Indicator */}
        <div className="fixed bottom-24 right-6 z-30">
          <div className={`px-3 py-2 rounded-lg ${darkMode ? 'bg-blue-900/80 border border-blue-700' : 'bg-blue-50 border border-blue-200'} backdrop-blur-sm`}>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-xs font-medium text-blue-600">Premium Features Active</span>
            </div>
          </div>
        </div>
      </DashboardLayout>

      {/* AI Learning Assistant Modal - Enhanced */}
      <AILearningAssistant
        isOpen={isAIAssistantOpen}
        onClose={() => setIsAIAssistantOpen(false)}
        premiumMode={true}
      />
    </>
  );
};

export default EnhancedDashboard;
