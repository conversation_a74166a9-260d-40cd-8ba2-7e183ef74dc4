import React from 'react';
import { motion } from 'framer-motion';
import { FaShieldAlt, FaGraduationCap, FaUsers, FaChartLine, FaCode, FaTrophy } from 'react-icons/fa';

function About() {
  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Hero Section */}
      <div className="bg-black text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl font-bold mb-4">About XCerberus</h1>
            <p className="text-gray-400 text-lg mb-8">
              Empowering the next generation of cybersecurity professionals through hands-on learning and real-world challenges.
            </p>
            
            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[
                { icon: FaUsers, value: '10,000+', label: 'Active Users' },
                { icon: FaCode, value: '500+', label: 'Challenges' },
                { icon: FaTrophy, value: '50K+', label: 'Completions' },
                { icon: FaChartLine, value: '95%', label: 'Success Rate' }
              ].map((stat, index) => (
                <div key={index} className="bg-gray-800 rounded-lg p-4">
                  <stat.icon className="text-[#88cc14] text-2xl mx-auto mb-2" />
                  <div className="text-xl font-bold">{stat.value}</div>
                  <div className="text-gray-400 text-sm">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Mission Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Mission</h2>
            <p className="text-gray-600 text-lg">
              To provide accessible, high-quality cybersecurity education through practical, hands-on learning experiences that prepare individuals for real-world challenges.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white p-6 rounded-lg shadow-sm"
            >
              <div className="w-12 h-12 bg-[#88cc14]/10 rounded-lg flex items-center justify-center mb-4">
                <FaShieldAlt className="text-[#88cc14] text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Practical Learning</h3>
              <p className="text-gray-600">
                Learn by doing with our interactive labs, CTF challenges, and real-world scenarios designed to build practical skills.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white p-6 rounded-lg shadow-sm"
            >
              <div className="w-12 h-12 bg-[#88cc14]/10 rounded-lg flex items-center justify-center mb-4">
                <FaGraduationCap className="text-[#88cc14] text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Comprehensive Curriculum</h3>
              <p className="text-gray-600">
                From basics to advanced topics, our structured learning paths ensure thorough understanding of cybersecurity concepts.
              </p>
            </motion.div>
          </div>

          {/* 
            Team Section - Currently Hidden
            To unhide this section:
            1. Remove the display: none style from the div below
            2. Or remove the style prop entirely
            3. Or uncomment the section if using comments
          */}
          <div style={{ display: 'none' }}>
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Team</h2>
              <p className="text-gray-600 text-lg mb-8">
                A dedicated team of cybersecurity experts, educators, and industry professionals committed to your success.
              </p>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {[
                  {
                    name: "Dr. Sarah Chen",
                    role: "Chief Security Officer",
                    bio: "15+ years in cybersecurity, specializing in threat intelligence and incident response."
                  },
                  {
                    name: "Mark Rodriguez",
                    role: "Lead Instructor",
                    bio: "Former red team lead with extensive experience in penetration testing and security assessments."
                  },
                  {
                    name: "Lisa Kumar",
                    role: "Content Director",
                    bio: "Security researcher and educator focused on making complex concepts accessible to all."
                  }
                ].map((member, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-white p-6 rounded-lg shadow-sm"
                  >
                    <div className="w-20 h-20 bg-[#88cc14]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <FaUsers className="text-[#88cc14] text-2xl" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                    <p className="text-gray-500 mb-2">{member.role}</p>
                    <p className="text-gray-600 text-sm">{member.bio}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default About;