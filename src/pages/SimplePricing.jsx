import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { FaCheck, FaTimes, FaCrown, FaBuilding, FaRocket } from 'react-icons/fa';
import { TIER_FEATURES } from '../contexts/SimpleSubscriptionContext';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';

const SimplePricing = () => {
  // Get the subscription tiers
  const tiers = Object.values(TIER_FEATURES);
  const { darkMode } = useGlobalTheme();

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} pt-24 pb-16 px-4`}>
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h1 className={`text-4xl md:text-5xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Choose Your <span className="text-[#88cc14]">Subscription</span> Plan
          </h1>
          <p className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-600'} max-w-3xl mx-auto`}>
            Select the plan that best fits your cybersecurity learning journey. Upgrade anytime as your skills grow.
          </p>
        </div>

        {/* Pricing Plans */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {tiers.map((tier, index) => (
            <div
              key={tier.name}
              className={`bg-[#1A1F35] rounded-xl overflow-hidden border ${
                tier.name === 'Premium'
                  ? 'border-[#88cc14] shadow-lg shadow-[#88cc14]/10'
                  : 'border-gray-800'
              }`}
            >
              {/* Popular badge */}
              {tier.name === 'Premium' && (
                <div className="bg-[#88cc14] text-black text-center py-1 font-bold text-sm">
                  MOST POPULAR
                </div>
              )}

              <div className="p-6">
                {/* Tier header */}
                <div className="flex items-center gap-2 mb-2">
                  {tier.name === 'Premium' ? (
                    <FaCrown className="text-[#88cc14] text-xl" />
                  ) : tier.name === 'Business' ? (
                    <FaBuilding className="text-[#88cc14] text-xl" />
                  ) : (
                    <FaRocket className="text-[#88cc14] text-xl" />
                  )}
                  <h2 className="text-2xl font-bold text-white">{tier.name}</h2>
                </div>

                {/* Price */}
                <div className="mb-6">
                  <div className="flex items-end gap-1">
                    {tier.name === 'Free' ? (
                      <span className="text-4xl font-bold text-white">Free</span>
                    ) : tier.name === 'Business' ? (
                      <span className="text-4xl font-bold text-white">Contact Sales</span>
                    ) : (
                      <>
                        <span className="text-4xl font-bold text-white">{tier.currency || '₹'}{tier.price}</span>
                        <span className="text-gray-400 mb-1">/month</span>
                      </>
                    )}
                  </div>
                  {tier.price > 0 && (
                    <p className="text-gray-400 text-sm mt-1">Billed monthly, cancel anytime</p>
                  )}
                </div>

                {/* Features */}
                <ul className="space-y-3 mb-8">
                  {tier.features.map((feature, i) => (
                    <li key={i} className="flex items-start gap-2">
                      <FaCheck className="text-[#88cc14] mt-1 flex-shrink-0" />
                      <span className="text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* Action button */}
                <Link
                  to={tier.name === 'Free' ? '/signup' : tier.name === 'Business' ? '/contact' : '/login'}
                  className={`inline-block w-full text-center py-2 px-4 rounded-lg font-medium ${
                    tier.name === 'Premium'
                      ? 'bg-[#88cc14] hover:bg-[#7ab811] text-black'
                      : tier.name === 'Free'
                        ? 'bg-[#1A1F35] hover:bg-[#252D4A] text-white border border-gray-700'
                        : 'bg-[#0B1120] hover:bg-[#151F38] text-white border border-gray-700'
                  }`}
                >
                  {tier.name === 'Free' ? 'Get Started' :
                   tier.name === 'Premium' ? `Upgrade to Premium` :
                   'Contact Sales'}
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Feature Comparison Table */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-white text-center mb-10">Feature Comparison</h2>

          <div className="overflow-x-auto">
            <table className="w-full bg-[#1A1F35] rounded-lg border border-gray-800">
              <thead>
                <tr className="border-b border-gray-800">
                  <th className="px-6 py-4 text-left text-white">Feature</th>
                  <th className="px-6 py-4 text-center text-white">Free</th>
                  <th className="px-6 py-4 text-center text-white">Premium</th>
                  <th className="px-6 py-4 text-center text-white">Business</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Learning Modules</td>
                  <td className="px-6 py-4 text-center text-gray-300">3</td>
                  <td className="px-6 py-4 text-center text-gray-300">All 50</td>
                  <td className="px-6 py-4 text-center text-gray-300">All 50</td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Challenges</td>
                  <td className="px-6 py-4 text-center text-gray-300">5</td>
                  <td className="px-6 py-4 text-center text-gray-300">100</td>
                  <td className="px-6 py-4 text-center text-gray-300">All 150</td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Pricing</td>
                  <td className="px-6 py-4 text-center text-gray-300">Free</td>
                  <td className="px-6 py-4 text-center text-gray-300">₹399/month</td>
                  <td className="px-6 py-4 text-center text-gray-300">Contact Sales</td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Start Hack</td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaTimes className="mx-auto text-red-500" /></td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaCheck className="mx-auto text-[#88cc14]" /></td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaCheck className="mx-auto text-[#88cc14]" /></td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Dashboard</td>
                  <td className="px-6 py-4 text-center text-gray-300">Basic</td>
                  <td className="px-6 py-4 text-center text-gray-300">Advanced</td>
                  <td className="px-6 py-4 text-center text-gray-300">Premium</td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Team Management</td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaTimes className="mx-auto text-red-500" /></td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaTimes className="mx-auto text-red-500" /></td>
                  <td className="px-6 py-4 text-center text-gray-300"><FaCheck className="mx-auto text-[#88cc14]" /></td>
                </tr>
                <tr className="border-t border-gray-800">
                  <td className="px-6 py-4 text-gray-300 font-medium">Support</td>
                  <td className="px-6 py-4 text-center text-gray-300">Community</td>
                  <td className="px-6 py-4 text-center text-gray-300">Priority</td>
                  <td className="px-6 py-4 text-center text-gray-300">Dedicated</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimplePricing;
