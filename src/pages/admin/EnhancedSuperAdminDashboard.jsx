import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  FaUsers, FaChartLine, FaCog, FaDatabase, FaBell, FaRocket,
  FaGlobe, FaShieldAlt, FaCode, FaBook, FaGamepad, FaTrophy,
  FaUserTie, FaEye, FaPlus, FaEdit, FaTrash, FaDownload,
  FaUpload, FaSync, FaExclamationTriangle, FaCheckCircle,
  FaCrown, FaGem, FaDollarSign, FaCalendarAlt, FaEnvelope
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { supabase } from '../../lib/supabase';

/**
 * EnhancedSuperAdminDashboard Component
 * 
 * Complete platform management dashboard with:
 * - User and subscription management
 * - Dashboard management and white-label capabilities
 * - Real-time statistics and notifications
 * - Content management system
 * - Platform configuration
 */
const EnhancedSuperAdminDashboard = () => {
  const navigate = useNavigate();
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalUsers: 0,
    freeUsers: 0,
    premiumUsers: 0,
    businessUsers: 0,
    totalRevenue: 0,
    activeSubscriptions: 0,
    expiringSubscriptions: 0,
    totalDashboards: 16,
    activeDashboards: 13,
    whitelabelInstances: 0
  });
  const [recentActivity, setRecentActivity] = useState([]);
  const [subscriptionAlerts, setSubscriptionAlerts] = useState([]);
  const [dashboardManagement, setDashboardManagement] = useState([]);

  // Fetch comprehensive platform statistics
  useEffect(() => {
    const fetchPlatformStats = async () => {
      try {
        setLoading(true);

        // Fetch user statistics
        const { data: users, error: usersError } = await supabase
          .from('profiles')
          .select('subscription_tier, created_at, last_sign_in_at');

        if (usersError) throw usersError;

        // Fetch subscription data
        const { data: subscriptions, error: subsError } = await supabase
          .from('subscription_tracking')
          .select('*');

        if (subsError) throw subsError;

        // Calculate statistics
        const totalUsers = users?.length || 0;
        const freeUsers = users?.filter(u => !u.subscription_tier || u.subscription_tier === 'free').length || 0;
        const premiumUsers = users?.filter(u => u.subscription_tier === 'premium').length || 0;
        const businessUsers = users?.filter(u => u.subscription_tier === 'business').length || 0;

        // Calculate expiring subscriptions (next 30 days)
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
        
        const expiringSubscriptions = subscriptions?.filter(sub => {
          if (!sub.end_date) return false;
          const endDate = new Date(sub.end_date);
          return endDate <= thirtyDaysFromNow && endDate > new Date();
        }).length || 0;

        // Fetch recent activity
        const { data: activity, error: activityError } = await supabase
          .from('user_activity')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(10);

        setStats({
          totalUsers,
          freeUsers,
          premiumUsers,
          businessUsers,
          totalRevenue: subscriptions?.length * 399 || 0, // Simplified calculation
          activeSubscriptions: subscriptions?.filter(s => s.status === 'active').length || 0,
          expiringSubscriptions,
          totalDashboards: 16,
          activeDashboards: 13,
          whitelabelInstances: 0 // Will be implemented
        });

        setRecentActivity(activity || []);

        // Create subscription alerts
        const alerts = [];
        if (expiringSubscriptions > 0) {
          alerts.push({
            type: 'warning',
            title: 'Subscriptions Expiring Soon',
            message: `${expiringSubscriptions} subscriptions will expire in the next 30 days`,
            action: 'Send Renewal Notifications'
          });
        }

        if (premiumUsers > 0) {
          alerts.push({
            type: 'info',
            title: 'Premium User Engagement',
            message: `${premiumUsers} premium users are active`,
            action: 'View Premium Analytics'
          });
        }

        setSubscriptionAlerts(alerts);

      } catch (error) {
        console.error('Error fetching platform stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPlatformStats();
  }, []);

  // Dashboard management data
  useEffect(() => {
    const dashboards = [
      { id: 'main', name: 'Main Dashboard', status: 'active', users: stats.totalUsers, type: 'core' },
      { id: 'enhanced', name: 'Enhanced Dashboard', status: 'active', users: stats.premiumUsers, type: 'premium' },
      { id: 'simplified', name: 'Simplified Dashboard', status: 'active', users: stats.freeUsers, type: 'free' },
      { id: 'admin', name: 'Admin Dashboard', status: 'active', users: 5, type: 'admin' },
      { id: 'learning', name: 'Learning Hub', status: 'active', users: stats.totalUsers, type: 'core' },
      { id: 'challenges', name: 'Challenges Hub', status: 'active', users: stats.totalUsers, type: 'core' },
      { id: 'security', name: 'Security Insights', status: 'active', users: stats.totalUsers, type: 'core' },
      { id: 'leaderboard', name: 'Leaderboard', status: 'active', users: stats.totalUsers, type: 'core' }
    ];
    setDashboardManagement(dashboards);
  }, [stats]);

  // Handle sending renewal notifications
  const handleSendRenewalNotifications = async () => {
    try {
      // This would integrate with your email service
      console.log('Sending renewal notifications to expiring subscriptions...');
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert(`Renewal notifications sent to ${stats.expiringSubscriptions} users`);
    } catch (error) {
      console.error('Error sending notifications:', error);
    }
  };

  // Handle creating white-label instance
  const handleCreateWhiteLabel = () => {
    // This would open a modal or navigate to white-label creation
    console.log('Creating new white-label instance...');
    alert('White-label creation feature will be implemented');
  };

  // Render overview tab
  const renderOverview = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Users</p>
              <p className="text-2xl font-bold text-[#88cc14]">{stats.totalUsers.toLocaleString()}</p>
            </div>
            <FaUsers className="text-3xl text-[#88cc14]" />
          </div>
          <div className="mt-2 text-sm">
            <span className="text-green-500">Free: {stats.freeUsers}</span> | 
            <span className="text-blue-500 ml-1">Premium: {stats.premiumUsers}</span> | 
            <span className="text-purple-500 ml-1">Business: {stats.businessUsers}</span>
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Revenue</p>
              <p className="text-2xl font-bold text-green-500">${stats.totalRevenue.toLocaleString()}</p>
            </div>
            <FaDollarSign className="text-3xl text-green-500" />
          </div>
          <div className="mt-2 text-sm text-gray-500">
            Active Subscriptions: {stats.activeSubscriptions}
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Dashboards</p>
              <p className="text-2xl font-bold text-blue-500">{stats.activeDashboards}/{stats.totalDashboards}</p>
            </div>
            <FaChartLine className="text-3xl text-blue-500" />
          </div>
          <div className="mt-2 text-sm text-gray-500">
            Active Dashboards
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Expiring Soon</p>
              <p className="text-2xl font-bold text-orange-500">{stats.expiringSubscriptions}</p>
            </div>
            <FaExclamationTriangle className="text-3xl text-orange-500" />
          </div>
          <div className="mt-2">
            <button
              onClick={handleSendRenewalNotifications}
              className="text-sm bg-orange-500 text-white px-3 py-1 rounded hover:bg-orange-600"
            >
              Send Notifications
            </button>
          </div>
        </motion.div>
      </div>

      {/* Alerts and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Subscription Alerts */}
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <FaBell className="mr-2 text-orange-500" />
            Subscription Alerts
          </h3>
          <div className="space-y-3">
            {subscriptionAlerts.map((alert, index) => (
              <div key={index} className={`p-3 rounded border-l-4 ${
                alert.type === 'warning' ? 'border-orange-500 bg-orange-50' : 'border-blue-500 bg-blue-50'
              }`}>
                <h4 className="font-medium text-gray-900">{alert.title}</h4>
                <p className="text-sm text-gray-600">{alert.message}</p>
                <button className="text-sm text-blue-600 hover:underline mt-1">
                  {alert.action}
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <FaRocket className="mr-2 text-[#88cc14]" />
            Quick Actions
          </h3>
          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={handleCreateWhiteLabel}
              className="p-3 bg-[#88cc14] text-black rounded-lg hover:bg-[#7ab512] flex items-center justify-center"
            >
              <FaPlus className="mr-2" />
              Create White-Label
            </button>
            <button
              onClick={() => setActiveTab('users')}
              className="p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center justify-center"
            >
              <FaUsers className="mr-2" />
              Manage Users
            </button>
            <button
              onClick={() => setActiveTab('dashboards')}
              className="p-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 flex items-center justify-center"
            >
              <FaChartLine className="mr-2" />
              Dashboard Control
            </button>
            <button
              onClick={() => setActiveTab('analytics')}
              className="p-3 bg-green-500 text-white rounded-lg hover:bg-green-600 flex items-center justify-center"
            >
              <FaEye className="mr-2" />
              View Analytics
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // Render dashboard management tab
  const renderDashboardManagement = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Dashboard Management</h2>
        <button
          onClick={handleCreateWhiteLabel}
          className="bg-[#88cc14] text-black px-4 py-2 rounded-lg hover:bg-[#7ab512] flex items-center"
        >
          <FaPlus className="mr-2" />
          Create White-Label Instance
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {dashboardManagement.map((dashboard) => (
          <motion.div
            key={dashboard.id}
            whileHover={{ scale: 1.02 }}
            className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">{dashboard.name}</h3>
              <span className={`px-2 py-1 rounded text-xs ${
                dashboard.status === 'active' ? 'bg-green-500 text-white' : 'bg-gray-500 text-white'
              }`}>
                {dashboard.status}
              </span>
            </div>

            <div className="space-y-2 mb-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Active Users:</span>
                <span className="text-sm font-medium">{dashboard.users}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Type:</span>
                <span className={`text-sm font-medium ${
                  dashboard.type === 'premium' ? 'text-blue-500' :
                  dashboard.type === 'admin' ? 'text-orange-500' :
                  dashboard.type === 'free' ? 'text-green-500' : 'text-purple-500'
                }`}>
                  {dashboard.type}
                </span>
              </div>
            </div>

            <div className="flex space-x-2">
              <button
                onClick={() => navigate(`/${dashboard.id === 'main' ? 'dashboard' : dashboard.id === 'enhanced' ? 'enhanced-dashboard' : dashboard.id === 'simplified' ? 'simplified-dashboard' : dashboard.id}`)}
                className="flex-1 bg-blue-500 text-white px-3 py-2 rounded text-sm hover:bg-blue-600"
              >
                <FaEye className="inline mr-1" />
                View
              </button>
              <button className="flex-1 bg-gray-500 text-white px-3 py-2 rounded text-sm hover:bg-gray-600">
                <FaEdit className="inline mr-1" />
                Configure
              </button>
            </div>
          </motion.div>
        ))}
      </div>

      {/* White-Label Instances */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h3 className="text-lg font-semibold mb-4">White-Label Instances</h3>
        <div className="text-center py-8">
          <FaGlobe className="text-4xl text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 mb-4">No white-label instances created yet</p>
          <button
            onClick={handleCreateWhiteLabel}
            className="bg-[#88cc14] text-black px-6 py-3 rounded-lg hover:bg-[#7ab512]"
          >
            Create Your First White-Label Instance
          </button>
        </div>
      </div>
    </div>
  );

  // Render user management tab
  const renderUserManagement = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">User Management</h2>

      {/* User Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center">
            <div className="p-3 bg-green-500/20 rounded-lg">
              <FaUsers className="text-green-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-500">Free Users</p>
              <p className="text-xl font-bold">{stats.freeUsers}</p>
            </div>
          </div>
        </div>

        <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center">
            <div className="p-3 bg-blue-500/20 rounded-lg">
              <FaCrown className="text-blue-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-500">Premium Users</p>
              <p className="text-xl font-bold">{stats.premiumUsers}</p>
            </div>
          </div>
        </div>

        <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center">
            <div className="p-3 bg-purple-500/20 rounded-lg">
              <FaGem className="text-purple-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-500">Business Users</p>
              <p className="text-xl font-bold">{stats.businessUsers}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Subscription Expiry Management */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35]' : 'bg-white'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h3 className="text-lg font-semibold mb-4">Subscription Expiry Management</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium mb-2">Expiring in Next 30 Days</h4>
            <div className="text-3xl font-bold text-orange-500 mb-2">{stats.expiringSubscriptions}</div>
            <button
              onClick={handleSendRenewalNotifications}
              className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 flex items-center"
            >
              <FaEnvelope className="mr-2" />
              Send Renewal Notifications
            </button>
          </div>
          <div>
            <h4 className="font-medium mb-2">Active Subscriptions</h4>
            <div className="text-3xl font-bold text-green-500 mb-2">{stats.activeSubscriptions}</div>
            <button className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 flex items-center">
              <FaCheckCircle className="mr-2" />
              View Active Users
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // Navigation tabs
  const tabs = [
    { id: 'overview', name: 'Overview', icon: FaChartLine },
    { id: 'dashboards', name: 'Dashboard Management', icon: FaDatabase },
    { id: 'users', name: 'User Management', icon: FaUsers },
    { id: 'analytics', name: 'Analytics', icon: FaEye },
    { id: 'settings', name: 'Platform Settings', icon: FaCog }
  ];

  if (loading) {
    return (
      <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} flex items-center justify-center`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14] mx-auto mb-4"></div>
          <p className={`${darkMode ? 'text-white' : 'text-gray-900'}`}>Loading Super Admin Dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Header */}
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-b px-6 py-4`}>
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <FaShieldAlt className="mr-3 text-[#88cc14]" />
              Super Admin Dashboard
            </h1>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Complete platform management and control center
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/dashboard-inventory')}
              className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center"
            >
              <FaEye className="mr-2" />
              Dashboard Inventory
            </button>
            <button
              onClick={() => navigate('/')}
              className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600"
            >
              Back to Platform
            </button>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className={`w-64 ${darkMode ? 'bg-[#1A1F35] border-gray-700' : 'bg-white border-gray-200'} border-r min-h-screen`}>
          <nav className="p-4">
            <div className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center transition-colors ${
                    activeTab === tab.id
                      ? 'bg-[#88cc14] text-black'
                      : darkMode
                        ? 'hover:bg-[#252D4A] text-gray-300'
                        : 'hover:bg-gray-100 text-gray-700'
                  }`}
                >
                  <tab.icon className="mr-3" />
                  {tab.name}
                </button>
              ))}
            </div>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'dashboards' && renderDashboardManagement()}
          {activeTab === 'users' && renderUserManagement()}
          {activeTab === 'analytics' && (
            <div className="text-center py-12">
              <FaChartLine className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Advanced Analytics</h3>
              <p className="text-gray-500 mb-4">Comprehensive platform analytics and reporting</p>
              <button className="bg-[#88cc14] text-black px-6 py-3 rounded-lg hover:bg-[#7ab512]">
                Coming Soon
              </button>
            </div>
          )}
          {activeTab === 'settings' && (
            <div className="text-center py-12">
              <FaCog className="text-4xl text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Platform Settings</h3>
              <p className="text-gray-500 mb-4">Configure platform-wide settings and preferences</p>
              <button className="bg-[#88cc14] text-black px-6 py-3 rounded-lg hover:bg-[#7ab512]">
                Coming Soon
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedSuperAdminDashboard;
