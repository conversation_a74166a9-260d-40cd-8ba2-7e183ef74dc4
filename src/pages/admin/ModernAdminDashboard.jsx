import React, { useState, useEffect } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaUsers,
  FaFileAlt,
  FaBell,
  FaChartLine,
  FaCog,
  FaSignOutAlt,
  FaUserPlus,
  FaSearch,
  FaFilter,
  FaCoins,
  FaCrown,
  FaBuilding,
  FaUserEdit,
  FaUserMinus,
  FaEnvelope,
  FaHome,
  FaChevronDown,
  FaChevronUp,
  FaSync,
  FaExclamationTriangle,
  FaCheckCircle,
  FaLaptopCode,
  FaBook,
  FaGraduationCap,
  FaShieldAlt,
  FaBars,
  FaTimes,
  FaEllipsisV,
  FaCalendarAlt,
  FaUserShield,
  FaChartBar,
  FaChartPie,
  FaChartArea
} from 'react-icons/fa';
import { useAdminDashboard } from '../../contexts/AdminDashboardContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { useSubscription } from '../../contexts/SubscriptionContext';
import StatisticsOverview from '../../components/admin/StatisticsOverview';
import UserManagement from '../../components/admin/UserManagement';
import ContentManagement from '../../components/admin/ContentManagement';
import NotificationSystem from '../../components/admin/NotificationSystem';
import SystemSettings from '../../components/admin/SystemSettings';
import UserSubscriptionManager from '../../components/admin/UserSubscriptionManager';

/**
 * ModernAdminDashboard Component
 *
 * A modern, responsive admin dashboard with real-time data updates
 * and a clean, intuitive interface.
 */
const ModernAdminDashboard = () => {
  const navigate = useNavigate();
  const { user, profile } = useAuth();
  const { darkMode } = useGlobalTheme();
  const { subscriptionLevel } = useSubscription();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Use the admin dashboard context
  const {
    isAdmin,
    isSuperAdmin,
    stats,
    recentUsers,
    recentSubscriptions,
    recentActivities,
    notifications,
    loading,
    error,
    refreshing,
    lastUpdated,
    fetchDashboardData,
    formatCurrency,
    formatDate
  } = useAdminDashboard();

  // Handle refresh
  const handleRefresh = () => {
    fetchDashboardData(true);
  };

  // Handle logout
  const handleLogout = () => {
    // Clear local storage
    localStorage.removeItem('supabase.auth.token');
    localStorage.removeItem('supabase.auth.user');
    localStorage.removeItem('user_subscription');

    // Redirect to login
    navigate('/login', { replace: true });
  };

  // If not admin, redirect to home
  if (!loading && !isAdmin) {
    return <Navigate to="/" />;
  }

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return darkMode ? 'text-green-400' : 'text-green-600';
      case 'expired':
        return darkMode ? 'text-red-400' : 'text-red-600';
      case 'pending':
        return darkMode ? 'text-yellow-400' : 'text-yellow-600';
      default:
        return darkMode ? 'text-gray-400' : 'text-gray-600';
    }
  };

  // Get growth indicator
  const getGrowthIndicator = (value) => {
    if (value > 0) {
      return <span className="text-green-500 flex items-center">+{value}% <FaChevronUp className="ml-1" /></span>;
    } else if (value < 0) {
      return <span className="text-red-500 flex items-center">{value}% <FaChevronDown className="ml-1" /></span>;
    } else {
      return <span className="text-gray-500">0%</span>;
    }
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-100 text-gray-900'}`}>
      <div className="flex h-screen overflow-hidden">
        {/* Sidebar - Desktop */}
        <div
          className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border-r
            fixed inset-y-0 left-0 z-30 w-64 transition-transform duration-300 transform
            ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
            md:relative md:translate-x-0`}
        >
          <div className="flex items-center justify-between h-16 px-6 border-b border-gray-700">
            <div className="flex items-center">
              <FaShieldAlt className="text-[#88cc14] mr-2" />
              <h1 className="text-xl font-bold">Admin Panel</h1>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="md:hidden text-gray-500 hover:text-white"
            >
              <FaTimes />
            </button>
          </div>

          <div className="p-4">
            <div className="mb-6">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full bg-[#88cc14]/20 flex items-center justify-center mr-3">
                  {profile?.avatar_url ? (
                    <img
                      src={profile.avatar_url}
                      alt={profile?.username}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <span className="text-[#88cc14]">
                      {profile?.username?.charAt(0).toUpperCase() || 'A'}
                    </span>
                  )}
                </div>
                <div>
                  <p className="font-medium">{profile?.username || 'Admin User'}</p>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    {isSuperAdmin ? 'Super Admin' : 'Admin'}
                  </p>
                </div>
              </div>
            </div>

            <nav className="space-y-1">
              <button
                onClick={() => setActiveTab('overview')}
                className={`w-full flex items-center px-4 py-2.5 rounded-lg transition-colors ${
                  activeTab === 'overview'
                    ? 'bg-[#88cc14] text-black'
                    : darkMode
                      ? 'text-gray-300 hover:bg-gray-800'
                      : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <FaChartLine className="mr-3" />
                <span>Overview</span>
              </button>

              <button
                onClick={() => setActiveTab('users')}
                className={`w-full flex items-center px-4 py-2.5 rounded-lg transition-colors ${
                  activeTab === 'users'
                    ? 'bg-[#88cc14] text-black'
                    : darkMode
                      ? 'text-gray-300 hover:bg-gray-800'
                      : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <FaUsers className="mr-3" />
                <span>Users</span>
              </button>

              <button
                onClick={() => setActiveTab('subscriptions')}
                className={`w-full flex items-center px-4 py-2.5 rounded-lg transition-colors ${
                  activeTab === 'subscriptions'
                    ? 'bg-[#88cc14] text-black'
                    : darkMode
                      ? 'text-gray-300 hover:bg-gray-800'
                      : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <FaCrown className="mr-3" />
                <span>Subscriptions</span>
              </button>

              <button
                onClick={() => setActiveTab('content')}
                className={`w-full flex items-center px-4 py-2.5 rounded-lg transition-colors ${
                  activeTab === 'content'
                    ? 'bg-[#88cc14] text-black'
                    : darkMode
                      ? 'text-gray-300 hover:bg-gray-800'
                      : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <FaFileAlt className="mr-3" />
                <span>Content</span>
              </button>

              <button
                onClick={() => setActiveTab('notifications')}
                className={`w-full flex items-center px-4 py-2.5 rounded-lg transition-colors ${
                  activeTab === 'notifications'
                    ? 'bg-[#88cc14] text-black'
                    : darkMode
                      ? 'text-gray-300 hover:bg-gray-800'
                      : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <div className="relative mr-3">
                  <FaBell />
                  {stats.totalNotifications > 0 && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                      {stats.totalNotifications > 9 ? '9+' : stats.totalNotifications}
                    </span>
                  )}
                </div>
                <span>Notifications</span>
              </button>

              <button
                onClick={() => setActiveTab('settings')}
                className={`w-full flex items-center px-4 py-2.5 rounded-lg transition-colors ${
                  activeTab === 'settings'
                    ? 'bg-[#88cc14] text-black'
                    : darkMode
                      ? 'text-gray-300 hover:bg-gray-800'
                      : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <FaCog className="mr-3" />
                <span>Settings</span>
              </button>
            </nav>

            <div className="pt-6 mt-6 border-t border-gray-700">
              <button
                onClick={handleLogout}
                className={`w-full flex items-center px-4 py-2.5 rounded-lg transition-colors ${
                  darkMode
                    ? 'text-red-400 hover:bg-red-900/30'
                    : 'text-red-600 hover:bg-red-50'
                }`}
              >
                <FaSignOutAlt className="mr-3" />
                <span>Sign Out</span>
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <header className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border-b flex items-center h-16 px-6`}>
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="md:hidden mr-4 text-gray-500 hover:text-gray-600"
            >
              <FaBars />
            </button>

            <div className="flex-1">
              <h2 className="text-lg font-semibold">
                {activeTab === 'overview' && 'Dashboard Overview'}
                {activeTab === 'users' && 'User Management'}
                {activeTab === 'subscriptions' && 'Subscription Management'}
                {activeTab === 'content' && 'Content Management'}
                {activeTab === 'notifications' && 'Notification System'}
                {activeTab === 'settings' && 'System Settings'}
              </h2>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={handleRefresh}
                className={`p-2 rounded-full ${
                  darkMode
                    ? 'hover:bg-gray-800'
                    : 'hover:bg-gray-100'
                } relative`}
                disabled={refreshing}
              >
                <FaSync className={`${refreshing ? 'animate-spin' : ''}`} />
              </button>

              <div className="relative">
                <button className={`p-2 rounded-full ${
                  darkMode
                    ? 'hover:bg-gray-800'
                    : 'hover:bg-gray-100'
                } relative`}>
                  <FaBell />
                  {stats.totalNotifications > 0 && (
                    <span className="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                      {stats.totalNotifications > 9 ? '9+' : stats.totalNotifications}
                    </span>
                  )}
                </button>
              </div>

              <div className="h-8 border-l border-gray-700"></div>

              <div className="flex items-center">
                <div className="w-8 h-8 rounded-full bg-[#88cc14]/20 flex items-center justify-center">
                  {profile?.avatar_url ? (
                    <img
                      src={profile.avatar_url}
                      alt={profile?.username}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <span className="text-[#88cc14]">
                      {profile?.username?.charAt(0).toUpperCase() || 'A'}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </header>

          {/* Main Content Area */}
          <main className="flex-1 overflow-y-auto p-6">
            {loading && !refreshing ? (
              <div className="flex justify-center items-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14]"></div>
              </div>
            ) : error ? (
              <div className={`p-4 rounded-lg ${darkMode ? 'bg-red-900/20 text-red-400' : 'bg-red-100 text-red-800'}`}>
                <div className="flex items-center">
                  <FaExclamationTriangle className="mr-2" />
                  <span>{error}</span>
                </div>
              </div>
            ) : (
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  {/* Overview Tab */}
                  {activeTab === 'overview' && (
                    <div className="space-y-6">
                      {/* Stats Cards */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                          <div className="flex items-center">
                            <div className="p-3 rounded-full bg-blue-500 bg-opacity-10">
                              <FaUsers className="text-blue-500" />
                            </div>
                            <div className="ml-4">
                              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Total Users</p>
                              <div className="flex items-center">
                                <p className="text-2xl font-bold">{stats.totalUsers}</p>
                                <div className="ml-2 text-xs">
                                  {getGrowthIndicator(stats.userGrowth)}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                          <div className="flex items-center">
                            <div className="p-3 rounded-full bg-green-500 bg-opacity-10">
                              <FaCrown className="text-green-500" />
                            </div>
                            <div className="ml-4">
                              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Premium Users</p>
                              <p className="text-2xl font-bold">{stats.premiumUsers}</p>
                            </div>
                          </div>
                        </div>

                        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                          <div className="flex items-center">
                            <div className="p-3 rounded-full bg-purple-500 bg-opacity-10">
                              <FaBuilding className="text-purple-500" />
                            </div>
                            <div className="ml-4">
                              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Business Users</p>
                              <p className="text-2xl font-bold">{stats.businessUsers}</p>
                            </div>
                          </div>
                        </div>

                        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                          <div className="flex items-center">
                            <div className="p-3 rounded-full bg-yellow-500 bg-opacity-10">
                              <FaCoins className="text-yellow-500" />
                            </div>
                            <div className="ml-4">
                              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Total Revenue</p>
                              <div className="flex items-center">
                                <p className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</p>
                                <div className="ml-2 text-xs">
                                  {getGrowthIndicator(stats.revenueGrowth)}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Statistics Overview */}
                      <div className="mb-6">
                        <StatisticsOverview />
                      </div>

                      {/* Recent Activity */}
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Recent Users */}
                        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                          <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold">Recent Users</h3>
                            <button
                              onClick={() => setActiveTab('users')}
                              className="text-sm text-[#88cc14] hover:text-[#7ab811]"
                            >
                              View All
                            </button>
                          </div>

                          <div className="space-y-3">
                            {recentUsers.map(user => (
                              <div
                                key={user.id}
                                className={`flex items-center p-2 rounded-lg ${
                                  darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
                                }`}
                              >
                                <div className="w-10 h-10 rounded-full bg-[#88cc14]/20 flex items-center justify-center mr-3">
                                  {user.avatar_url ? (
                                    <img
                                      src={user.avatar_url}
                                      alt={user.username}
                                      className="w-full h-full rounded-full object-cover"
                                    />
                                  ) : (
                                    <span className="text-[#88cc14]">
                                      {user.username?.charAt(0).toUpperCase() || 'U'}
                                    </span>
                                  )}
                                </div>
                                <div className="flex-1">
                                  <p className="font-medium">{user.username}</p>
                                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                    {user.full_name || 'No name provided'}
                                  </p>
                                </div>
                                <div>
                                  <span className={`px-2 py-1 rounded text-xs ${
                                    user.subscription_tier === 'premium'
                                      ? 'bg-green-100 text-green-800'
                                      : user.subscription_tier === 'business'
                                        ? 'bg-purple-100 text-purple-800'
                                        : 'bg-blue-100 text-blue-800'
                                  }`}>
                                    {user.subscription_tier.charAt(0).toUpperCase() + user.subscription_tier.slice(1)}
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Recent Subscriptions */}
                        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                          <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold">Recent Subscriptions</h3>
                            <button
                              onClick={() => setActiveTab('subscriptions')}
                              className="text-sm text-[#88cc14] hover:text-[#7ab811]"
                            >
                              View All
                            </button>
                          </div>

                          <div className="space-y-3">
                            {recentSubscriptions.map(sub => (
                              <div
                                key={sub.id}
                                className={`flex items-center p-2 rounded-lg ${
                                  darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
                                }`}
                              >
                                <div className="w-10 h-10 rounded-full bg-[#88cc14]/20 flex items-center justify-center mr-3">
                                  {sub.profiles?.avatar_url ? (
                                    <img
                                      src={sub.profiles.avatar_url}
                                      alt={sub.profiles.username}
                                      className="w-full h-full rounded-full object-cover"
                                    />
                                  ) : (
                                    <span className="text-[#88cc14]">
                                      {sub.profiles?.username?.charAt(0).toUpperCase() || 'U'}
                                    </span>
                                  )}
                                </div>
                                <div className="flex-1">
                                  <p className="font-medium">{sub.profiles?.username || 'Unknown User'}</p>
                                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                    {sub.subscription_plans?.name || 'Unknown Plan'}
                                  </p>
                                </div>
                                <div className="text-right">
                                  <p className="font-medium">{formatCurrency(sub.amount || 0)}</p>
                                  <p className={`text-sm ${getStatusColor(sub.status)}`}>
                                    {sub.status?.charAt(0).toUpperCase() + sub.status?.slice(1) || 'Unknown'}
                                  </p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* Recent Activities */}
                      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                        <h3 className="text-lg font-semibold mb-4">Recent Activities</h3>

                        <div className="space-y-4">
                          {recentActivities.map(activity => (
                            <div
                              key={activity.id}
                              className={`flex items-start p-3 rounded-lg ${
                                darkMode ? 'bg-gray-800/50' : 'bg-gray-50'
                              }`}
                            >
                              <div className="w-10 h-10 rounded-full bg-[#88cc14]/20 flex items-center justify-center mr-3">
                                {activity.profiles?.avatar_url ? (
                                  <img
                                    src={activity.profiles.avatar_url}
                                    alt={activity.profiles.username}
                                    className="w-full h-full rounded-full object-cover"
                                  />
                                ) : (
                                  <span className="text-[#88cc14]">
                                    {activity.profiles?.username?.charAt(0).toUpperCase() || 'U'}
                                  </span>
                                )}
                              </div>
                              <div className="flex-1">
                                <div className="flex justify-between">
                                  <p className="font-medium">{activity.profiles?.username || 'Unknown User'}</p>
                                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                    {formatDate(activity.created_at)}
                                  </p>
                                </div>
                                <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                                  {activity.activity_type === 'challenge_completed' && 'Completed a challenge'}
                                  {activity.activity_type === 'module_completed' && 'Completed a learning module'}
                                  {activity.activity_type === 'subscription_changed' && 'Changed subscription'}
                                  {activity.activity_type === 'login' && 'Logged in'}
                                  {activity.activity_type === 'signup' && 'Signed up'}
                                </p>
                                {activity.activity_data && (
                                  <p className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                    {typeof activity.activity_data === 'string'
                                      ? activity.activity_data
                                      : JSON.stringify(activity.activity_data)}
                                  </p>
                                )}
                              </div>
                            </div>
                          ))}

                          {recentActivities.length === 0 && (
                            <p className={`text-center py-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              No recent activities
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Users Tab */}
                  {activeTab === 'users' && (
                    <UserManagement />
                  )}

                  {/* Subscriptions Tab */}
                  {activeTab === 'subscriptions' && (
                    <UserSubscriptionManager />
                  )}

                  {/* Content Tab */}
                  {activeTab === 'content' && (
                    <ContentManagement />
                  )}

                  {/* Notifications Tab */}
                  {activeTab === 'notifications' && (
                    <NotificationSystem />
                  )}

                  {/* Settings Tab */}
                  {activeTab === 'settings' && (
                    <SystemSettings />
                  )}
                </motion.div>
              </AnimatePresence>
            )}
          </main>

          {/* Footer */}
          <footer className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border-t px-6 py-3`}>
            <div className="flex justify-between items-center">
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                &copy; {new Date().getFullYear()} XCerberus Admin Panel
              </p>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                Last updated: {formatDate(lastUpdated)}
              </p>
            </div>
          </footer>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {!sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
          onClick={() => setSidebarOpen(true)}
        ></div>
      )}
    </div>
  );
};

export default ModernAdminDashboard;
