import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../../lib/supabase';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { useSubscription } from '../../contexts/SubscriptionContext';
import UserSubscriptionManager from '../../components/admin/UserSubscriptionManager';

/**
 * SimpleAdminDashboard Component
 * 
 * A simplified admin dashboard focused on subscription management.
 */
const SimpleAdminDashboard = () => {
  const navigate = useNavigate();
  const { user, profile } = useAuth();
  const { darkMode } = useGlobalTheme();
  const { subscriptionLevel } = useSubscription();
  const [activeTab, setActiveTab] = useState('subscriptions');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Check if user is admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) {
        navigate('/login');
        return;
      }

      try {
        // For testing purposes, we'll assume all logged-in users can access the admin dashboard
        // In a real app, you would check if the user has admin privileges
      } catch (error) {
        console.error('Error checking admin status:', error);
        setError('You do not have permission to access this page');
      }
    };

    checkAdminStatus();
  }, [user, navigate]);

  // Handle logout
  const handleLogout = () => {
    // Clear local storage
    localStorage.removeItem('supabase.auth.token');
    localStorage.removeItem('supabase.auth.user');
    localStorage.removeItem('user_subscription');
    
    // Redirect to login
    navigate('/login', { replace: true });
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-100 text-gray-900'}`}>
      <header className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border-b px-6 py-4`}>
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="text-2xl font-bold">Admin Dashboard</h1>
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full bg-[#88cc14]/20 flex items-center justify-center mr-2">
                {profile?.avatar_url ? (
                  <img 
                    src={profile.avatar_url} 
                    alt={profile?.username} 
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <span className="text-[#88cc14]">
                    {profile?.username?.charAt(0).toUpperCase() || 'A'}
                  </span>
                )}
              </div>
              <span>{profile?.username || 'Admin'}</span>
            </div>
            <button
              onClick={handleLogout}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
            >
              Logout
            </button>
          </div>
        </div>
      </header>

      <main className="container mx-auto py-6 px-4">
        {error ? (
          <div className="bg-red-100 text-red-800 p-4 rounded-lg mb-6">
            {error}
          </div>
        ) : (
          <div>
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-4">Subscription Management</h2>
              <p className="mb-4">
                Use this interface to manage user subscription tiers. Changes will be applied immediately
                and will affect what features users can access.
              </p>
            </div>
            
            <div className="bg-white rounded-lg shadow-md p-6">
              <UserSubscriptionManager />
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default SimpleAdminDashboard;
