import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import { FaUsers, FaCoins, FaChartLine, FaUserShield, FaFileAlt, FaLaptopCode, FaCog, FaBell, FaPalette, FaBuilding, FaUserCog } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { Navigate, useNavigate } from 'react-router-dom';

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { user, profile } = useAuth();
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('overview');
  const [stats, setStats] = useState({
    totalUsers: 0,
    premiumUsers: 0,
    businessUsers: 0,
    totalRevenue: 0,
    activeSubscriptions: 0,
    totalChallenges: 0,
    totalModules: 0,
    totalNotifications: 0,
    pendingApprovals: 0
  });
  const [recentUsers, setRecentUsers] = useState([]);
  const [recentSubscriptions, setRecentSubscriptions] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [pendingContent, setPendingContent] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);

  // Check if user is admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) return;

      try {
        // For testing purposes, we'll check user_metadata directly
        // In production, use the database query below
        const isAdminUser = user.user_metadata?.is_admin === true;
        const isSuperAdminUser = user.user_metadata?.is_super_admin === true;

        setIsAdmin(isAdminUser || isSuperAdminUser);
        setIsSuperAdmin(isSuperAdminUser);

        // Database query for production
        /*
        const { data, error } = await supabase
          .from('profiles')
          .select('role_id, user_roles(name)')
          .eq('id', user.id)
          .single();

        if (error) throw error;

        const isAdminUser = data?.user_roles?.name === 'admin' || data?.user_roles?.name === 'super_admin';
        const isSuperAdminUser = data?.user_roles?.name === 'super_admin';
        setIsAdmin(isAdminUser);
        setIsSuperAdmin(isSuperAdminUser);
        */

        if (!isAdminUser && !isSuperAdminUser) {
          setError('You do not have permission to access this page');
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
        setError(error.message);
      }
    };

    checkAdminStatus();
  }, [user]);

  // Fetch dashboard data
  useEffect(() => {
    if (!isAdmin) return;

    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch user stats
        const { data: userData, error: userError } = await supabase
          .from('profiles')
          .select('subscription_tier, count')
          .group('subscription_tier');

        if (userError) throw userError;

        // Fetch subscription stats
        const { data: subData, error: subError } = await supabase
          .from('subscription_tracking')
          .select('status, count')
          .eq('status', 'active')
          .group('status');

        if (subError) throw subError;

        // Fetch revenue stats
        const { data: revenueData, error: revenueError } = await supabase
          .from('subscription_tracking')
          .select('payment_id')
          .not('payment_id', 'is', null);

        if (revenueError) throw revenueError;

        // Fetch challenge stats
        const { data: challengeData, error: challengeError } = await supabase
          .from('challenges')
          .select('count');

        if (challengeError) throw challengeError;

        // Fetch module stats
        const { data: moduleData, error: moduleError } = await supabase
          .from('learning_modules')
          .select('count');

        if (moduleError) throw moduleError;

        // Fetch recent users
        const { data: recentUserData, error: recentUserError } = await supabase
          .from('profiles')
          .select('id, username, full_name, subscription_tier, created_at')
          .order('created_at', { ascending: false })
          .limit(5);

        if (recentUserError) throw recentUserError;

        // Fetch recent subscriptions
        const { data: recentSubData, error: recentSubError } = await supabase
          .from('subscription_tracking')
          .select(`
            id,
            start_date,
            end_date,
            status,
            user:profiles(username, full_name),
            plan:subscription_plans(name, price)
          `)
          .order('created_at', { ascending: false })
          .limit(5);

        if (recentSubError) throw recentSubError;

        // Process user stats
        const totalUsers = userData.reduce((sum, item) => sum + item.count, 0);
        const premiumUsers = userData.find(item => item.subscription_tier === 'premium')?.count || 0;
        const businessUsers = userData.find(item => item.subscription_tier === 'business')?.count || 0;

        // Process subscription stats
        const activeSubscriptions = subData[0]?.count || 0;

        // Set stats
        setStats({
          totalUsers,
          premiumUsers,
          businessUsers,
          totalRevenue: revenueData.length * 399, // Simplified calculation
          activeSubscriptions,
          totalChallenges: challengeData[0]?.count || 0,
          totalModules: moduleData[0]?.count || 0
        });

        setRecentUsers(recentUserData);
        setRecentSubscriptions(recentSubData);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [isAdmin]);

  // If not admin, redirect to home
  if (!loading && !isAdmin) {
    return <Navigate to="/" />;
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120] text-white' : 'bg-gray-50 text-gray-900'} pt-24 pb-16`}>
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row">
          {/* Sidebar */}
          <div className={`w-full md:w-64 mb-6 md:mb-0 md:mr-6 ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
            <h2 className="text-xl font-bold mb-4">Admin Panel</h2>
            <nav>
              <ul className="space-y-2">
                <li>
                  <button
                    onClick={() => setActiveTab('overview')}
                    className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                      activeTab === 'overview'
                        ? 'bg-[#88cc14] text-black'
                        : darkMode ? 'hover:bg-[#252D4A]' : 'hover:bg-gray-100'
                    }`}
                  >
                    <FaChartLine className="mr-2" /> Overview
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => setActiveTab('users')}
                    className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                      activeTab === 'users'
                        ? 'bg-[#88cc14] text-black'
                        : darkMode ? 'hover:bg-[#252D4A]' : 'hover:bg-gray-100'
                    }`}
                  >
                    <FaUsers className="mr-2" /> Users
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => setActiveTab('subscriptions')}
                    className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                      activeTab === 'subscriptions'
                        ? 'bg-[#88cc14] text-black'
                        : darkMode ? 'hover:bg-[#252D4A]' : 'hover:bg-gray-100'
                    }`}
                  >
                    <FaUserShield className="mr-2" /> Subscriptions
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => setActiveTab('challenges')}
                    className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                      activeTab === 'challenges'
                        ? 'bg-[#88cc14] text-black'
                        : darkMode ? 'hover:bg-[#252D4A]' : 'hover:bg-gray-100'
                    }`}
                  >
                    <FaLaptopCode className="mr-2" /> Challenges
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => setActiveTab('modules')}
                    className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                      activeTab === 'modules'
                        ? 'bg-[#88cc14] text-black'
                        : darkMode ? 'hover:bg-[#252D4A]' : 'hover:bg-gray-100'
                    }`}
                  >
                    <FaFileAlt className="mr-2" /> Learning Modules
                  </button>
                </li>

                {/* Super Admin Features */}
                {isSuperAdmin && (
                  <>
                    <li className="mt-4 mb-2 px-4 text-sm text-gray-500">
                      Super Admin Features
                    </li>
                    <li>
                      <button
                        onClick={() => setActiveTab('notifications')}
                        className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                          activeTab === 'notifications'
                            ? 'bg-[#88cc14] text-black'
                            : darkMode ? 'hover:bg-[#252D4A]' : 'hover:bg-gray-100'
                        }`}
                      >
                        <FaBell className="mr-2" /> Notifications
                      </button>
                    </li>
                    <li>
                      <button
                        onClick={() => setActiveTab('customization')}
                        className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                          activeTab === 'customization'
                            ? 'bg-[#88cc14] text-black'
                            : darkMode ? 'hover:bg-[#252D4A]' : 'hover:bg-gray-100'
                        }`}
                      >
                        <FaPalette className="mr-2" /> Page Customization
                      </button>
                    </li>
                    <li>
                      <button
                        onClick={() => setActiveTab('business')}
                        className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                          activeTab === 'business'
                            ? 'bg-[#88cc14] text-black'
                            : darkMode ? 'hover:bg-[#252D4A]' : 'hover:bg-gray-100'
                        }`}
                      >
                        <FaBuilding className="mr-2" /> Business Setup
                      </button>
                    </li>
                    <li>
                      <button
                        onClick={() => setActiveTab('user-management')}
                        className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                          activeTab === 'user-management'
                            ? 'bg-[#88cc14] text-black'
                            : darkMode ? 'hover:bg-[#252D4A]' : 'hover:bg-gray-100'
                        }`}
                      >
                        <FaUserCog className="mr-2" /> User Management
                      </button>
                    </li>
                  </>
                )}
                <li>
                  <button
                    onClick={() => setActiveTab('revenue')}
                    className={`w-full text-left px-4 py-2 rounded-md flex items-center ${
                      activeTab === 'revenue'
                        ? 'bg-[#88cc14] text-black'
                        : darkMode ? 'hover:bg-[#252D4A]' : 'hover:bg-gray-100'
                    }`}
                  >
                    <FaCoins className="mr-2" /> Revenue
                  </button>
                </li>
              </ul>
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {loading ? (
              <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
                <p>Loading dashboard data...</p>
              </div>
            ) : error ? (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <p>{error}</p>
              </div>
            ) : (
              <>
                {/* Overview Tab */}
                {activeTab === 'overview' && (
                  <div>
                    <h1 className="text-2xl font-bold mb-6">Dashboard Overview</h1>

                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                        <div className="flex items-center">
                          <div className="p-3 rounded-full bg-blue-500 bg-opacity-10">
                            <FaUsers className="text-blue-500" />
                          </div>
                          <div className="ml-4">
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Total Users</p>
                            <p className="text-2xl font-bold">{stats.totalUsers}</p>
                          </div>
                        </div>
                      </div>

                      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                        <div className="flex items-center">
                          <div className="p-3 rounded-full bg-green-500 bg-opacity-10">
                            <FaUserShield className="text-green-500" />
                          </div>
                          <div className="ml-4">
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Premium Users</p>
                            <p className="text-2xl font-bold">{stats.premiumUsers}</p>
                          </div>
                        </div>
                      </div>

                      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                        <div className="flex items-center">
                          <div className="p-3 rounded-full bg-purple-500 bg-opacity-10">
                            <FaCoins className="text-purple-500" />
                          </div>
                          <div className="ml-4">
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Total Revenue</p>
                            <p className="text-2xl font-bold">₹{stats.totalRevenue}</p>
                          </div>
                        </div>
                      </div>

                      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                        <div className="flex items-center">
                          <div className="p-3 rounded-full bg-yellow-500 bg-opacity-10">
                            <FaLaptopCode className="text-yellow-500" />
                          </div>
                          <div className="ml-4">
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Active Challenges</p>
                            <p className="text-2xl font-bold">{stats.totalChallenges}</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Recent Activity */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Recent Users */}
                      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                        <h2 className="text-xl font-bold mb-4">Recent Users</h2>
                        <div className="overflow-x-auto">
                          <table className="w-full">
                            <thead>
                              <tr className={`${darkMode ? 'border-gray-800' : 'border-gray-200'} border-b`}>
                                <th className="px-4 py-2 text-left">Username</th>
                                <th className="px-4 py-2 text-left">Tier</th>
                                <th className="px-4 py-2 text-left">Joined</th>
                              </tr>
                            </thead>
                            <tbody>
                              {recentUsers.map(user => (
                                <tr key={user.id} className={`${darkMode ? 'border-gray-800 hover:bg-[#252D4A]' : 'border-gray-200 hover:bg-gray-100'} border-b`}>
                                  <td className="px-4 py-2">{user.username}</td>
                                  <td className="px-4 py-2">
                                    <span className={`px-2 py-1 rounded text-xs ${
                                      user.subscription_tier === 'premium'
                                        ? 'bg-green-100 text-green-800'
                                        : user.subscription_tier === 'business'
                                          ? 'bg-purple-100 text-purple-800'
                                          : 'bg-gray-100 text-gray-800'
                                    }`}>
                                      {user.subscription_tier}
                                    </span>
                                  </td>
                                  <td className="px-4 py-2">{new Date(user.created_at).toLocaleDateString()}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>

                      {/* Recent Subscriptions */}
                      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-lg border p-4`}>
                        <h2 className="text-xl font-bold mb-4">Recent Subscriptions</h2>
                        <div className="overflow-x-auto">
                          <table className="w-full">
                            <thead>
                              <tr className={`${darkMode ? 'border-gray-800' : 'border-gray-200'} border-b`}>
                                <th className="px-4 py-2 text-left">User</th>
                                <th className="px-4 py-2 text-left">Plan</th>
                                <th className="px-4 py-2 text-left">Status</th>
                                <th className="px-4 py-2 text-left">Expires</th>
                              </tr>
                            </thead>
                            <tbody>
                              {recentSubscriptions.map(sub => (
                                <tr key={sub.id} className={`${darkMode ? 'border-gray-800 hover:bg-[#252D4A]' : 'border-gray-200 hover:bg-gray-100'} border-b`}>
                                  <td className="px-4 py-2">{sub.user.username}</td>
                                  <td className="px-4 py-2">{sub.plan.name}</td>
                                  <td className="px-4 py-2">
                                    <span className={`px-2 py-1 rounded text-xs ${
                                      sub.status === 'active'
                                        ? 'bg-green-100 text-green-800'
                                        : sub.status === 'expired'
                                          ? 'bg-red-100 text-red-800'
                                          : 'bg-yellow-100 text-yellow-800'
                                    }`}>
                                      {sub.status}
                                    </span>
                                  </td>
                                  <td className="px-4 py-2">{new Date(sub.end_date).toLocaleDateString()}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Users Tab */}
                {activeTab === 'users' && (
                  <div>
                    <h1 className="text-2xl font-bold mb-6">User Management</h1>
                    <p>User management interface will be implemented here.</p>
                  </div>
                )}

                {/* Subscriptions Tab */}
                {activeTab === 'subscriptions' && (
                  <div>
                    <h1 className="text-2xl font-bold mb-6">Subscription Management</h1>
                    <p>Subscription management interface will be implemented here.</p>
                  </div>
                )}

                {/* Challenges Tab */}
                {activeTab === 'challenges' && (
                  <div>
                    <h1 className="text-2xl font-bold mb-6">Challenge Management</h1>
                    <p>Challenge management interface will be implemented here.</p>
                  </div>
                )}

                {/* Modules Tab */}
                {activeTab === 'modules' && (
                  <div>
                    <h1 className="text-2xl font-bold mb-6">Learning Module Management</h1>
                    <p>Learning module management interface will be implemented here.</p>
                  </div>
                )}

                {/* Revenue Tab */}
                {activeTab === 'revenue' && (
                  <div>
                    <h1 className="text-2xl font-bold mb-6">Revenue Analytics</h1>
                    <p>Revenue analytics interface will be implemented here.</p>
                  </div>
                )}
                {/* Super Admin Tabs */}
                {activeTab === 'notifications' && (
                  <div>
                    <h2 className="text-2xl font-bold mb-6">Notification Management</h2>
                    <div className="text-center py-12">
                      <p className="text-gray-500">Notification management features coming soon</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  export default AdminDashboard;
