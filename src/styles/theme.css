/* Cyberpunk Theme */
:root {
  /* Common colors */
  --neon-green: #88cc14;
  --neon-bright: #00ff41;
  --neon-hover: #7ab811;
  --neon-glow: rgba(136, 204, 20, 0.2);
  --cyber-black: #0B1120;
  --cyber-darker: #080B14;
  --grid-color: rgba(136, 204, 20, 0.1);

  /* Light Theme Variables */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --card-bg: #ffffff;
  --card-border: #e2e8f0;
  --input-bg: #f8fafc;
  --input-border: #e2e8f0;
  --accent-color: #88cc14;
  --accent-hover: #7ab811;
  --shadow-color: rgba(0, 0, 0, 0.05);
}

/* Dark Theme */
.dark {
  --bg-primary: #0B1120;
  --bg-secondary: #1A1F35;
  --bg-tertiary: #252D4A;
  --text-primary: #ffffff;
  --text-secondary: #94a3b8;
  --border-color: #334155;
  --card-bg: #1A1F35;
  --card-border: #334155;
  --input-bg: #0F172A;
  --input-border: #334155;
  --accent-color: #88cc14;
  --accent-hover: #7ab811;
  --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Base Styles */
body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s, color 0.3s;
}

/* Theme Utility Classes */
.theme-card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  color: var(--text-primary);
  box-shadow: 0 4px 6px var(--shadow-color);
  transition: background-color 0.3s, border-color 0.3s, color 0.3s, box-shadow 0.3s;
}

.theme-input {
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  color: var(--text-primary);
  transition: background-color 0.3s, border-color 0.3s, color 0.3s;
}

.theme-input:focus {
  border-color: var(--accent-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(136, 204, 20, 0.2);
}

.theme-button-primary {
  background-color: var(--accent-color);
  color: #000000;
  transition: background-color 0.3s;
}

.theme-button-primary:hover {
  background-color: var(--accent-hover);
}

.theme-button-secondary {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  transition: background-color 0.3s, border-color 0.3s, color 0.3s;
}

.theme-button-secondary:hover {
  background-color: var(--bg-tertiary);
}

.theme-table {
  background-color: var(--card-bg);
  border-color: var(--card-border);
  color: var(--text-primary);
  transition: background-color 0.3s, border-color 0.3s, color 0.3s;
}

.theme-table th {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--card-border);
}

.theme-table td {
  border-color: var(--card-border);
}

.theme-table tr:hover {
  background-color: var(--bg-tertiary);
}

/* Text Colors */
.theme-text-primary {
  color: var(--text-primary);
}

.theme-text-secondary {
  color: var(--text-secondary);
}

/* Border Colors */
.theme-border {
  border-color: var(--border-color);
}

/* Background Colors */
.theme-bg-primary {
  background-color: var(--bg-primary);
}

.theme-bg-secondary {
  background-color: var(--bg-secondary);
}

.theme-bg-tertiary {
  background-color: var(--bg-tertiary);
}

/* Cyberpunk Styles */
.cyber-body {
  @apply bg-cyber-black text-white font-mono;
  background-image:
    linear-gradient(var(--grid-color) 1px, transparent 1px),
    linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Terminal Styles */
.terminal-window {
  @apply bg-black/90 rounded-lg overflow-hidden border border-primary/20;
  box-shadow: 0 0 20px rgba(136, 204, 20, 0.1);
  height: 500px;
}

.terminal-header {
  @apply bg-black/50 backdrop-blur-sm border-b border-primary/20 p-3;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.terminal-body {
  @apply p-4 h-[calc(100%-48px)] overflow-auto;
  font-family: 'JetBrains Mono', monospace;
}

/* Mission Card */
.mission-card {
  @apply bg-black/30 backdrop-blur-sm border border-primary/20 rounded-xl p-6;
  transition: all 0.3s ease;
}

.mission-card:hover {
  @apply border-primary;
  box-shadow: 0 0 30px rgba(136, 204, 20, 0.2);
  transform: translateY(-5px);
}

/* Neon Text Effects */
.neon-text {
  color: var(--neon-green);
  text-shadow:
    0 0 5px var(--neon-green),
    0 0 10px var(--neon-green),
    0 0 20px var(--neon-green);
}

/* Buttons */
.cyber-button {
  @apply bg-black/50 text-primary border border-primary/30 font-bold py-3 px-6 rounded-lg;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.cyber-button:hover {
  @apply bg-primary text-black;
  box-shadow: 0 0 20px rgba(136, 204, 20, 0.5);
}

.cyber-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    120deg,
    transparent,
    rgba(136, 204, 20, 0.4),
    transparent
  );
  transition: 0.6s;
}

.cyber-button:hover::before {
  left: 100%;
}

/* Panels */
.cyber-panel {
  @apply bg-black/30 backdrop-blur-sm border border-primary/20 rounded-lg p-6;
  box-shadow: 0 0 20px rgba(136, 204, 20, 0.1);
}

/* Progress Bars */
.cyber-progress {
  @apply h-2 bg-black/30 rounded-full overflow-hidden;
}

.cyber-progress-bar {
  @apply h-full bg-primary transition-all duration-300;
  box-shadow: 0 0 10px rgba(136, 204, 20, 0.5);
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-black/30;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary/30 rounded;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary/50;
}

/* Animations */
@keyframes scanline {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

.scanline::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(136, 204, 20, 0.1);
  animation: scanline 2s linear infinite;
}

/* Terminal Text */
.terminal-text {
  @apply font-mono text-primary;
}

/* Status Indicators */
.status-indicator {
  @apply w-2 h-2 rounded-full;
}

.status-active {
  @apply bg-primary animate-pulse;
}

.status-inactive {
  @apply bg-red-500;
}

.status-pending {
  @apply bg-yellow-500;
}

/* Glassmorphism */
.glass {
  @apply bg-black/30 backdrop-blur-md;
  border: 1px solid rgba(136, 204, 20, 0.2);
  box-shadow: 0 8px 32px rgba(136, 204, 20, 0.1);
}

/* Neon Text Effect */
.neon-text {
  color: #88cc14;
  text-shadow:
    0 0 7px rgba(136, 204, 20, 0.7),
    0 0 10px rgba(136, 204, 20, 0.5),
    0 0 21px rgba(136, 204, 20, 0.3);
}

/* Hover Underline Animation */
.hover-underline {
  position: relative;
}

.hover-underline::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: #88cc14;
  box-shadow: 0 0 10px rgba(136, 204, 20, 0.5);
  transition: width 0.3s ease;
}

.hover-underline:hover::after {
  width: 100%;
}

/* Loading Effects */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba(136, 204, 20, 0.1) 25%,
    rgba(136, 204, 20, 0.2) 50%,
    rgba(136, 204, 20, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Terminal Theme */
.xterm-viewport::-webkit-scrollbar {
  width: 8px;
}

.xterm-viewport::-webkit-scrollbar-track {
  background: rgba(136, 204, 20, 0.1);
}

.xterm-viewport::-webkit-scrollbar-thumb {
  background: rgba(136, 204, 20, 0.3);
  border-radius: 4px;
}

.xterm-viewport::-webkit-scrollbar-thumb:hover {
  background: rgba(136, 204, 20, 0.5);
}

/* Mission Card Hover Effects */
.mission-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mission-card:hover {
  transform: translateY(-5px);
  box-shadow:
    0 0 30px rgba(136, 204, 20, 0.2),
    0 0 60px rgba(136, 204, 20, 0.1);
}

.mission-card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(136, 204, 20, 0.1),
    transparent
  );
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.mission-card:hover::before {
  transform: translateX(100%);
}