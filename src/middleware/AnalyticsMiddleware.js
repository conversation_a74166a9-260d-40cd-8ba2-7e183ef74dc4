/**
 * XCerberus Analytics Middleware
 * 
 * Comprehensive tracking system that monitors every user interaction,
 * learning progress, platform usage, and behavioral analytics.
 */

class AnalyticsMiddleware {
  constructor() {
    this.events = [];
    this.userSessions = new Map();
    this.realTimeMetrics = {
      activeUsers: 0,
      currentSessions: 0,
      totalEvents: 0,
      platformUsage: {},
      learningPaths: {},
      securityDomains: {
        fundamentals: 0,
        blueTeam: 0,
        redTeam: 0,
        purpleTeam: 0,
        forensics: 0,
        malwareAnalysis: 0,
        networkSecurity: 0,
        webSecurity: 0,
        cloudSecurity: 0,
        iot: 0
      }
    };
    this.initializeTracking();
  }

  /**
   * Initialize comprehensive tracking system
   */
  initializeTracking() {
    // Track page views
    this.trackPageViews();
    
    // Track user interactions
    this.trackUserInteractions();
    
    // Track learning progress
    this.trackLearningProgress();
    
    // Track Web3 interactions
    this.trackWeb3Activities();
    
    // Track performance metrics
    this.trackPerformanceMetrics();
    
    console.log('🔍 Analytics Middleware initialized - Tracking everything!');
  }

  /**
   * Track all page views and navigation
   */
  trackPageViews() {
    // Monitor route changes
    window.addEventListener('popstate', (event) => {
      this.logEvent('page_view', {
        path: window.location.pathname,
        timestamp: Date.now(),
        referrer: document.referrer,
        userAgent: navigator.userAgent
      });
    });

    // Track dashboard usage
    this.trackDashboardUsage();
  }

  /**
   * Track which dashboards users are using
   */
  trackDashboardUsage() {
    const dashboards = [
      'main-dashboard', 'enhanced-dashboard', 'simplified-dashboard',
      'web3-dashboard', 'admin-dashboard', 'super-admin-dashboard',
      'analytics-dashboard', 'content-management', 'virtual-labs',
      'certifications', 'assessments', 'security-insights'
    ];

    dashboards.forEach(dashboard => {
      if (window.location.pathname.includes(dashboard)) {
        this.realTimeMetrics.platformUsage[dashboard] = 
          (this.realTimeMetrics.platformUsage[dashboard] || 0) + 1;
      }
    });
  }

  /**
   * Track user interactions and behaviors
   */
  trackUserInteractions() {
    // Click tracking
    document.addEventListener('click', (event) => {
      this.logEvent('user_click', {
        element: event.target.tagName,
        className: event.target.className,
        id: event.target.id,
        text: event.target.textContent?.substring(0, 50),
        coordinates: { x: event.clientX, y: event.clientY },
        timestamp: Date.now()
      });
    });

    // Form submissions
    document.addEventListener('submit', (event) => {
      this.logEvent('form_submission', {
        formId: event.target.id,
        formAction: event.target.action,
        timestamp: Date.now()
      });
    });

    // Scroll tracking
    let scrollTimeout;
    window.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        this.logEvent('scroll_depth', {
          scrollY: window.scrollY,
          scrollPercent: (window.scrollY / document.body.scrollHeight) * 100,
          timestamp: Date.now()
        });
      }, 100);
    });
  }

  /**
   * Track learning progress and domain focus
   */
  trackLearningProgress() {
    // Track course completions
    this.trackCourseProgress();
    
    // Track lab activities
    this.trackLabActivities();
    
    // Track assessment performance
    this.trackAssessmentPerformance();
    
    // Track security domain preferences
    this.trackSecurityDomains();
  }

  /**
   * Track which security domains users are focusing on
   */
  trackSecurityDomains() {
    const domainKeywords = {
      fundamentals: ['basic', 'intro', 'fundamental', 'beginner'],
      blueTeam: ['defense', 'monitoring', 'soc', 'incident', 'blue'],
      redTeam: ['penetration', 'exploit', 'attack', 'red', 'offensive'],
      purpleTeam: ['purple', 'collaboration', 'integrated'],
      forensics: ['forensic', 'investigation', 'evidence'],
      malwareAnalysis: ['malware', 'reverse', 'analysis'],
      networkSecurity: ['network', 'firewall', 'ids', 'ips'],
      webSecurity: ['web', 'application', 'owasp', 'xss', 'sql'],
      cloudSecurity: ['cloud', 'aws', 'azure', 'gcp'],
      iot: ['iot', 'embedded', 'hardware']
    };

    // Analyze current page content to determine domain
    const pageContent = document.body.textContent.toLowerCase();
    
    Object.entries(domainKeywords).forEach(([domain, keywords]) => {
      const matches = keywords.filter(keyword => pageContent.includes(keyword));
      if (matches.length > 0) {
        this.realTimeMetrics.securityDomains[domain]++;
        this.logEvent('domain_engagement', {
          domain,
          keywords: matches,
          timestamp: Date.now()
        });
      }
    });
  }

  /**
   * Track course progress and completion
   */
  trackCourseProgress() {
    // Monitor course module completions
    const courseEvents = ['module_start', 'module_complete', 'course_complete'];
    
    courseEvents.forEach(eventType => {
      document.addEventListener(eventType, (event) => {
        this.logEvent('learning_progress', {
          type: eventType,
          courseId: event.detail?.courseId,
          moduleId: event.detail?.moduleId,
          progress: event.detail?.progress,
          timeSpent: event.detail?.timeSpent,
          timestamp: Date.now()
        });
      });
    });
  }

  /**
   * Track virtual lab activities
   */
  trackLabActivities() {
    const labEvents = ['lab_start', 'lab_complete', 'tool_usage', 'command_execution'];
    
    labEvents.forEach(eventType => {
      document.addEventListener(eventType, (event) => {
        this.logEvent('lab_activity', {
          type: eventType,
          labId: event.detail?.labId,
          tool: event.detail?.tool,
          command: event.detail?.command,
          success: event.detail?.success,
          timestamp: Date.now()
        });
      });
    });
  }

  /**
   * Track assessment performance
   */
  trackAssessmentPerformance() {
    document.addEventListener('assessment_complete', (event) => {
      this.logEvent('assessment_performance', {
        assessmentId: event.detail?.assessmentId,
        score: event.detail?.score,
        timeSpent: event.detail?.timeSpent,
        questionsCorrect: event.detail?.questionsCorrect,
        totalQuestions: event.detail?.totalQuestions,
        difficulty: event.detail?.difficulty,
        timestamp: Date.now()
      });
    });
  }

  /**
   * Track Web3 interactions
   */
  trackWeb3Activities() {
    const web3Events = [
      'wallet_connect', 'token_earn', 'token_stake', 'nft_mint',
      'dao_vote', 'marketplace_purchase', 'content_create'
    ];

    web3Events.forEach(eventType => {
      document.addEventListener(eventType, (event) => {
        this.logEvent('web3_activity', {
          type: eventType,
          amount: event.detail?.amount,
          tokenType: event.detail?.tokenType,
          transactionHash: event.detail?.transactionHash,
          gasUsed: event.detail?.gasUsed,
          timestamp: Date.now()
        });
      });
    });
  }

  /**
   * Track performance metrics
   */
  trackPerformanceMetrics() {
    // Page load times
    window.addEventListener('load', () => {
      const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
      this.logEvent('performance_metric', {
        type: 'page_load',
        loadTime,
        timestamp: Date.now()
      });
    });

    // API response times
    this.interceptAPIRequests();
  }

  /**
   * Intercept and track API requests
   */
  interceptAPIRequests() {
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      const startTime = performance.now();
      const url = args[0];
      
      try {
        const response = await originalFetch(...args);
        const endTime = performance.now();
        
        this.logEvent('api_request', {
          url,
          method: args[1]?.method || 'GET',
          status: response.status,
          responseTime: endTime - startTime,
          timestamp: Date.now()
        });
        
        return response;
      } catch (error) {
        const endTime = performance.now();
        
        this.logEvent('api_error', {
          url,
          method: args[1]?.method || 'GET',
          error: error.message,
          responseTime: endTime - startTime,
          timestamp: Date.now()
        });
        
        throw error;
      }
    };
  }

  /**
   * Log event to analytics system
   */
  logEvent(eventType, data) {
    const event = {
      id: this.generateEventId(),
      type: eventType,
      data,
      sessionId: this.getCurrentSessionId(),
      userId: this.getCurrentUserId(),
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    this.events.push(event);
    this.realTimeMetrics.totalEvents++;
    
    // Send to analytics API
    this.sendToAnalyticsAPI(event);
    
    // Update real-time metrics
    this.updateRealTimeMetrics(event);
    
    console.log(`📊 Event logged: ${eventType}`, data);
  }

  /**
   * Send event to analytics API
   */
  async sendToAnalyticsAPI(event) {
    try {
      await fetch('/api/analytics/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(event)
      });
    } catch (error) {
      console.error('Failed to send analytics event:', error);
      // Store locally for retry
      this.storeEventLocally(event);
    }
  }

  /**
   * Store event locally for offline/retry scenarios
   */
  storeEventLocally(event) {
    const storedEvents = JSON.parse(localStorage.getItem('analytics_events') || '[]');
    storedEvents.push(event);
    localStorage.setItem('analytics_events', JSON.stringify(storedEvents));
  }

  /**
   * Update real-time metrics
   */
  updateRealTimeMetrics(event) {
    // Update active users count
    this.updateActiveUsers();
    
    // Update session count
    this.updateSessionCount();
    
    // Broadcast real-time updates
    this.broadcastRealTimeUpdate(event);
  }

  /**
   * Update active users count
   */
  updateActiveUsers() {
    const now = Date.now();
    const activeThreshold = 5 * 60 * 1000; // 5 minutes
    
    // Clean up old sessions
    for (const [sessionId, lastActivity] of this.userSessions.entries()) {
      if (now - lastActivity > activeThreshold) {
        this.userSessions.delete(sessionId);
      }
    }
    
    this.realTimeMetrics.activeUsers = this.userSessions.size;
    this.realTimeMetrics.currentSessions = this.userSessions.size;
  }

  /**
   * Broadcast real-time updates to dashboard
   */
  broadcastRealTimeUpdate(event) {
    // Send to WebSocket if available
    if (window.analyticsWebSocket) {
      window.analyticsWebSocket.send(JSON.stringify({
        type: 'real_time_update',
        metrics: this.realTimeMetrics,
        event
      }));
    }
    
    // Dispatch custom event for dashboard updates
    window.dispatchEvent(new CustomEvent('analytics_update', {
      detail: {
        metrics: this.realTimeMetrics,
        event
      }
    }));
  }

  /**
   * Get comprehensive analytics data
   */
  getAnalyticsData() {
    return {
      events: this.events,
      realTimeMetrics: this.realTimeMetrics,
      userSessions: Array.from(this.userSessions.entries()),
      summary: this.generateAnalyticsSummary()
    };
  }

  /**
   * Generate analytics summary
   */
  generateAnalyticsSummary() {
    const now = Date.now();
    const last24Hours = now - (24 * 60 * 60 * 1000);
    const recentEvents = this.events.filter(event => event.timestamp > last24Hours);
    
    return {
      totalEvents: this.events.length,
      recentEvents: recentEvents.length,
      activeUsers: this.realTimeMetrics.activeUsers,
      topDashboards: this.getTopDashboards(),
      learningActivity: this.getLearningActivity(),
      securityDomainDistribution: this.realTimeMetrics.securityDomains,
      web3Activity: this.getWeb3Activity(),
      performanceMetrics: this.getPerformanceMetrics()
    };
  }

  /**
   * Helper methods
   */
  generateEventId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  getCurrentSessionId() {
    if (!sessionStorage.getItem('analytics_session_id')) {
      sessionStorage.setItem('analytics_session_id', this.generateEventId());
    }
    return sessionStorage.getItem('analytics_session_id');
  }

  getCurrentUserId() {
    // Get from auth context or localStorage
    return localStorage.getItem('user_id') || 'anonymous';
  }

  getTopDashboards() {
    return Object.entries(this.realTimeMetrics.platformUsage)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);
  }

  getLearningActivity() {
    const learningEvents = this.events.filter(event => 
      event.type === 'learning_progress' || event.type === 'lab_activity'
    );
    return learningEvents.length;
  }

  getWeb3Activity() {
    const web3Events = this.events.filter(event => event.type === 'web3_activity');
    return web3Events.length;
  }

  getPerformanceMetrics() {
    const performanceEvents = this.events.filter(event => event.type === 'performance_metric');
    const avgLoadTime = performanceEvents.reduce((sum, event) => 
      sum + (event.data.loadTime || 0), 0) / performanceEvents.length;
    
    return {
      averageLoadTime: avgLoadTime || 0,
      totalRequests: performanceEvents.length
    };
  }
}

// Initialize global analytics middleware
window.analyticsMiddleware = new AnalyticsMiddleware();

export default AnalyticsMiddleware;
