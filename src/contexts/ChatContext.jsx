import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { supabase, safeQuery } from '../lib/supabase';
// Import the cybersecurity response system
import { generateCybersecurityResponse } from '../lib/cybersecurityLLM';
// Import the cybersecurity responses for fallback
import { cybersecurityResponses } from '../lib/cybersecurityLLM';

const ChatContext = createContext();

// Try to load messages from session storage
const loadSessionMessages = () => {
  try {
    const savedMessages = sessionStorage.getItem('xcerberus_chat_messages');
    return savedMessages ? JSON.parse(savedMessages).map(msg => ({
      ...msg,
      timestamp: new Date(msg.timestamp)
    })) : [];
  } catch (error) {
    console.error('Error loading messages from session storage:', error);
    return [];
  }
};

const initialState = {
  isOpen: false,
  isMinimized: false,
  isExpanded: false,
  messages: loadSessionMessages(),
  input: '',
  isTyping: false,
  showSuggestions: true,
  error: null,
  loading: false,
  connectionStatus: 'connecting', // 'connecting', 'connected', 'error'
  conversationHistory: [],
  userContext: null,
  activeConversationId: null // Track the current conversation ID
};

function chatReducer(state, action) {
  switch (action.type) {
    case 'SET_CONNECTION_STATUS':
      return {
        ...state,
        connectionStatus: action.payload
      };
    case 'TOGGLE_CHAT':
      return {
        ...state,
        isOpen: !state.isOpen,
        isMinimized: false
      };
    case 'MINIMIZE_CHAT':
      return {
        ...state,
        isMinimized: true,
        isExpanded: false
      };
    case 'MAXIMIZE_CHAT':
      return {
        ...state,
        isMinimized: false
      };
    case 'TOGGLE_EXPAND':
      return {
        ...state,
        isExpanded: !state.isExpanded
      };
    case 'ADD_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload],
        error: null
      };
    case 'SET_MESSAGES':
      return {
        ...state,
        messages: action.payload
      };
    case 'SET_INPUT':
      return {
        ...state,
        input: action.payload
      };
    case 'SET_TYPING':
      return {
        ...state,
        isTyping: action.payload
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isTyping: false
      };
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload
      };
    case 'SET_SHOW_SUGGESTIONS':
      return {
        ...state,
        showSuggestions: action.payload
      };
    case 'CLEAR_MESSAGES':
      return {
        ...state,
        messages: [],
        error: null
      };
    case 'SET_CONVERSATION_HISTORY':
      return {
        ...state,
        conversationHistory: action.payload
      };
    case 'SET_USER_CONTEXT':
      return {
        ...state,
        userContext: action.payload
      };
    case 'SET_ACTIVE_CONVERSATION':
      return {
        ...state,
        activeConversationId: action.payload
      };
    case 'CLEAR_ACTIVE_CONVERSATION':
      return {
        ...state,
        activeConversationId: null
      };

    default:
      return state;
  }
}

export function ChatProvider({ children }) {
  const [state, dispatch] = useReducer(chatReducer, initialState);

  // Save messages to session storage whenever they change
  useEffect(() => {
    try {
      sessionStorage.setItem('xcerberus_chat_messages', JSON.stringify(state.messages));
    } catch (error) {
      console.error('Error saving messages to session storage:', error);
    }
  }, [state.messages]);

  // Check Supabase connection on mount and load user context
  React.useEffect(() => {
    const checkConnection = async () => {
      try {
        const { data, error } = await supabase.from('ai_responses').select('count').single();
        if (!error) {
          dispatch({ type: 'SET_CONNECTION_STATUS', payload: 'connected' });

          // Load user context if logged in
          const { data: { session } } = await supabase.auth.getSession();
          if (session?.user?.id) {
            loadUserContext(session.user.id);
            loadConversationHistory(session.user.id);
          }
        } else {
          throw error;
        }
      } catch (error) {
        console.error('Supabase connection error:', error);
        dispatch({ type: 'SET_CONNECTION_STATUS', payload: 'error' });
      }
    };

    checkConnection();
  }, []);

  // Load user context from database
  const loadUserContext = async (userId) => {
    try {
      // Get user profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError) throw profileError;

      // Get user's learning progress
      const { data: learningProgress, error: learningError } = await supabase
        .from('user_learning_progress')
        .select('*')
        .eq('user_id', userId)
        .order('last_accessed', { ascending: false })
        .limit(5);

      if (learningError) throw learningError;

      // Get user's challenge progress
      const { data: challengeProgress, error: challengeError } = await supabase
        .from('user_challenge_progress')
        .select('*')
        .eq('user_id', userId)
        .order('last_attempted', { ascending: false })
        .limit(5);

      if (challengeError) throw challengeError;

      const userContext = {
        profile: profile || {},
        learningProgress: learningProgress || [],
        challengeProgress: challengeProgress || []
      };

      dispatch({ type: 'SET_USER_CONTEXT', payload: userContext });
    } catch (error) {
      console.error('Error loading user context:', error);
    }
  };

  // Load conversation history from database
  const loadConversationHistory = async (userId) => {
    try {
      // First, get all conversations for this user
      const { data: conversationsData, error: conversationsError } = await supabase
        .from('conversations')
        .select('*')
        .eq('user_id', userId)
        .order('updated_at', { ascending: false })
        .limit(10);

      if (conversationsError) throw conversationsError;

      if (!conversationsData || conversationsData.length === 0) {
        dispatch({ type: 'SET_CONVERSATION_HISTORY', payload: [] });
        return;
      }

      // For each conversation, get the messages
      const conversationsWithMessages = await Promise.all(conversationsData.map(async (conversation) => {
        const { data: messagesData, error: messagesError } = await supabase
          .from('chat_messages')
          .select('*')
          .eq('conversation_id', conversation.id)
          .order('created_at', { ascending: true });

        if (messagesError) throw messagesError;

        // Format messages for the UI
        const formattedMessages = messagesData.map(message => ({
          type: message.type,
          content: message.content,
          category: message.category,
          timestamp: new Date(message.created_at),
          conversationId: conversation.id
        }));

        return {
          id: conversation.id,
          title: conversation.title,
          createdAt: new Date(conversation.created_at),
          updatedAt: new Date(conversation.updated_at),
          messages: formattedMessages
        };
      }));

      console.log('Loaded conversations:', conversationsWithMessages);
      dispatch({ type: 'SET_CONVERSATION_HISTORY', payload: conversationsWithMessages });
    } catch (error) {
      console.error('Error loading conversation history:', error);
    }
  };

  const handleSubmit = useCallback(async (input) => {
    if (!input.trim() || state.isTyping) return;

    console.log('Handling submit with input:', input);

    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_TYPING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      // Get current user
      const { data: { session } } = await supabase.auth.getSession();
      const userId = session?.user?.id;

      // Add user message
      const userMessage = {
        type: 'user',
        content: input,
        timestamp: new Date()
      };

      dispatch({ type: 'ADD_MESSAGE', payload: userMessage });
      dispatch({ type: 'SET_INPUT', payload: '' });
      dispatch({ type: 'SET_SHOW_SUGGESTIONS', payload: false });

      // Add thinking message
      const thinkingMessage = {
        type: 'ai',
        content: '🤔 Analyzing your question...',
        category: 'thinking',
        timestamp: new Date(),
        thinking: true
      };
      dispatch({ type: 'ADD_MESSAGE', payload: thinkingMessage });

      // Get AI response with retry mechanism and fallback
      let response;

      // We don't need mock responses anymore since we have a local knowledge base
      // and a fallback mechanism to Gemini API

      // Use our mock response system for reliable answers
      try {
        // Only log in development mode
        if (import.meta.env.DEV) {
          console.log('Using mock response system');
        }

        // Get response from our mock system
        response = await generateCybersecurityResponse(input, 'english', userId);

        // Ensure we have a valid response
        if (!response || !response.content) {
          throw new Error('Invalid response from mock system');
        }

        // Save the question and response to the database for future reference
        if (userId) {
          try {
            await supabase.from('ai_responses').insert({
              keyword: input.toLowerCase(),
              content: response.content,
              category: response.category,
              domain: 'CYBERSECURITY',
              language: 'english',
              source: response.source || 'local',
              user_id: userId,
              created_at: new Date().toISOString()
            });
          } catch (dbError) {
            console.error('Error saving response to database:', dbError);
            // Continue even if saving fails
          }
        }
      } catch (error) {
        console.error('Failed to get response:', error);
        // Use a default response even in case of error
        response = {
          content: cybersecurityResponses.default,
          category: "general",
          language: "english",
          cached: false,
          source: 'fallback'
        };
      }

      // Remove thinking message
      const currentMessages = [...state.messages].filter(m => !m.thinking);

      // Only log in development mode
      if (import.meta.env.DEV) {
        console.log('Current messages after removing thinking:', currentMessages.length);
      }

      dispatch({
        type: 'SET_MESSAGES',
        payload: currentMessages
      });

      // Add AI response with fallback for undefined response
      const aiMessage = {
        type: 'ai',
        content: response?.content || "I apologize, but I couldn't generate a response. Please try again.",
        category: response?.category || "error",
        language: response?.language || "english",
        timestamp: new Date(),
        cached: response?.cached || false
      };

      // Only log in development mode
      if (import.meta.env.DEV) {
        console.log('Adding AI message:', aiMessage);
      }

      dispatch({ type: 'ADD_MESSAGE', payload: aiMessage });
      dispatch({ type: 'SET_TYPING', payload: false });
      dispatch({ type: 'SET_LOADING', payload: false });

      // Save message to database if user is logged in
      if (userId) {
        try {
          // Generate a unique conversation ID if this is a new conversation
          let conversationId;

          // Check if we have an active conversation
          if (state.activeConversationId) {
            conversationId = state.activeConversationId;
          } else {
            // Create a new conversation
            const { data: newConversation, error: convError } = await supabase
              .from('conversations')
              .insert({
                user_id: userId,
                title: input.substring(0, 50) + (input.length > 50 ? '...' : ''),
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
              .select('id')
              .single();

            if (convError) throw convError;
            conversationId = newConversation.id;

            // Update state with the new conversation ID
            dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: conversationId });
          }

          // Save both messages with the conversation ID
          await supabase.from('chat_messages').insert([
            {
              user_id: userId,
              conversation_id: conversationId,
              type: 'user',
              content: input,
              created_at: new Date().toISOString()
            },
            {
              user_id: userId,
              conversation_id: conversationId,
              type: 'ai',
              content: response.content,
              category: response.category,
              created_at: new Date().toISOString()
            }
          ]);

          // Update conversation's updated_at timestamp
          await supabase
            .from('conversations')
            .update({ updated_at: new Date().toISOString() })
            .eq('id', conversationId);

          // Reload conversation history to include the new messages
          if (userId) {
            loadConversationHistory(userId);
          }
        } catch (error) {
          console.error('Error saving chat messages:', error);
          // Non-critical error, don't show to user
        }
      }

    } catch (error) {
      console.error('Error in chat:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
      dispatch({ type: 'SET_TYPING', payload: false });
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [state.isTyping, state.messages]);

  const value = {
    state,
    dispatch,
    toggleChat: () => dispatch({ type: 'TOGGLE_CHAT' }),
    minimizeChat: () => dispatch({ type: 'MINIMIZE_CHAT' }),
    maximizeChat: () => dispatch({ type: 'MAXIMIZE_CHAT' }),
    toggleExpand: () => dispatch({ type: 'TOGGLE_EXPAND' }),
    addMessage: (message) => dispatch({ type: 'ADD_MESSAGE', payload: message }),
    setMessages: (messages) => dispatch({ type: 'SET_MESSAGES', payload: messages }),
    setInput: (input) => dispatch({ type: 'SET_INPUT', payload: input }),
    setTyping: (isTyping) => dispatch({ type: 'SET_TYPING', payload: isTyping }),
    setError: (error) => dispatch({ type: 'SET_ERROR', payload: error }),
    setLoading: (loading) => dispatch({ type: 'SET_LOADING', payload: loading }),
    setShowSuggestions: (show) => dispatch({ type: 'SET_SHOW_SUGGESTIONS', payload: show }),
    clearMessages: () => dispatch({ type: 'CLEAR_MESSAGES' }),
    handleSubmit
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
}

export function useChat() {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
}