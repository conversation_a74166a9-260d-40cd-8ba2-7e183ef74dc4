import React, { createContext, useContext, useState, useEffect } from 'react';
import { ethers } from 'ethers';

/**
 * Web3 Context for XCerberus Platform
 * 
 * Manages wallet connections, smart contract interactions,
 * and blockchain state for the decentralized platform.
 */

// Contract addresses (replace with deployed addresses)
const CONTRACT_ADDRESSES = {
  XCYBER_TOKEN: import.meta.env.VITE_XCYBER_TOKEN_ADDRESS || '******************************************',
  CERTIFICATES: import.meta.env.VITE_CERTIFICATES_ADDRESS || '******************************************',
  MARKETPLACE: import.meta.env.VITE_MARKETPLACE_ADDRESS || '******************************************',
  DAO: import.meta.env.VITE_DAO_ADDRESS || '******************************************'
};

// Contract ABIs (simplified for demo)
const XCYBER_TOKEN_ABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function transfer(address to, uint256 amount) returns (bool)",
  "function approve(address spender, uint256 amount) returns (bool)",
  "function allowance(address owner, address spender) view returns (uint256)",
  "function mintReward(address to, uint256 amount, string reason)",
  "function stakeTokens(uint256 amount)",
  "function calculateStakingReward(address user) view returns (uint256)",
  "function claimStakingRewards()",
  "event TokensEarned(address indexed user, uint256 amount, string reason)"
];

const CERTIFICATES_ABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function tokenOfOwnerByIndex(address owner, uint256 index) view returns (uint256)",
  "function certificates(uint256 tokenId) view returns (tuple(string certificationType, string title, string description, string skillsProven, uint256 issueDate, uint256 expiryDate, string metadataURI, address issuer, bool isVerified, uint256 skillLevel))",
  "function getUserCertificates(address user) view returns (uint256[])",
  "function verifyCertificate(uint256 tokenId) view returns (bool)",
  "function issueCertificate(address recipient, string certificationType, string title, string description, string skillsProven, uint256 expiryDate, string metadataURI, uint256 skillLevel) returns (uint256)",
  "event CertificateIssued(address indexed recipient, uint256 indexed tokenId, string certificationType, string title)"
];

const MARKETPLACE_ABI = [
  "function contents(uint256 id) view returns (tuple(uint256 id, address creator, string title, string description, string contentType, uint256 price, string metadataURI, bool isActive, uint256 totalSales, uint256 rating, uint256 reviewCount))",
  "function listContent(string title, string description, string contentType, uint256 price, string metadataURI) returns (uint256)",
  "function purchaseContent(uint256 contentId)",
  "function hasContentAccess(address user, uint256 contentId) view returns (bool)",
  "function getCreatorContents(address creator) view returns (uint256[])",
  "event ContentListed(uint256 indexed contentId, address indexed creator, string title, uint256 price)",
  "event ContentPurchased(uint256 indexed contentId, address indexed buyer, address indexed creator, uint256 price)"
];

const DAO_ABI = [
  "function proposals(uint256 id) view returns (tuple(uint256 id, address proposer, string title, string description, uint256 votingDeadline, uint256 forVotes, uint256 againstVotes, bool executed))",
  "function createProposal(string title, string description) returns (uint256)",
  "function vote(uint256 proposalId, bool support)",
  "function calculateVotingPower(address user) view returns (uint256)",
  "function executeProposal(uint256 proposalId)",
  "event ProposalCreated(uint256 indexed proposalId, address indexed proposer, string title)",
  "event VoteCast(uint256 indexed proposalId, address indexed voter, bool support, uint256 weight)"
];

const Web3Context = createContext();

export const useWeb3 = () => {
  const context = useContext(Web3Context);
  if (!context) {
    throw new Error('useWeb3 must be used within a Web3Provider');
  }
  return context;
};

export const Web3Provider = ({ children }) => {
  const [account, setAccount] = useState(null);
  const [provider, setProvider] = useState(null);
  const [signer, setSigner] = useState(null);
  const [contracts, setContracts] = useState({});
  const [chainId, setChainId] = useState(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [web3Modal, setWeb3Modal] = useState(null);
  const [tokenBalance, setTokenBalance] = useState('0');
  const [certificateCount, setCertificateCount] = useState(0);
  const [stakingRewards, setStakingRewards] = useState('0');

  // Initialize Web3 connection
  useEffect(() => {
    // Check if MetaMask is available
    if (typeof window.ethereum !== 'undefined') {
      console.log('MetaMask is available');
    }
  }, []);

  // Connect wallet
  const connectWallet = async () => {
    if (typeof window.ethereum === 'undefined') {
      alert('Please install MetaMask to use Web3 features');
      return;
    }

    setIsConnecting(true);
    try {
      // Request account access
      await window.ethereum.request({ method: 'eth_requestAccounts' });

      const provider = new ethers.providers.Web3Provider(window.ethereum);
      const signer = provider.getSigner();
      const account = await signer.getAddress();
      const network = await provider.getNetwork();

      setProvider(provider);
      setSigner(signer);
      setAccount(account);
      setChainId(network.chainId);

      // Initialize contracts
      await initializeContracts(signer);

      // Set up event listeners
      window.ethereum.on('accountsChanged', handleAccountsChanged);
      window.ethereum.on('chainChanged', handleChainChanged);
      window.ethereum.on('disconnect', handleDisconnect);

      // Save connection state
      localStorage.setItem('walletConnected', 'true');

    } catch (error) {
      console.error('Error connecting wallet:', error);
      alert('Failed to connect wallet: ' + error.message);
    } finally {
      setIsConnecting(false);
    }
  };

  // Initialize smart contracts (mock implementation for demo)
  const initializeContracts = async (signer) => {
    try {
      // For demo purposes, we'll use mock contracts
      // In production, replace with actual deployed contract addresses
      const mockContracts = {
        xcyberToken: {
          address: '******************************************',
          balanceOf: async () => ethers.utils.parseEther('1000'),
          transfer: async () => ({ hash: '0xmockhash' }),
          approve: async () => ({ hash: '0xmockhash' })
        },
        certificates: {
          address: '******************************************',
          balanceOf: async () => ethers.BigNumber.from('5'),
          getUserCertificates: async () => []
        },
        marketplace: {
          address: '******************************************'
        },
        dao: {
          address: '******************************************',
          calculateVotingPower: async () => ethers.utils.parseEther('1500')
        }
      };

      setContracts(mockContracts);

      // Load user data with mock contracts
      await loadUserData(await signer.getAddress(), mockContracts);

    } catch (error) {
      console.error('Error initializing contracts:', error);
    }
  };

  // Load user blockchain data (mock implementation)
  const loadUserData = async (userAddress, contractInstances) => {
    try {
      const { xcyberToken, certificates, dao } = contractInstances;

      // Get token balance (mock)
      const balance = await xcyberToken.balanceOf(userAddress);
      setTokenBalance(ethers.utils.formatEther(balance));

      // Get certificate count (mock)
      const certCount = await certificates.balanceOf(userAddress);
      setCertificateCount(certCount.toNumber());

      // Set mock staking rewards
      setStakingRewards('25.5');

    } catch (error) {
      console.error('Error loading user data:', error);
      // Set default values if loading fails
      setTokenBalance('1000');
      setCertificateCount(5);
      setStakingRewards('25.5');
    }
  };

  // Disconnect wallet
  const disconnectWallet = async () => {
    setAccount(null);
    setProvider(null);
    setSigner(null);
    setContracts({});
    setChainId(null);
    setTokenBalance('0');
    setCertificateCount(0);
    setStakingRewards('0');

    // Clear connection state
    localStorage.removeItem('walletConnected');
  };

  // Event handlers
  const handleAccountsChanged = (accounts) => {
    if (accounts.length === 0) {
      disconnectWallet();
    } else {
      setAccount(accounts[0]);
      if (contracts.xcyberToken) {
        loadUserData(accounts[0], contracts);
      }
    }
  };

  const handleChainChanged = (chainId) => {
    setChainId(parseInt(chainId, 16));
    window.location.reload(); // Reload to reinitialize contracts
  };

  const handleDisconnect = () => {
    disconnectWallet();
  };

  // Token operations (mock implementations)
  const earnTokens = async (amount, reason) => {
    try {
      // Mock earning tokens
      const currentBalance = parseFloat(tokenBalance);
      const newBalance = currentBalance + amount;
      setTokenBalance(newBalance.toString());

      console.log(`Earned ${amount} XCYBER tokens for: ${reason}`);

      return {
        success: true,
        txHash: '0x' + Math.random().toString(16).substr(2, 64)
      };
    } catch (error) {
      console.error('Error earning tokens:', error);
      return { success: false, error: error.message };
    }
  };

  const stakeTokens = async (amount) => {
    try {
      // Mock staking tokens
      const currentBalance = parseFloat(tokenBalance);
      const stakeAmount = parseFloat(amount);

      if (stakeAmount > currentBalance) {
        throw new Error('Insufficient balance');
      }

      const newBalance = currentBalance - stakeAmount;
      setTokenBalance(newBalance.toString());

      // Increase staking rewards
      const currentRewards = parseFloat(stakingRewards);
      setStakingRewards((currentRewards + stakeAmount * 0.1).toString());

      console.log(`Staked ${amount} XCYBER tokens`);

      return {
        success: true,
        txHash: '0x' + Math.random().toString(16).substr(2, 64)
      };
    } catch (error) {
      console.error('Error staking tokens:', error);
      return { success: false, error: error.message };
    }
  };

  const claimStakingRewards = async () => {
    try {
      // Mock claiming rewards
      const currentBalance = parseFloat(tokenBalance);
      const rewards = parseFloat(stakingRewards);

      if (rewards <= 0) {
        throw new Error('No rewards to claim');
      }

      const newBalance = currentBalance + rewards;
      setTokenBalance(newBalance.toString());
      setStakingRewards('0');

      console.log(`Claimed ${rewards} XCYBER tokens in staking rewards`);

      return {
        success: true,
        txHash: '0x' + Math.random().toString(16).substr(2, 64)
      };
    } catch (error) {
      console.error('Error claiming rewards:', error);
      return { success: false, error: error.message };
    }
  };

  // Certificate operations (mock implementations)
  const issueCertificate = async (certificateData) => {
    try {
      // Mock certificate issuance
      const tokenId = Math.floor(Math.random() * 10000);

      // Increase certificate count
      setCertificateCount(prev => prev + 1);

      // Award tokens for earning certificate
      const rewardAmount = certificateData.skillLevel || 50;
      await earnTokens(rewardAmount, 'Certificate earned');

      console.log(`Issued certificate: ${certificateData.title}`);

      return {
        success: true,
        txHash: '0x' + Math.random().toString(16).substr(2, 64),
        tokenId: tokenId.toString()
      };
    } catch (error) {
      console.error('Error issuing certificate:', error);
      return { success: false, error: error.message };
    }
  };

  const getUserCertificates = async (userAddress = account) => {
    try {
      // Mock certificates data
      const mockCertificates = [
        {
          tokenId: '1',
          certificationType: 'course',
          title: 'Web Security Fundamentals',
          description: 'Completed comprehensive web security course',
          skillsProven: 'XSS, SQL Injection, CSRF Prevention',
          issueDate: Math.floor(Date.now() / 1000) - 86400 * 30, // 30 days ago
          expiryDate: 0, // Never expires
          skillLevel: 85,
          isVerified: true
        },
        {
          tokenId: '2',
          certificationType: 'certification',
          title: 'Ethical Hacker Certified',
          description: 'Industry-recognized ethical hacking certification',
          skillsProven: 'Penetration Testing, Vulnerability Assessment',
          issueDate: Math.floor(Date.now() / 1000) - 86400 * 60, // 60 days ago
          expiryDate: Math.floor(Date.now() / 1000) + 86400 * 365, // 1 year from now
          skillLevel: 92,
          isVerified: true
        }
      ];

      return { success: true, certificates: mockCertificates };
    } catch (error) {
      console.error('Error getting certificates:', error);
      return { success: false, error: error.message };
    }
  };

  // Marketplace operations (mock implementations)
  const listContent = async (contentData) => {
    try {
      // Mock content listing
      const contentId = Math.floor(Math.random() * 10000);

      console.log(`Listed content: ${contentData.title} for ${contentData.price} XCYBER`);

      return {
        success: true,
        txHash: '0x' + Math.random().toString(16).substr(2, 64),
        contentId: contentId.toString()
      };
    } catch (error) {
      console.error('Error listing content:', error);
      return { success: false, error: error.message };
    }
  };

  const purchaseContent = async (contentId) => {
    try {
      // Mock content purchase
      const price = 50; // Mock price in XCYBER
      const currentBalance = parseFloat(tokenBalance);

      if (currentBalance < price) {
        throw new Error('Insufficient XCYBER balance');
      }

      const newBalance = currentBalance - price;
      setTokenBalance(newBalance.toString());

      console.log(`Purchased content ${contentId} for ${price} XCYBER`);

      return {
        success: true,
        txHash: '0x' + Math.random().toString(16).substr(2, 64)
      };
    } catch (error) {
      console.error('Error purchasing content:', error);
      return { success: false, error: error.message };
    }
  };

  // DAO operations (mock implementations)
  const createProposal = async (title, description) => {
    try {
      // Mock proposal creation
      const proposalId = Math.floor(Math.random() * 1000);

      console.log(`Created DAO proposal: ${title}`);

      return {
        success: true,
        txHash: '0x' + Math.random().toString(16).substr(2, 64),
        proposalId: proposalId.toString()
      };
    } catch (error) {
      console.error('Error creating proposal:', error);
      return { success: false, error: error.message };
    }
  };

  const voteOnProposal = async (proposalId, support) => {
    try {
      // Mock voting
      console.log(`Voted ${support ? 'FOR' : 'AGAINST'} proposal ${proposalId}`);

      return {
        success: true,
        txHash: '0x' + Math.random().toString(16).substr(2, 64)
      };
    } catch (error) {
      console.error('Error voting on proposal:', error);
      return { success: false, error: error.message };
    }
  };

  const getVotingPower = async (userAddress = account) => {
    try {
      // Mock voting power calculation
      const tokenBalance = parseFloat(tokenBalance) || 1000;
      const certificateBonus = certificateCount * 100;
      const votingPower = tokenBalance + certificateBonus;

      return {
        success: true,
        votingPower: votingPower.toString()
      };
    } catch (error) {
      console.error('Error getting voting power:', error);
      return { success: false, error: error.message };
    }
  };

  // Auto-connect on page load if previously connected
  useEffect(() => {
    const wasConnected = localStorage.getItem('walletConnected');
    if (wasConnected === 'true' && typeof window.ethereum !== 'undefined') {
      connectWallet();
    }
  }, []);

  const value = {
    // Connection state
    account,
    provider,
    signer,
    chainId,
    isConnecting,
    isConnected: !!account,
    
    // User data
    tokenBalance,
    certificateCount,
    stakingRewards,
    
    // Connection methods
    connectWallet,
    disconnectWallet,
    
    // Token operations
    earnTokens,
    stakeTokens,
    claimStakingRewards,
    
    // Certificate operations
    issueCertificate,
    getUserCertificates,
    
    // Marketplace operations
    listContent,
    purchaseContent,
    
    // DAO operations
    createProposal,
    voteOnProposal,
    getVotingPower,
    
    // Contracts
    contracts
  };

  return (
    <Web3Context.Provider value={value}>
      {children}
    </Web3Context.Provider>
  );
};
