import React, { createContext, useContext, useState, useEffect } from 'react';
import { ethers } from 'ethers';
import Web3Modal from 'web3modal';
import WalletConnectProvider from '@walletconnect/web3-provider';

/**
 * Web3 Context for XCerberus Platform
 * 
 * Manages wallet connections, smart contract interactions,
 * and blockchain state for the decentralized platform.
 */

// Contract addresses (replace with deployed addresses)
const CONTRACT_ADDRESSES = {
  XCYBER_TOKEN: process.env.REACT_APP_XCYBER_TOKEN_ADDRESS || '0x...',
  CERTIFICATES: process.env.REACT_APP_CERTIFICATES_ADDRESS || '0x...',
  MARKETPLACE: process.env.REACT_APP_MARKETPLACE_ADDRESS || '0x...',
  DAO: process.env.REACT_APP_DAO_ADDRESS || '0x...'
};

// Contract ABIs (simplified for demo)
const XCYBER_TOKEN_ABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function transfer(address to, uint256 amount) returns (bool)",
  "function approve(address spender, uint256 amount) returns (bool)",
  "function allowance(address owner, address spender) view returns (uint256)",
  "function mintReward(address to, uint256 amount, string reason)",
  "function stakeTokens(uint256 amount)",
  "function calculateStakingReward(address user) view returns (uint256)",
  "function claimStakingRewards()",
  "event TokensEarned(address indexed user, uint256 amount, string reason)"
];

const CERTIFICATES_ABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function tokenOfOwnerByIndex(address owner, uint256 index) view returns (uint256)",
  "function certificates(uint256 tokenId) view returns (tuple(string certificationType, string title, string description, string skillsProven, uint256 issueDate, uint256 expiryDate, string metadataURI, address issuer, bool isVerified, uint256 skillLevel))",
  "function getUserCertificates(address user) view returns (uint256[])",
  "function verifyCertificate(uint256 tokenId) view returns (bool)",
  "function issueCertificate(address recipient, string certificationType, string title, string description, string skillsProven, uint256 expiryDate, string metadataURI, uint256 skillLevel) returns (uint256)",
  "event CertificateIssued(address indexed recipient, uint256 indexed tokenId, string certificationType, string title)"
];

const MARKETPLACE_ABI = [
  "function contents(uint256 id) view returns (tuple(uint256 id, address creator, string title, string description, string contentType, uint256 price, string metadataURI, bool isActive, uint256 totalSales, uint256 rating, uint256 reviewCount))",
  "function listContent(string title, string description, string contentType, uint256 price, string metadataURI) returns (uint256)",
  "function purchaseContent(uint256 contentId)",
  "function hasContentAccess(address user, uint256 contentId) view returns (bool)",
  "function getCreatorContents(address creator) view returns (uint256[])",
  "event ContentListed(uint256 indexed contentId, address indexed creator, string title, uint256 price)",
  "event ContentPurchased(uint256 indexed contentId, address indexed buyer, address indexed creator, uint256 price)"
];

const DAO_ABI = [
  "function proposals(uint256 id) view returns (tuple(uint256 id, address proposer, string title, string description, uint256 votingDeadline, uint256 forVotes, uint256 againstVotes, bool executed))",
  "function createProposal(string title, string description) returns (uint256)",
  "function vote(uint256 proposalId, bool support)",
  "function calculateVotingPower(address user) view returns (uint256)",
  "function executeProposal(uint256 proposalId)",
  "event ProposalCreated(uint256 indexed proposalId, address indexed proposer, string title)",
  "event VoteCast(uint256 indexed proposalId, address indexed voter, bool support, uint256 weight)"
];

const Web3Context = createContext();

export const useWeb3 = () => {
  const context = useContext(Web3Context);
  if (!context) {
    throw new Error('useWeb3 must be used within a Web3Provider');
  }
  return context;
};

export const Web3Provider = ({ children }) => {
  const [account, setAccount] = useState(null);
  const [provider, setProvider] = useState(null);
  const [signer, setSigner] = useState(null);
  const [contracts, setContracts] = useState({});
  const [chainId, setChainId] = useState(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [web3Modal, setWeb3Modal] = useState(null);
  const [tokenBalance, setTokenBalance] = useState('0');
  const [certificateCount, setCertificateCount] = useState(0);
  const [stakingRewards, setStakingRewards] = useState('0');

  // Initialize Web3Modal
  useEffect(() => {
    const providerOptions = {
      walletconnect: {
        package: WalletConnectProvider,
        options: {
          infuraId: process.env.REACT_APP_INFURA_ID || 'your-infura-id'
        }
      }
    };

    const modal = new Web3Modal({
      network: 'mainnet',
      cacheProvider: true,
      providerOptions
    });

    setWeb3Modal(modal);
  }, []);

  // Connect wallet
  const connectWallet = async () => {
    if (!web3Modal) return;

    setIsConnecting(true);
    try {
      const instance = await web3Modal.connect();
      const provider = new ethers.providers.Web3Provider(instance);
      const signer = provider.getSigner();
      const account = await signer.getAddress();
      const network = await provider.getNetwork();

      setProvider(provider);
      setSigner(signer);
      setAccount(account);
      setChainId(network.chainId);

      // Initialize contracts
      await initializeContracts(signer);

      // Set up event listeners
      instance.on('accountsChanged', handleAccountsChanged);
      instance.on('chainChanged', handleChainChanged);
      instance.on('disconnect', handleDisconnect);

    } catch (error) {
      console.error('Error connecting wallet:', error);
    } finally {
      setIsConnecting(false);
    }
  };

  // Initialize smart contracts
  const initializeContracts = async (signer) => {
    try {
      const xcyberToken = new ethers.Contract(
        CONTRACT_ADDRESSES.XCYBER_TOKEN,
        XCYBER_TOKEN_ABI,
        signer
      );

      const certificates = new ethers.Contract(
        CONTRACT_ADDRESSES.CERTIFICATES,
        CERTIFICATES_ABI,
        signer
      );

      const marketplace = new ethers.Contract(
        CONTRACT_ADDRESSES.MARKETPLACE,
        MARKETPLACE_ABI,
        signer
      );

      const dao = new ethers.Contract(
        CONTRACT_ADDRESSES.DAO,
        DAO_ABI,
        signer
      );

      setContracts({
        xcyberToken,
        certificates,
        marketplace,
        dao
      });

      // Load user data
      await loadUserData(signer.getAddress(), { xcyberToken, certificates });

    } catch (error) {
      console.error('Error initializing contracts:', error);
    }
  };

  // Load user blockchain data
  const loadUserData = async (userAddress, contractInstances) => {
    try {
      const { xcyberToken, certificates } = contractInstances;

      // Get token balance
      const balance = await xcyberToken.balanceOf(userAddress);
      setTokenBalance(ethers.utils.formatEther(balance));

      // Get certificate count
      const certCount = await certificates.balanceOf(userAddress);
      setCertificateCount(certCount.toNumber());

      // Get staking rewards
      const rewards = await xcyberToken.calculateStakingReward(userAddress);
      setStakingRewards(ethers.utils.formatEther(rewards));

    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  // Disconnect wallet
  const disconnectWallet = async () => {
    if (web3Modal) {
      web3Modal.clearCachedProvider();
    }
    setAccount(null);
    setProvider(null);
    setSigner(null);
    setContracts({});
    setChainId(null);
    setTokenBalance('0');
    setCertificateCount(0);
    setStakingRewards('0');
  };

  // Event handlers
  const handleAccountsChanged = (accounts) => {
    if (accounts.length === 0) {
      disconnectWallet();
    } else {
      setAccount(accounts[0]);
      if (contracts.xcyberToken) {
        loadUserData(accounts[0], contracts);
      }
    }
  };

  const handleChainChanged = (chainId) => {
    setChainId(parseInt(chainId, 16));
    window.location.reload(); // Reload to reinitialize contracts
  };

  const handleDisconnect = () => {
    disconnectWallet();
  };

  // Token operations
  const earnTokens = async (amount, reason) => {
    try {
      if (!contracts.xcyberToken) throw new Error('Contract not initialized');
      
      const tx = await contracts.xcyberToken.mintReward(
        account,
        ethers.utils.parseEther(amount.toString()),
        reason
      );
      await tx.wait();
      
      // Refresh balance
      await loadUserData(account, contracts);
      
      return { success: true, txHash: tx.hash };
    } catch (error) {
      console.error('Error earning tokens:', error);
      return { success: false, error: error.message };
    }
  };

  const stakeTokens = async (amount) => {
    try {
      if (!contracts.xcyberToken) throw new Error('Contract not initialized');
      
      const tx = await contracts.xcyberToken.stakeTokens(
        ethers.utils.parseEther(amount.toString())
      );
      await tx.wait();
      
      // Refresh data
      await loadUserData(account, contracts);
      
      return { success: true, txHash: tx.hash };
    } catch (error) {
      console.error('Error staking tokens:', error);
      return { success: false, error: error.message };
    }
  };

  const claimStakingRewards = async () => {
    try {
      if (!contracts.xcyberToken) throw new Error('Contract not initialized');
      
      const tx = await contracts.xcyberToken.claimStakingRewards();
      await tx.wait();
      
      // Refresh data
      await loadUserData(account, contracts);
      
      return { success: true, txHash: tx.hash };
    } catch (error) {
      console.error('Error claiming rewards:', error);
      return { success: false, error: error.message };
    }
  };

  // Certificate operations
  const issueCertificate = async (certificateData) => {
    try {
      if (!contracts.certificates) throw new Error('Contract not initialized');
      
      const {
        recipient,
        certificationType,
        title,
        description,
        skillsProven,
        expiryDate,
        metadataURI,
        skillLevel
      } = certificateData;
      
      const tx = await contracts.certificates.issueCertificate(
        recipient,
        certificationType,
        title,
        description,
        skillsProven,
        expiryDate,
        metadataURI,
        skillLevel
      );
      
      const receipt = await tx.wait();
      
      // Get token ID from event
      const event = receipt.events?.find(e => e.event === 'CertificateIssued');
      const tokenId = event?.args?.tokenId;
      
      // Refresh data
      await loadUserData(account, contracts);
      
      return { success: true, txHash: tx.hash, tokenId: tokenId?.toString() };
    } catch (error) {
      console.error('Error issuing certificate:', error);
      return { success: false, error: error.message };
    }
  };

  const getUserCertificates = async (userAddress = account) => {
    try {
      if (!contracts.certificates) throw new Error('Contract not initialized');
      
      const tokenIds = await contracts.certificates.getUserCertificates(userAddress);
      const certificates = [];
      
      for (const tokenId of tokenIds) {
        const cert = await contracts.certificates.certificates(tokenId);
        certificates.push({
          tokenId: tokenId.toString(),
          ...cert
        });
      }
      
      return { success: true, certificates };
    } catch (error) {
      console.error('Error getting certificates:', error);
      return { success: false, error: error.message };
    }
  };

  // Marketplace operations
  const listContent = async (contentData) => {
    try {
      if (!contracts.marketplace) throw new Error('Contract not initialized');
      
      const { title, description, contentType, price, metadataURI } = contentData;
      
      const tx = await contracts.marketplace.listContent(
        title,
        description,
        contentType,
        ethers.utils.parseEther(price.toString()),
        metadataURI
      );
      
      const receipt = await tx.wait();
      
      // Get content ID from event
      const event = receipt.events?.find(e => e.event === 'ContentListed');
      const contentId = event?.args?.contentId;
      
      return { success: true, txHash: tx.hash, contentId: contentId?.toString() };
    } catch (error) {
      console.error('Error listing content:', error);
      return { success: false, error: error.message };
    }
  };

  const purchaseContent = async (contentId) => {
    try {
      if (!contracts.marketplace) throw new Error('Contract not initialized');
      
      // First approve tokens if needed
      const content = await contracts.marketplace.contents(contentId);
      const price = content.price;
      
      const allowance = await contracts.xcyberToken.allowance(
        account,
        CONTRACT_ADDRESSES.MARKETPLACE
      );
      
      if (allowance.lt(price)) {
        const approveTx = await contracts.xcyberToken.approve(
          CONTRACT_ADDRESSES.MARKETPLACE,
          price
        );
        await approveTx.wait();
      }
      
      // Purchase content
      const tx = await contracts.marketplace.purchaseContent(contentId);
      await tx.wait();
      
      // Refresh data
      await loadUserData(account, contracts);
      
      return { success: true, txHash: tx.hash };
    } catch (error) {
      console.error('Error purchasing content:', error);
      return { success: false, error: error.message };
    }
  };

  // DAO operations
  const createProposal = async (title, description) => {
    try {
      if (!contracts.dao) throw new Error('Contract not initialized');
      
      const tx = await contracts.dao.createProposal(title, description);
      const receipt = await tx.wait();
      
      // Get proposal ID from event
      const event = receipt.events?.find(e => e.event === 'ProposalCreated');
      const proposalId = event?.args?.proposalId;
      
      return { success: true, txHash: tx.hash, proposalId: proposalId?.toString() };
    } catch (error) {
      console.error('Error creating proposal:', error);
      return { success: false, error: error.message };
    }
  };

  const voteOnProposal = async (proposalId, support) => {
    try {
      if (!contracts.dao) throw new Error('Contract not initialized');
      
      const tx = await contracts.dao.vote(proposalId, support);
      await tx.wait();
      
      return { success: true, txHash: tx.hash };
    } catch (error) {
      console.error('Error voting on proposal:', error);
      return { success: false, error: error.message };
    }
  };

  const getVotingPower = async (userAddress = account) => {
    try {
      if (!contracts.dao) throw new Error('Contract not initialized');
      
      const power = await contracts.dao.calculateVotingPower(userAddress);
      return { success: true, votingPower: ethers.utils.formatEther(power) };
    } catch (error) {
      console.error('Error getting voting power:', error);
      return { success: false, error: error.message };
    }
  };

  // Auto-connect on page load
  useEffect(() => {
    if (web3Modal && web3Modal.cachedProvider) {
      connectWallet();
    }
  }, [web3Modal]);

  const value = {
    // Connection state
    account,
    provider,
    signer,
    chainId,
    isConnecting,
    isConnected: !!account,
    
    // User data
    tokenBalance,
    certificateCount,
    stakingRewards,
    
    // Connection methods
    connectWallet,
    disconnectWallet,
    
    // Token operations
    earnTokens,
    stakeTokens,
    claimStakingRewards,
    
    // Certificate operations
    issueCertificate,
    getUserCertificates,
    
    // Marketplace operations
    listContent,
    purchaseContent,
    
    // DAO operations
    createProposal,
    voteOnProposal,
    getVotingPower,
    
    // Contracts
    contracts
  };

  return (
    <Web3Context.Provider value={value}>
      {children}
    </Web3Context.Provider>
  );
};
