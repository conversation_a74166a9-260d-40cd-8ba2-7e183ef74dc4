# Local Supabase Configuration
# These settings point to the local Supabase instance

# Local Supabase URLs and Keys (will be generated when supabase start completes)
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

# Backend service environment variables for local development
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

# Keep existing API keys
VITE_GEMINI_API_KEY=AIzaSyCjrRX2vRmhFk1OQxHLleXMq0pGJC6xXrM
VITE_ABUSEIPDB_API_KEY=********************************************************************************
VITE_OTX_API_KEY=437e960dfa3557cd103b887b8948554f86a97f7ab562a13e546311a1cc5cebe0

# Service Ports (same as before)
AUTH_SERVICE_PORT=3001
LEARNING_SERVICE_PORT=3002
CHALLENGES_SERVICE_PORT=3003
AI_SERVICE_PORT=3004
THREATS_SERVICE_PORT=3005

# Frontend Configuration
REACT_APP_API_URL=http://localhost:3001

# Additional API Keys (from .env.example)
GOOGLE_API_KEY=your_google_api_key_here
ABUSEIPDB_API_KEY=********************************************************************************
OTX_API_KEY=437e960dfa3557cd103b887b8948554f86a97f7ab562a13e546311a1cc5cebe0

# Web3 Configuration
# Contract Addresses (Mock addresses for development)
VITE_XCYBER_TOKEN_ADDRESS=0x1234567890123456789012345678901234567890
VITE_CERTIFICATES_ADDRESS=0x2345678901234567890123456789012345678901
VITE_MARKETPLACE_ADDRESS=0x3456789012345678901234567890123456789012
VITE_DAO_ADDRESS=0x4567890123456789012345678901234567890123

# Network Configuration
VITE_CHAIN_ID=1337
VITE_NETWORK_NAME=localhost

# API Keys for Web3 (Add your actual keys here)
VITE_INFURA_ID=your-infura-project-id
VITE_ALCHEMY_KEY=your-alchemy-api-key

# Development flags
VITE_ENABLE_MOCK_WEB3=true
VITE_DEBUG_MODE=true
