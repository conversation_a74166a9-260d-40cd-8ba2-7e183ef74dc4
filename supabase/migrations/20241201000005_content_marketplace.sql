-- Content Marketplace Database Schema
-- User-generated content, monetization, and community features

-- Content creators and their profiles
CREATE TABLE IF NOT EXISTS content_creators (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Creator profile
  creator_name VARCHAR(100) NOT NULL,
  bio TEXT,
  expertise_areas TEXT[],
  website_url TEXT,
  social_links JSONB DEFAULT '{}',
  
  -- Verification and status
  is_verified BOOLEAN DEFAULT false,
  verification_level VARCHAR(20) DEFAULT 'none', -- none, basic, expert, partner
  creator_tier VARCHAR(20) DEFAULT 'bronze', -- bronze, silver, gold, platinum
  
  -- Performance metrics
  total_content INTEGER DEFAULT 0,
  total_sales INTEGER DEFAULT 0,
  total_revenue INTEGER DEFAULT 0, -- in cents
  average_rating DECIMAL(3,2) DEFAULT 0,
  total_reviews INTEGER DEFAULT 0,
  
  -- Payout information
  payout_method VARCHAR(50), -- paypal, stripe, bank_transfer
  payout_details JSONB DEFAULT '{}',
  tax_information JSONB DEFAULT '{}',
  
  -- Status
  is_active BOOLEAN DEFAULT true,
  suspended_until TIMESTAMP WITH TIME ZONE,
  suspension_reason TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- Marketplace content items
CREATE TABLE IF NOT EXISTS marketplace_content (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  creator_id UUID REFERENCES content_creators(id) ON DELETE CASCADE,
  
  -- Content details
  title VARCHAR(255) NOT NULL,
  description TEXT,
  content_type VARCHAR(50) NOT NULL, -- course, lab, assessment, tool, template
  category_id UUID REFERENCES content_categories(id),
  
  -- Content data
  content_data JSONB NOT NULL DEFAULT '{}',
  preview_content JSONB DEFAULT '{}',
  thumbnail_url TEXT,
  demo_video_url TEXT,
  
  -- Pricing and monetization
  pricing_model VARCHAR(20) NOT NULL DEFAULT 'one_time', -- one_time, subscription, free, freemium
  price INTEGER DEFAULT 0, -- in cents
  subscription_price INTEGER DEFAULT 0, -- monthly price in cents
  currency VARCHAR(3) DEFAULT 'USD',
  
  -- Licensing and usage
  license_type VARCHAR(50) DEFAULT 'standard', -- standard, commercial, educational, open_source
  usage_rights JSONB DEFAULT '{}',
  redistribution_allowed BOOLEAN DEFAULT false,
  modification_allowed BOOLEAN DEFAULT false,
  
  -- Content metadata
  difficulty_level VARCHAR(20) DEFAULT 'intermediate',
  estimated_duration INTEGER, -- in minutes
  prerequisites TEXT[],
  learning_objectives TEXT[],
  tags TEXT[],
  
  -- Quality and moderation
  moderation_status VARCHAR(20) DEFAULT 'pending', -- pending, approved, rejected, flagged
  quality_score DECIMAL(3,2) DEFAULT 0,
  moderation_notes TEXT,
  moderated_by UUID REFERENCES auth.users(id),
  moderated_at TIMESTAMP WITH TIME ZONE,
  
  -- Performance metrics
  view_count INTEGER DEFAULT 0,
  download_count INTEGER DEFAULT 0,
  purchase_count INTEGER DEFAULT 0,
  average_rating DECIMAL(3,2) DEFAULT 0,
  total_reviews INTEGER DEFAULT 0,
  
  -- Visibility and status
  is_published BOOLEAN DEFAULT false,
  is_featured BOOLEAN DEFAULT false,
  published_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content purchases and transactions
CREATE TABLE IF NOT EXISTS content_purchases (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  content_id UUID REFERENCES marketplace_content(id),
  creator_id UUID REFERENCES content_creators(id),
  
  -- Transaction details
  transaction_type VARCHAR(20) NOT NULL, -- purchase, subscription, renewal, refund
  amount INTEGER NOT NULL, -- in cents
  currency VARCHAR(3) DEFAULT 'USD',
  
  -- Payment processing
  payment_method VARCHAR(50),
  payment_processor VARCHAR(50), -- stripe, paypal, etc.
  payment_processor_id VARCHAR(255),
  payment_status VARCHAR(20) DEFAULT 'pending', -- pending, completed, failed, refunded
  
  -- Subscription details (if applicable)
  subscription_id UUID,
  subscription_period_start TIMESTAMP WITH TIME ZONE,
  subscription_period_end TIMESTAMP WITH TIME ZONE,
  
  -- Revenue sharing
  creator_revenue INTEGER, -- creator's share in cents
  platform_revenue INTEGER, -- platform's share in cents
  revenue_share_percentage DECIMAL(5,2) DEFAULT 70.00, -- creator gets 70% by default
  
  -- Access and licensing
  license_granted JSONB DEFAULT '{}',
  access_expires_at TIMESTAMP WITH TIME ZONE,
  download_limit INTEGER,
  downloads_used INTEGER DEFAULT 0,
  
  -- Status
  is_active BOOLEAN DEFAULT true,
  refunded_at TIMESTAMP WITH TIME ZONE,
  refund_reason TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content reviews and ratings
CREATE TABLE IF NOT EXISTS content_reviews (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  content_id UUID REFERENCES marketplace_content(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id),
  purchase_id UUID REFERENCES content_purchases(id),
  
  -- Review details
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  title VARCHAR(255),
  review_text TEXT,
  
  -- Review metadata
  is_verified_purchase BOOLEAN DEFAULT false,
  helpful_votes INTEGER DEFAULT 0,
  total_votes INTEGER DEFAULT 0,
  
  -- Moderation
  is_flagged BOOLEAN DEFAULT false,
  flag_reason TEXT,
  is_approved BOOLEAN DEFAULT true,
  moderated_by UUID REFERENCES auth.users(id),
  moderated_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(content_id, user_id) -- One review per user per content
);

-- Content collections and bundles
CREATE TABLE IF NOT EXISTS content_collections (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  creator_id UUID REFERENCES content_creators(id) ON DELETE CASCADE,
  
  -- Collection details
  name VARCHAR(255) NOT NULL,
  description TEXT,
  collection_type VARCHAR(20) DEFAULT 'bundle', -- bundle, series, pathway
  
  -- Pricing
  bundle_price INTEGER, -- in cents, can be less than sum of individual prices
  discount_percentage DECIMAL(5,2) DEFAULT 0,
  
  -- Metadata
  thumbnail_url TEXT,
  is_published BOOLEAN DEFAULT false,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content collection items
CREATE TABLE IF NOT EXISTS content_collection_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  collection_id UUID REFERENCES content_collections(id) ON DELETE CASCADE,
  content_id UUID REFERENCES marketplace_content(id) ON DELETE CASCADE,
  
  sort_order INTEGER DEFAULT 0,
  is_required BOOLEAN DEFAULT true,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(collection_id, content_id)
);

-- Creator earnings and payouts
CREATE TABLE IF NOT EXISTS creator_earnings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  creator_id UUID REFERENCES content_creators(id) ON DELETE CASCADE,
  
  -- Earnings period
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  
  -- Earnings breakdown
  gross_revenue INTEGER DEFAULT 0, -- total sales in cents
  platform_fee INTEGER DEFAULT 0, -- platform's share
  net_earnings INTEGER DEFAULT 0, -- creator's share
  
  -- Transaction counts
  total_sales INTEGER DEFAULT 0,
  new_customers INTEGER DEFAULT 0,
  returning_customers INTEGER DEFAULT 0,
  
  -- Payout details
  payout_status VARCHAR(20) DEFAULT 'pending', -- pending, processing, paid, failed
  payout_amount INTEGER, -- actual payout amount (may differ due to taxes, fees)
  payout_date DATE,
  payout_reference VARCHAR(255),
  
  -- Tax and compliance
  tax_withheld INTEGER DEFAULT 0,
  tax_form_required BOOLEAN DEFAULT false,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(creator_id, period_start, period_end)
);

-- Content marketplace analytics
CREATE TABLE IF NOT EXISTS marketplace_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  content_id UUID REFERENCES marketplace_content(id) ON DELETE CASCADE,
  
  -- Performance metrics
  daily_views INTEGER DEFAULT 0,
  daily_downloads INTEGER DEFAULT 0,
  daily_purchases INTEGER DEFAULT 0,
  daily_revenue INTEGER DEFAULT 0,
  
  -- User engagement
  average_session_duration INTEGER DEFAULT 0, -- in seconds
  bounce_rate DECIMAL(5,2) DEFAULT 0,
  conversion_rate DECIMAL(5,2) DEFAULT 0,
  
  -- Geographic data
  top_countries JSONB DEFAULT '{}',
  top_regions JSONB DEFAULT '{}',
  
  -- Date
  analytics_date DATE NOT NULL,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(content_id, analytics_date)
);

-- Content submission workflow
CREATE TABLE IF NOT EXISTS content_submissions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  creator_id UUID REFERENCES content_creators(id) ON DELETE CASCADE,
  content_id UUID REFERENCES marketplace_content(id) ON DELETE CASCADE,
  
  -- Submission details
  submission_type VARCHAR(20) NOT NULL, -- new, update, resubmission
  submission_notes TEXT,
  
  -- Review process
  review_status VARCHAR(20) DEFAULT 'pending', -- pending, in_review, approved, rejected, changes_requested
  reviewer_id UUID REFERENCES auth.users(id),
  review_notes TEXT,
  review_checklist JSONB DEFAULT '{}',
  
  -- Quality assessment
  content_quality_score DECIMAL(3,2),
  technical_quality_score DECIMAL(3,2),
  educational_value_score DECIMAL(3,2),
  
  -- Timeline
  submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  review_started_at TIMESTAMP WITH TIME ZONE,
  review_completed_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_marketplace_content_creator ON marketplace_content(creator_id);
CREATE INDEX IF NOT EXISTS idx_marketplace_content_category ON marketplace_content(category_id);
CREATE INDEX IF NOT EXISTS idx_marketplace_content_status ON marketplace_content(moderation_status, is_published);
CREATE INDEX IF NOT EXISTS idx_marketplace_content_featured ON marketplace_content(is_featured, published_at);
CREATE INDEX IF NOT EXISTS idx_content_purchases_user ON content_purchases(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_content_purchases_creator ON content_purchases(creator_id, created_at);
CREATE INDEX IF NOT EXISTS idx_content_reviews_content ON content_reviews(content_id, is_approved);
CREATE INDEX IF NOT EXISTS idx_creator_earnings_creator ON creator_earnings(creator_id, period_start);
CREATE INDEX IF NOT EXISTS idx_marketplace_analytics_content ON marketplace_analytics(content_id, analytics_date);

-- Enable RLS
ALTER TABLE content_creators ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketplace_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE creator_earnings ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketplace_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_submissions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own creator profile" ON content_creators FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update their own creator profile" ON content_creators FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own creator profile" ON content_creators FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Published content is viewable by everyone" ON marketplace_content FOR SELECT USING (
  is_published = true AND moderation_status = 'approved'
);
CREATE POLICY "Creators can manage their own content" ON marketplace_content FOR ALL USING (
  EXISTS (SELECT 1 FROM content_creators WHERE content_creators.id = marketplace_content.creator_id AND content_creators.user_id = auth.uid())
);

CREATE POLICY "Users can view their own purchases" ON content_purchases FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Creators can view purchases of their content" ON content_purchases FOR SELECT USING (
  EXISTS (SELECT 1 FROM content_creators WHERE content_creators.id = content_purchases.creator_id AND content_creators.user_id = auth.uid())
);

CREATE POLICY "Users can manage their own reviews" ON content_reviews FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Approved reviews are viewable by everyone" ON content_reviews FOR SELECT USING (is_approved = true);

CREATE POLICY "Creators can view their own earnings" ON creator_earnings FOR SELECT USING (
  EXISTS (SELECT 1 FROM content_creators WHERE content_creators.id = creator_earnings.creator_id AND content_creators.user_id = auth.uid())
);

-- Functions for marketplace operations
CREATE OR REPLACE FUNCTION calculate_creator_revenue_share(
  purchase_amount INTEGER,
  creator_tier VARCHAR(20) DEFAULT 'bronze'
)
RETURNS JSONB AS $$
DECLARE
  revenue_share_percentage DECIMAL(5,2);
  creator_revenue INTEGER;
  platform_revenue INTEGER;
BEGIN
  -- Determine revenue share based on creator tier
  revenue_share_percentage := CASE creator_tier
    WHEN 'platinum' THEN 85.00
    WHEN 'gold' THEN 80.00
    WHEN 'silver' THEN 75.00
    ELSE 70.00 -- bronze
  END;
  
  creator_revenue := ROUND(purchase_amount * revenue_share_percentage / 100);
  platform_revenue := purchase_amount - creator_revenue;
  
  RETURN JSON_BUILD_OBJECT(
    'revenue_share_percentage', revenue_share_percentage,
    'creator_revenue', creator_revenue,
    'platform_revenue', platform_revenue
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION update_content_rating(content_id UUID)
RETURNS VOID AS $$
DECLARE
  avg_rating DECIMAL(3,2);
  review_count INTEGER;
BEGIN
  -- Calculate average rating and count
  SELECT 
    COALESCE(AVG(rating), 0),
    COUNT(*)
  INTO avg_rating, review_count
  FROM content_reviews
  WHERE content_reviews.content_id = update_content_rating.content_id
    AND is_approved = true;
  
  -- Update marketplace content
  UPDATE marketplace_content
  SET 
    average_rating = avg_rating,
    total_reviews = review_count,
    updated_at = NOW()
  WHERE id = content_id;
  
  -- Update creator's overall rating
  UPDATE content_creators
  SET 
    average_rating = (
      SELECT COALESCE(AVG(mc.average_rating), 0)
      FROM marketplace_content mc
      WHERE mc.creator_id = content_creators.id
        AND mc.is_published = true
    ),
    updated_at = NOW()
  WHERE id = (
    SELECT creator_id FROM marketplace_content WHERE id = content_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update ratings when reviews are added/updated
CREATE OR REPLACE FUNCTION trigger_update_content_rating()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM update_content_rating(COALESCE(NEW.content_id, OLD.content_id));
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_rating_on_review_change
  AFTER INSERT OR UPDATE OR DELETE ON content_reviews
  FOR EACH ROW
  EXECUTE FUNCTION trigger_update_content_rating();

-- Function to process content purchase
CREATE OR REPLACE FUNCTION process_content_purchase(
  user_id UUID,
  content_id UUID,
  payment_amount INTEGER,
  payment_method VARCHAR(50),
  payment_processor_id VARCHAR(255)
)
RETURNS UUID AS $$
DECLARE
  purchase_id UUID;
  creator_record content_creators%ROWTYPE;
  revenue_split JSONB;
BEGIN
  -- Get creator information
  SELECT cc.* INTO creator_record
  FROM content_creators cc
  JOIN marketplace_content mc ON cc.id = mc.creator_id
  WHERE mc.id = content_id;
  
  -- Calculate revenue split
  revenue_split := calculate_creator_revenue_share(payment_amount, creator_record.creator_tier);
  
  -- Create purchase record
  INSERT INTO content_purchases (
    user_id,
    content_id,
    creator_id,
    transaction_type,
    amount,
    payment_method,
    payment_processor_id,
    payment_status,
    creator_revenue,
    platform_revenue,
    revenue_share_percentage
  ) VALUES (
    user_id,
    content_id,
    creator_record.id,
    'purchase',
    payment_amount,
    payment_method,
    payment_processor_id,
    'completed',
    (revenue_split->>'creator_revenue')::INTEGER,
    (revenue_split->>'platform_revenue')::INTEGER,
    (revenue_split->>'revenue_share_percentage')::DECIMAL
  ) RETURNING id INTO purchase_id;
  
  -- Update content purchase count
  UPDATE marketplace_content
  SET purchase_count = purchase_count + 1
  WHERE id = content_id;
  
  -- Update creator totals
  UPDATE content_creators
  SET 
    total_sales = total_sales + 1,
    total_revenue = total_revenue + (revenue_split->>'creator_revenue')::INTEGER
  WHERE id = creator_record.id;
  
  RETURN purchase_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
