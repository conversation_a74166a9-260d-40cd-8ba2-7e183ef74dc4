-- Advanced Assessment Engine Database Schema
-- Comprehensive assessment system with automated grading capabilities

-- Assessment types and configurations
CREATE TABLE IF NOT EXISTS assessment_types (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  display_name VARCHAR(100) NOT NULL,
  description TEXT,
  grading_method VARCHAR(50) NOT NULL, -- automatic, manual, hybrid, ai_assisted
  max_attempts INTEGER DEFAULT 3,
  time_limit INTEGER, -- in minutes
  passing_score INTEGER DEFAULT 70, -- percentage
  configuration JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Question banks for reusable questions
CREATE TABLE IF NOT EXISTS question_banks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  category_id UUID REFERENCES content_categories(id),
  difficulty_level VARCHAR(20) DEFAULT 'intermediate',
  tags TEXT[],
  is_public BOOLEAN DEFAULT false,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Individual questions
CREATE TABLE IF NOT EXISTS assessment_questions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  question_bank_id UUID REFERENCES question_banks(id) ON DELETE CASCADE,
  question_type VARCHAR(50) NOT NULL, -- multiple_choice, true_false, short_answer, essay, code, practical
  question_text TEXT NOT NULL,
  question_data JSONB NOT NULL DEFAULT '{}', -- type-specific data (options, correct answers, etc.)
  
  -- Scoring configuration
  points INTEGER DEFAULT 1,
  difficulty_weight DECIMAL(3,2) DEFAULT 1.0,
  
  -- Metadata
  explanation TEXT,
  hints TEXT[],
  tags TEXT[],
  
  -- Analytics
  usage_count INTEGER DEFAULT 0,
  correct_rate DECIMAL(5,2) DEFAULT 0,
  
  -- Authoring
  created_by UUID REFERENCES auth.users(id),
  last_modified_by UUID REFERENCES auth.users(id),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Assessments (tests/quizzes/exams)
CREATE TABLE IF NOT EXISTS assessments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  assessment_type_id UUID REFERENCES assessment_types(id),
  content_id UUID REFERENCES learning_content(id), -- optional link to content
  
  -- Configuration
  instructions TEXT,
  time_limit INTEGER, -- in minutes
  max_attempts INTEGER DEFAULT 3,
  passing_score INTEGER DEFAULT 70,
  randomize_questions BOOLEAN DEFAULT false,
  randomize_options BOOLEAN DEFAULT false,
  show_results_immediately BOOLEAN DEFAULT true,
  allow_review BOOLEAN DEFAULT true,
  
  -- Scheduling
  available_from TIMESTAMP WITH TIME ZONE,
  available_until TIMESTAMP WITH TIME ZONE,
  
  -- Access control
  access_level VARCHAR(20) DEFAULT 'free',
  required_prerequisites UUID[],
  
  -- Status
  is_published BOOLEAN DEFAULT false,
  is_proctored BOOLEAN DEFAULT false,
  
  -- Analytics
  total_attempts INTEGER DEFAULT 0,
  average_score DECIMAL(5,2) DEFAULT 0,
  completion_rate DECIMAL(5,2) DEFAULT 0,
  
  -- Authoring
  created_by UUID REFERENCES auth.users(id),
  last_modified_by UUID REFERENCES auth.users(id),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Assessment-question relationships
CREATE TABLE IF NOT EXISTS assessment_questions_map (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  assessment_id UUID REFERENCES assessments(id) ON DELETE CASCADE,
  question_id UUID REFERENCES assessment_questions(id) ON DELETE CASCADE,
  sort_order INTEGER DEFAULT 0,
  points_override INTEGER, -- override default question points
  is_required BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Assessment attempts by users
CREATE TABLE IF NOT EXISTS assessment_attempts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  assessment_id UUID REFERENCES assessments(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id),
  attempt_number INTEGER NOT NULL,
  
  -- Attempt data
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  submitted_at TIMESTAMP WITH TIME ZONE,
  time_spent INTEGER, -- in seconds
  
  -- Scoring
  total_points INTEGER DEFAULT 0,
  max_points INTEGER DEFAULT 0,
  score_percentage DECIMAL(5,2) DEFAULT 0,
  passed BOOLEAN DEFAULT false,
  
  -- Status
  status VARCHAR(20) DEFAULT 'in_progress', -- in_progress, submitted, graded, expired
  
  -- Proctoring data
  proctoring_data JSONB DEFAULT '{}',
  
  -- Grading
  auto_graded_at TIMESTAMP WITH TIME ZONE,
  manually_graded_at TIMESTAMP WITH TIME ZONE,
  graded_by UUID REFERENCES auth.users(id),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(assessment_id, user_id, attempt_number)
);

-- Individual question responses
CREATE TABLE IF NOT EXISTS assessment_responses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  attempt_id UUID REFERENCES assessment_attempts(id) ON DELETE CASCADE,
  question_id UUID REFERENCES assessment_questions(id),
  
  -- Response data
  response_data JSONB NOT NULL DEFAULT '{}', -- user's answer(s)
  response_text TEXT, -- for text-based responses
  
  -- Scoring
  points_earned INTEGER DEFAULT 0,
  max_points INTEGER DEFAULT 0,
  is_correct BOOLEAN,
  
  -- Timing
  time_spent INTEGER, -- in seconds
  answered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Grading
  auto_graded BOOLEAN DEFAULT false,
  manually_graded BOOLEAN DEFAULT false,
  grader_feedback TEXT,
  graded_by UUID REFERENCES auth.users(id),
  graded_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Automated grading rules
CREATE TABLE IF NOT EXISTS grading_rules (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  question_type VARCHAR(50) NOT NULL,
  rule_name VARCHAR(100) NOT NULL,
  rule_logic JSONB NOT NULL, -- grading algorithm configuration
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Assessment analytics and insights
CREATE TABLE IF NOT EXISTS assessment_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  assessment_id UUID REFERENCES assessments(id) ON DELETE CASCADE,
  question_id UUID REFERENCES assessment_questions(id),
  
  -- Performance metrics
  total_responses INTEGER DEFAULT 0,
  correct_responses INTEGER DEFAULT 0,
  average_time_spent INTEGER DEFAULT 0,
  difficulty_index DECIMAL(5,2) DEFAULT 0,
  discrimination_index DECIMAL(5,2) DEFAULT 0,
  
  -- Calculated at
  calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(assessment_id, question_id)
);

-- Insert default assessment types
INSERT INTO assessment_types (name, display_name, description, grading_method) VALUES
('quiz', 'Quick Quiz', 'Short knowledge check with immediate feedback', 'automatic'),
('exam', 'Formal Exam', 'Comprehensive assessment with proctoring options', 'hybrid'),
('practice', 'Practice Test', 'Low-stakes practice with unlimited attempts', 'automatic'),
('certification', 'Certification Exam', 'High-stakes certification assessment', 'manual'),
('coding', 'Coding Challenge', 'Programming assessment with automated testing', 'ai_assisted'),
('practical', 'Practical Lab', 'Hands-on practical assessment', 'manual')
ON CONFLICT (name) DO NOTHING;

-- Insert default grading rules
INSERT INTO grading_rules (question_type, rule_name, rule_logic) VALUES
('multiple_choice', 'Exact Match', '{"method": "exact_match", "case_sensitive": false}'),
('true_false', 'Boolean Match', '{"method": "boolean_match"}'),
('short_answer', 'Keyword Matching', '{"method": "keyword_match", "keywords": [], "partial_credit": true}'),
('code', 'Test Case Execution', '{"method": "test_cases", "timeout": 30, "memory_limit": "128MB"}'),
('essay', 'Manual Grading Required', '{"method": "manual_only"}')
ON CONFLICT (question_type, rule_name) DO NOTHING;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_assessment_questions_bank ON assessment_questions(question_bank_id);
CREATE INDEX IF NOT EXISTS idx_assessment_questions_type ON assessment_questions(question_type);
CREATE INDEX IF NOT EXISTS idx_assessments_type ON assessments(assessment_type_id);
CREATE INDEX IF NOT EXISTS idx_assessments_published ON assessments(is_published, available_from, available_until);
CREATE INDEX IF NOT EXISTS idx_assessment_attempts_user ON assessment_attempts(user_id, assessment_id);
CREATE INDEX IF NOT EXISTS idx_assessment_attempts_status ON assessment_attempts(status, started_at);
CREATE INDEX IF NOT EXISTS idx_assessment_responses_attempt ON assessment_responses(attempt_id);
CREATE INDEX IF NOT EXISTS idx_assessment_responses_question ON assessment_responses(question_id);
CREATE INDEX IF NOT EXISTS idx_assessment_analytics_assessment ON assessment_analytics(assessment_id);

-- Enable RLS
ALTER TABLE question_banks ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE grading_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Public question banks are viewable by everyone" ON question_banks FOR SELECT USING (is_public = true);
CREATE POLICY "Question bank creators can manage their banks" ON question_banks FOR ALL USING (
  auth.uid() = created_by OR 
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin', 'instructor'))
);

CREATE POLICY "Published assessments are viewable by everyone" ON assessments FOR SELECT USING (
  is_published = true AND (available_from IS NULL OR available_from <= NOW()) AND (available_until IS NULL OR available_until >= NOW())
);

CREATE POLICY "Assessment creators can manage their assessments" ON assessments FOR ALL USING (
  auth.uid() = created_by OR 
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin', 'instructor'))
);

CREATE POLICY "Users can view their own attempts" ON assessment_attempts FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own attempts" ON assessment_attempts FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Instructors can view all attempts" ON assessment_attempts FOR SELECT USING (
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin', 'instructor'))
);

CREATE POLICY "Users can manage their own responses" ON assessment_responses FOR ALL USING (
  EXISTS (SELECT 1 FROM assessment_attempts WHERE assessment_attempts.id = assessment_responses.attempt_id AND assessment_attempts.user_id = auth.uid())
);

CREATE POLICY "Grading rules are viewable by instructors" ON grading_rules FOR SELECT USING (
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin', 'instructor'))
);

-- Functions for automated grading
CREATE OR REPLACE FUNCTION auto_grade_response(response_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  response_record assessment_responses%ROWTYPE;
  question_record assessment_questions%ROWTYPE;
  grading_rule grading_rules%ROWTYPE;
  is_correct BOOLEAN := false;
  points_earned INTEGER := 0;
BEGIN
  -- Get response and question data
  SELECT * INTO response_record FROM assessment_responses WHERE id = response_id;
  SELECT * INTO question_record FROM assessment_questions WHERE id = response_record.question_id;
  
  -- Get grading rule for question type
  SELECT * INTO grading_rule FROM grading_rules 
  WHERE question_type = question_record.question_type AND is_active = true 
  LIMIT 1;
  
  -- Apply grading logic based on question type
  CASE question_record.question_type
    WHEN 'multiple_choice' THEN
      is_correct := (response_record.response_data->>'answer' = question_record.question_data->>'correct_answer');
      points_earned := CASE WHEN is_correct THEN question_record.points ELSE 0 END;
      
    WHEN 'true_false' THEN
      is_correct := (response_record.response_data->>'answer')::boolean = (question_record.question_data->>'correct_answer')::boolean;
      points_earned := CASE WHEN is_correct THEN question_record.points ELSE 0 END;
      
    WHEN 'short_answer' THEN
      -- Simple keyword matching (can be enhanced with NLP)
      is_correct := LOWER(response_record.response_text) LIKE '%' || LOWER(question_record.question_data->>'correct_answer') || '%';
      points_earned := CASE WHEN is_correct THEN question_record.points ELSE 0 END;
      
    ELSE
      -- Manual grading required
      RETURN false;
  END CASE;
  
  -- Update response with grading results
  UPDATE assessment_responses SET
    is_correct = is_correct,
    points_earned = points_earned,
    max_points = question_record.points,
    auto_graded = true,
    graded_at = NOW()
  WHERE id = response_id;
  
  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate assessment attempt score
CREATE OR REPLACE FUNCTION calculate_attempt_score(attempt_id UUID)
RETURNS VOID AS $$
DECLARE
  total_earned INTEGER := 0;
  total_possible INTEGER := 0;
  score_percentage DECIMAL(5,2);
  passing_score INTEGER;
  passed BOOLEAN := false;
BEGIN
  -- Calculate totals from responses
  SELECT 
    COALESCE(SUM(points_earned), 0),
    COALESCE(SUM(max_points), 0)
  INTO total_earned, total_possible
  FROM assessment_responses 
  WHERE attempt_id = calculate_attempt_score.attempt_id;
  
  -- Calculate percentage
  score_percentage := CASE 
    WHEN total_possible > 0 THEN (total_earned::DECIMAL / total_possible::DECIMAL) * 100
    ELSE 0 
  END;
  
  -- Get passing score from assessment
  SELECT a.passing_score INTO passing_score
  FROM assessment_attempts aa
  JOIN assessments a ON aa.assessment_id = a.id
  WHERE aa.id = calculate_attempt_score.attempt_id;
  
  -- Determine if passed
  passed := score_percentage >= passing_score;
  
  -- Update attempt record
  UPDATE assessment_attempts SET
    total_points = total_earned,
    max_points = total_possible,
    score_percentage = score_percentage,
    passed = passed,
    status = 'graded',
    auto_graded_at = NOW()
  WHERE id = calculate_attempt_score.attempt_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
