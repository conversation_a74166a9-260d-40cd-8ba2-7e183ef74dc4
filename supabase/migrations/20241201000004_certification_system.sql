-- Certification System Database Schema
-- Comprehensive certification system with industry recognition and blockchain verification

-- Certification programs and tracks
CREATE TABLE IF NOT EXISTS certification_programs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(20) UNIQUE NOT NULL, -- e.g., <PERSON>CW<PERSON> (XCerberus Certified Web Pentester)
  description TEXT,
  
  -- Program details
  level VARCHAR(20) NOT NULL, -- foundation, associate, professional, expert
  category VARCHAR(50) NOT NULL, -- web_security, network_security, etc.
  version VARCHAR(10) DEFAULT '1.0',
  
  -- Requirements
  prerequisites TEXT[],
  required_experience_months INTEGER DEFAULT 0,
  required_assessments UUID[], -- assessment IDs
  required_labs UUID[], -- lab template IDs
  required_courses UUID[], -- content IDs
  
  -- Certification details
  validity_period_months INTEGER DEFAULT 36, -- 3 years default
  renewal_requirements TEXT[],
  continuing_education_hours INTEGER DEFAULT 40,
  
  -- Industry recognition
  industry_partners TEXT[],
  accreditation_bodies TEXT[],
  recognition_level VARCHAR(20) DEFAULT 'internal', -- internal, industry, government
  
  -- Pricing and access
  certification_fee INTEGER DEFAULT 0, -- in cents
  access_level VARCHAR(20) DEFAULT 'premium',
  
  -- Status
  is_active BOOLEAN DEFAULT true,
  launch_date DATE,
  
  -- Metadata
  badge_image_url TEXT,
  certificate_template_url TEXT,
  
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Individual certifications earned by users
CREATE TABLE IF NOT EXISTS user_certifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  program_id UUID REFERENCES certification_programs(id),
  
  -- Certification details
  certificate_number VARCHAR(50) UNIQUE NOT NULL,
  issued_date DATE NOT NULL DEFAULT CURRENT_DATE,
  expiry_date DATE NOT NULL,
  
  -- Verification
  verification_code VARCHAR(100) UNIQUE NOT NULL,
  blockchain_hash VARCHAR(255), -- for blockchain verification
  digital_signature TEXT,
  
  -- Achievement details
  final_score INTEGER,
  grade VARCHAR(10), -- A+, A, B+, B, C+, C
  completion_date DATE,
  
  -- Status
  status VARCHAR(20) DEFAULT 'active', -- active, expired, revoked, suspended
  revocation_reason TEXT,
  
  -- Renewal tracking
  last_renewed_date DATE,
  renewal_due_date DATE,
  continuing_education_completed INTEGER DEFAULT 0,
  
  -- Metadata
  certificate_pdf_url TEXT,
  badge_url TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, program_id, issued_date)
);

-- Certification requirements tracking
CREATE TABLE IF NOT EXISTS certification_requirements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  program_id UUID REFERENCES certification_programs(id) ON DELETE CASCADE,
  
  -- Requirement details
  requirement_type VARCHAR(50) NOT NULL, -- assessment, lab, course, experience, project
  requirement_id UUID, -- references to assessments, labs, etc.
  requirement_name VARCHAR(100) NOT NULL,
  description TEXT,
  
  -- Completion criteria
  minimum_score INTEGER,
  required_completion BOOLEAN DEFAULT true,
  weight DECIMAL(5,2) DEFAULT 1.0, -- weight in overall certification score
  
  -- Ordering and grouping
  category VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_optional BOOLEAN DEFAULT false,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User progress towards certifications
CREATE TABLE IF NOT EXISTS certification_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  program_id UUID REFERENCES certification_programs(id),
  requirement_id UUID REFERENCES certification_requirements(id),
  
  -- Progress details
  status VARCHAR(20) DEFAULT 'not_started', -- not_started, in_progress, completed, failed
  score INTEGER,
  completion_date DATE,
  
  -- Attempt tracking
  attempts INTEGER DEFAULT 0,
  last_attempt_date DATE,
  
  -- Evidence and verification
  evidence_urls TEXT[],
  verified_by UUID REFERENCES auth.users(id),
  verification_date DATE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, program_id, requirement_id)
);

-- Certification exam sessions (for proctored exams)
CREATE TABLE IF NOT EXISTS certification_exam_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  program_id UUID REFERENCES certification_programs(id),
  assessment_id UUID REFERENCES assessments(id),
  
  -- Session details
  session_code VARCHAR(20) UNIQUE NOT NULL,
  scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
  actual_start_time TIMESTAMP WITH TIME ZONE,
  actual_end_time TIMESTAMP WITH TIME ZONE,
  
  -- Proctoring details
  proctor_id UUID REFERENCES auth.users(id),
  proctoring_type VARCHAR(20) DEFAULT 'online', -- online, in_person, automated
  proctoring_notes TEXT,
  
  -- Session status
  status VARCHAR(20) DEFAULT 'scheduled', -- scheduled, in_progress, completed, cancelled, no_show
  
  -- Security and verification
  identity_verified BOOLEAN DEFAULT false,
  environment_verified BOOLEAN DEFAULT false,
  recording_url TEXT,
  
  -- Results
  passed BOOLEAN,
  score INTEGER,
  grade VARCHAR(10),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Continuing education activities
CREATE TABLE IF NOT EXISTS continuing_education (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  certification_id UUID REFERENCES user_certifications(id),
  
  -- Activity details
  activity_type VARCHAR(50) NOT NULL, -- course, conference, workshop, webinar, publication
  activity_name VARCHAR(200) NOT NULL,
  provider VARCHAR(100),
  description TEXT,
  
  -- Credit details
  hours_claimed DECIMAL(4,2) NOT NULL,
  hours_approved DECIMAL(4,2),
  credit_category VARCHAR(50), -- technical, management, ethics, etc.
  
  -- Verification
  completion_date DATE NOT NULL,
  certificate_url TEXT,
  verified_by UUID REFERENCES auth.users(id),
  verification_date DATE,
  verification_status VARCHAR(20) DEFAULT 'pending', -- pending, approved, rejected
  
  -- Evidence
  evidence_urls TEXT[],
  notes TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Certification analytics and statistics
CREATE TABLE IF NOT EXISTS certification_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  program_id UUID REFERENCES certification_programs(id),
  
  -- Enrollment and completion stats
  total_enrolled INTEGER DEFAULT 0,
  total_certified INTEGER DEFAULT 0,
  total_active_certificates INTEGER DEFAULT 0,
  total_expired_certificates INTEGER DEFAULT 0,
  
  -- Performance metrics
  average_completion_time_days INTEGER DEFAULT 0,
  average_score DECIMAL(5,2) DEFAULT 0,
  pass_rate DECIMAL(5,2) DEFAULT 0,
  
  -- Demographic insights
  certification_by_experience JSONB DEFAULT '{}',
  certification_by_region JSONB DEFAULT '{}',
  certification_by_industry JSONB DEFAULT '{}',
  
  -- Calculated at
  calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(program_id)
);

-- Insert default certification programs
INSERT INTO certification_programs (name, code, description, level, category, required_experience_months) VALUES
('XCerberus Certified Web Security Specialist', 'XCWSS', 'Foundation-level certification in web application security', 'foundation', 'web_security', 0),
('XCerberus Certified Penetration Tester', 'XCPT', 'Professional-level certification in penetration testing', 'professional', 'penetration_testing', 12),
('XCerberus Certified Security Analyst', 'XCSA', 'Associate-level certification in security analysis and incident response', 'associate', 'security_analysis', 6),
('XCerberus Certified Network Security Expert', 'XCNSE', 'Expert-level certification in network security', 'expert', 'network_security', 24),
('XCerberus Certified Cloud Security Professional', 'XCCSP', 'Professional-level certification in cloud security', 'professional', 'cloud_security', 18)
ON CONFLICT (code) DO NOTHING;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_certification_programs_category ON certification_programs(category, level);
CREATE INDEX IF NOT EXISTS idx_certification_programs_active ON certification_programs(is_active, launch_date);
CREATE INDEX IF NOT EXISTS idx_user_certifications_user ON user_certifications(user_id, status);
CREATE INDEX IF NOT EXISTS idx_user_certifications_program ON user_certifications(program_id, status);
CREATE INDEX IF NOT EXISTS idx_user_certifications_expiry ON user_certifications(expiry_date, status);
CREATE INDEX IF NOT EXISTS idx_certification_progress_user ON certification_progress(user_id, program_id);
CREATE INDEX IF NOT EXISTS idx_certification_exam_sessions_user ON certification_exam_sessions(user_id, scheduled_date);
CREATE INDEX IF NOT EXISTS idx_continuing_education_user ON continuing_education(user_id, certification_id);

-- Enable RLS
ALTER TABLE certification_programs ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_certifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE certification_requirements ENABLE ROW LEVEL SECURITY;
ALTER TABLE certification_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE certification_exam_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE continuing_education ENABLE ROW LEVEL SECURITY;
ALTER TABLE certification_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Active certification programs are viewable by everyone" ON certification_programs FOR SELECT USING (is_active = true);
CREATE POLICY "Certification managers can manage programs" ON certification_programs FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin', 'certification_manager'))
);

CREATE POLICY "Users can view their own certifications" ON user_certifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Public verification of certifications" ON user_certifications FOR SELECT USING (true); -- For verification purposes
CREATE POLICY "Certification managers can manage certifications" ON user_certifications FOR ALL USING (
  auth.uid() = user_id OR 
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin', 'certification_manager'))
);

CREATE POLICY "Users can view their own progress" ON certification_progress FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update their own progress" ON certification_progress FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "System can insert progress records" ON certification_progress FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can manage their own exam sessions" ON certification_exam_sessions FOR ALL USING (
  auth.uid() = user_id OR auth.uid() = proctor_id OR
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin', 'proctor'))
);

CREATE POLICY "Users can manage their own continuing education" ON continuing_education FOR ALL USING (
  auth.uid() = user_id OR 
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin', 'certification_manager'))
);

-- Functions for certification management
CREATE OR REPLACE FUNCTION generate_certificate_number(program_code VARCHAR(20))
RETURNS VARCHAR(50) AS $$
DECLARE
  year_suffix VARCHAR(2);
  sequence_num INTEGER;
  certificate_num VARCHAR(50);
BEGIN
  -- Get last 2 digits of current year
  year_suffix := RIGHT(EXTRACT(YEAR FROM CURRENT_DATE)::TEXT, 2);
  
  -- Get next sequence number for this program and year
  SELECT COALESCE(MAX(CAST(RIGHT(certificate_number, 6) AS INTEGER)), 0) + 1
  INTO sequence_num
  FROM user_certifications uc
  JOIN certification_programs cp ON uc.program_id = cp.id
  WHERE cp.code = program_code
    AND uc.certificate_number LIKE program_code || '-' || year_suffix || '%';
  
  -- Format: XCWSS-24-000001
  certificate_num := program_code || '-' || year_suffix || '-' || LPAD(sequence_num::TEXT, 6, '0');
  
  RETURN certificate_num;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION generate_verification_code()
RETURNS VARCHAR(100) AS $$
BEGIN
  RETURN 'VERIFY-' || UPPER(SUBSTRING(MD5(RANDOM()::TEXT || CLOCK_TIMESTAMP()::TEXT) FROM 1 FOR 16));
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION award_certification(
  user_id UUID,
  program_id UUID,
  final_score INTEGER,
  completion_date DATE DEFAULT CURRENT_DATE
)
RETURNS UUID AS $$
DECLARE
  certification_id UUID;
  program_record certification_programs%ROWTYPE;
  certificate_num VARCHAR(50);
  verification_code VARCHAR(100);
  expiry_date DATE;
  grade VARCHAR(10);
BEGIN
  -- Get program details
  SELECT * INTO program_record FROM certification_programs WHERE id = program_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Certification program not found';
  END IF;
  
  -- Generate certificate number and verification code
  certificate_num := generate_certificate_number(program_record.code);
  verification_code := generate_verification_code();
  
  -- Calculate expiry date
  expiry_date := completion_date + INTERVAL '1 month' * program_record.validity_period_months;
  
  -- Determine grade based on score
  grade := CASE 
    WHEN final_score >= 95 THEN 'A+'
    WHEN final_score >= 90 THEN 'A'
    WHEN final_score >= 85 THEN 'B+'
    WHEN final_score >= 80 THEN 'B'
    WHEN final_score >= 75 THEN 'C+'
    WHEN final_score >= 70 THEN 'C'
    ELSE 'F'
  END;
  
  -- Create certification record
  INSERT INTO user_certifications (
    user_id,
    program_id,
    certificate_number,
    verification_code,
    issued_date,
    expiry_date,
    completion_date,
    final_score,
    grade,
    status
  ) VALUES (
    user_id,
    program_id,
    certificate_num,
    verification_code,
    completion_date,
    expiry_date,
    completion_date,
    final_score,
    grade,
    'active'
  ) RETURNING id INTO certification_id;
  
  RETURN certification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION check_certification_eligibility(
  user_id UUID,
  program_id UUID
)
RETURNS JSONB AS $$
DECLARE
  result JSONB;
  total_requirements INTEGER;
  completed_requirements INTEGER;
  missing_requirements JSONB;
BEGIN
  -- Get total requirements
  SELECT COUNT(*) INTO total_requirements
  FROM certification_requirements
  WHERE program_id = check_certification_eligibility.program_id
    AND is_optional = false;
  
  -- Get completed requirements
  SELECT COUNT(*) INTO completed_requirements
  FROM certification_progress cp
  JOIN certification_requirements cr ON cp.requirement_id = cr.id
  WHERE cp.user_id = check_certification_eligibility.user_id
    AND cp.program_id = check_certification_eligibility.program_id
    AND cp.status = 'completed'
    AND cr.is_optional = false;
  
  -- Get missing requirements
  SELECT COALESCE(JSON_AGG(
    JSON_BUILD_OBJECT(
      'requirement_name', cr.requirement_name,
      'requirement_type', cr.requirement_type,
      'description', cr.description
    )
  ), '[]'::JSON) INTO missing_requirements
  FROM certification_requirements cr
  LEFT JOIN certification_progress cp ON (
    cp.requirement_id = cr.id 
    AND cp.user_id = check_certification_eligibility.user_id
  )
  WHERE cr.program_id = check_certification_eligibility.program_id
    AND cr.is_optional = false
    AND (cp.status IS NULL OR cp.status != 'completed');
  
  result := JSON_BUILD_OBJECT(
    'eligible', completed_requirements >= total_requirements,
    'total_requirements', total_requirements,
    'completed_requirements', completed_requirements,
    'completion_percentage', ROUND((completed_requirements::DECIMAL / NULLIF(total_requirements, 0)) * 100, 2),
    'missing_requirements', missing_requirements
  );
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
