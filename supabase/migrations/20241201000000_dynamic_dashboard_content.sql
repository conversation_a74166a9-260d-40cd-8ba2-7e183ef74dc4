-- Migration for Dynamic Dashboard Content Management
-- This migration creates tables for managing dashboard content dynamically

-- Dashboard configurations table
CREATE TABLE IF NOT EXISTS dashboard_configurations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  dashboard_id VARCHAR(50) NOT NULL UNIQUE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  access_level VARCHAR(20) DEFAULT 'free',
  configuration JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Dashboard content blocks table
CREATE TABLE IF NOT EXISTS dashboard_content_blocks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  dashboard_id VARCHAR(50) NOT NULL,
  block_type VARCHAR(50) NOT NULL, -- 'widget', 'chart', 'text', 'image', etc.
  block_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
  content JSONB NOT NULL DEFAULT '{}',
  position_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  access_level VARCHAR(20) DEFAULT 'free',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  FOREIGN KEY (dashboard_id) REFERENCES dashboard_configurations(dashboard_id) ON DELETE CASCADE
);

-- White-label instances table
CREATE TABLE IF NOT EXISTS whitelabel_instances (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  instance_name VARCHAR(100) NOT NULL,
  domain VARCHAR(255) UNIQUE,
  branding JSONB DEFAULT '{}', -- logo, colors, theme, etc.
  configuration JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  owner_id UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Dashboard analytics table
CREATE TABLE IF NOT EXISTS dashboard_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  dashboard_id VARCHAR(50) NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  session_id VARCHAR(100),
  event_type VARCHAR(50) NOT NULL, -- 'view', 'click', 'interaction', etc.
  event_data JSONB DEFAULT '{}',
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ip_address INET,
  user_agent TEXT
);

-- Subscription expiry tracking table
CREATE TABLE IF NOT EXISTS subscription_expiry_tracking (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  subscription_tier VARCHAR(20) NOT NULL,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT true,
  auto_renewal BOOLEAN DEFAULT false,
  notification_sent BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Platform notifications table
CREATE TABLE IF NOT EXISTS platform_notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title VARCHAR(200) NOT NULL,
  message TEXT NOT NULL,
  notification_type VARCHAR(50) DEFAULT 'info', -- 'info', 'warning', 'success', 'error'
  target_audience VARCHAR(50) DEFAULT 'all', -- 'all', 'free', 'premium', 'business', 'admin'
  is_active BOOLEAN DEFAULT true,
  scheduled_for TIMESTAMP WITH TIME ZONE,
  sent_at TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default dashboard configurations
INSERT INTO dashboard_configurations (dashboard_id, name, description, access_level, configuration) VALUES
('main', 'Main Dashboard', 'Unified dashboard with analytics and recommendations', 'free', '{"layout": "grid", "widgets": ["analytics", "progress", "recommendations"]}'),
('enhanced', 'Enhanced Dashboard', 'Advanced dashboard with learning paths and AI assistant', 'premium', '{"layout": "advanced", "widgets": ["learning_paths", "ai_assistant", "analytics"]}'),
('simplified', 'Simplified Dashboard', 'Clean minimal dashboard for basic users', 'free', '{"layout": "simple", "widgets": ["coins", "quick_actions", "activity"]}'),
('admin', 'Admin Dashboard', 'Administrative interface for platform management', 'admin', '{"layout": "admin", "widgets": ["user_management", "analytics", "settings"]}'),
('super_admin', 'Super Admin Dashboard', 'Complete platform management and control', 'super_admin', '{"layout": "super_admin", "widgets": ["platform_stats", "dashboard_management", "whitelabel"]}}')
ON CONFLICT (dashboard_id) DO NOTHING;

-- Insert default content blocks for main dashboard
INSERT INTO dashboard_content_blocks (dashboard_id, block_type, block_name, content, position_order) VALUES
('main', 'widget', 'Learning Analytics', '{"type": "chart", "data_source": "user_progress", "chart_type": "line"}', 1),
('main', 'widget', 'Challenge Progress', '{"type": "progress", "data_source": "challenges", "display": "circular"}', 2),
('main', 'widget', 'Recommendations', '{"type": "list", "data_source": "ai_recommendations", "limit": 5}', 3),
('main', 'widget', 'Recent Activity', '{"type": "timeline", "data_source": "user_activity", "limit": 10}', 4)
ON CONFLICT DO NOTHING;

-- Insert default content blocks for enhanced dashboard
INSERT INTO dashboard_content_blocks (dashboard_id, block_type, block_name, content, position_order, access_level) VALUES
('enhanced', 'widget', 'Learning Paths', '{"type": "grid", "data_source": "learning_paths", "interactive": true}', 1, 'premium'),
('enhanced', 'widget', 'AI Assistant', '{"type": "chat", "service": "ai_assistant", "features": ["help", "recommendations"]}', 2, 'premium'),
('enhanced', 'widget', 'Advanced Analytics', '{"type": "dashboard", "charts": ["progress", "skills", "performance"]}', 3, 'premium'),
('enhanced', 'widget', 'Module Progress', '{"type": "detailed_progress", "data_source": "learning_modules"}', 4, 'premium')
ON CONFLICT DO NOTHING;

-- Insert default content blocks for simplified dashboard
INSERT INTO dashboard_content_blocks (dashboard_id, block_type, block_name, content, position_order) VALUES
('simplified', 'widget', 'XCerberus Coins', '{"type": "counter", "data_source": "user_coins", "display": "large"}', 1),
('simplified', 'widget', 'Quick Actions', '{"type": "button_grid", "actions": ["learn", "challenges", "profile"]}', 2),
('simplified', 'widget', 'Recent Activity', '{"type": "simple_list", "data_source": "user_activity", "limit": 5}', 3),
('simplified', 'widget', 'Subscription Status', '{"type": "status_card", "data_source": "subscription"}', 4)
ON CONFLICT DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_dashboard_content_blocks_dashboard_id ON dashboard_content_blocks(dashboard_id);
CREATE INDEX IF NOT EXISTS idx_dashboard_content_blocks_active ON dashboard_content_blocks(is_active);
CREATE INDEX IF NOT EXISTS idx_dashboard_analytics_dashboard_id ON dashboard_analytics(dashboard_id);
CREATE INDEX IF NOT EXISTS idx_dashboard_analytics_user_id ON dashboard_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_dashboard_analytics_timestamp ON dashboard_analytics(timestamp);
CREATE INDEX IF NOT EXISTS idx_subscription_expiry_end_date ON subscription_expiry_tracking(end_date);
CREATE INDEX IF NOT EXISTS idx_subscription_expiry_active ON subscription_expiry_tracking(is_active);
CREATE INDEX IF NOT EXISTS idx_platform_notifications_active ON platform_notifications(is_active);
CREATE INDEX IF NOT EXISTS idx_platform_notifications_audience ON platform_notifications(target_audience);

-- Create RLS policies
ALTER TABLE dashboard_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE dashboard_content_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE whitelabel_instances ENABLE ROW LEVEL SECURITY;
ALTER TABLE dashboard_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_expiry_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE platform_notifications ENABLE ROW LEVEL SECURITY;

-- Policies for dashboard_configurations (admin only for modifications)
CREATE POLICY "Dashboard configurations are viewable by everyone" ON dashboard_configurations FOR SELECT USING (true);
CREATE POLICY "Dashboard configurations are modifiable by admins" ON dashboard_configurations FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin'))
);

-- Policies for dashboard_content_blocks (admin only for modifications)
CREATE POLICY "Dashboard content blocks are viewable by everyone" ON dashboard_content_blocks FOR SELECT USING (true);
CREATE POLICY "Dashboard content blocks are modifiable by admins" ON dashboard_content_blocks FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin'))
);

-- Policies for whitelabel_instances (super admin only)
CREATE POLICY "Whitelabel instances are viewable by super admins" ON whitelabel_instances FOR SELECT USING (
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role = 'super_admin')
);
CREATE POLICY "Whitelabel instances are modifiable by super admins" ON whitelabel_instances FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role = 'super_admin')
);

-- Policies for dashboard_analytics (users can view their own, admins can view all)
CREATE POLICY "Users can view their own analytics" ON dashboard_analytics FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Admins can view all analytics" ON dashboard_analytics FOR SELECT USING (
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin'))
);
CREATE POLICY "Analytics can be inserted by anyone" ON dashboard_analytics FOR INSERT WITH CHECK (true);

-- Policies for subscription_expiry_tracking (users can view their own, admins can view all)
CREATE POLICY "Users can view their own subscription tracking" ON subscription_expiry_tracking FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Admins can view all subscription tracking" ON subscription_expiry_tracking FOR SELECT USING (
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin'))
);
CREATE POLICY "Admins can modify subscription tracking" ON subscription_expiry_tracking FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin'))
);

-- Policies for platform_notifications (admins only)
CREATE POLICY "Platform notifications are viewable by everyone" ON platform_notifications FOR SELECT USING (is_active = true);
CREATE POLICY "Platform notifications are modifiable by admins" ON platform_notifications FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin'))
);

-- Create functions for dashboard management
CREATE OR REPLACE FUNCTION get_dashboard_content(dashboard_name TEXT, user_access_level TEXT DEFAULT 'free')
RETURNS TABLE (
  block_id UUID,
  block_type VARCHAR(50),
  block_name VARCHAR(100),
  content JSONB,
  position_order INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    dcb.id,
    dcb.block_type,
    dcb.block_name,
    dcb.content,
    dcb.position_order
  FROM dashboard_content_blocks dcb
  JOIN dashboard_configurations dc ON dcb.dashboard_id = dc.dashboard_id
  WHERE dc.dashboard_id = dashboard_name
    AND dcb.is_active = true
    AND dc.is_active = true
    AND (
      dcb.access_level = 'free' OR
      (user_access_level = 'premium' AND dcb.access_level IN ('free', 'premium')) OR
      (user_access_level = 'business' AND dcb.access_level IN ('free', 'premium', 'business')) OR
      (user_access_level IN ('admin', 'super_admin'))
    )
  ORDER BY dcb.position_order;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to track dashboard usage
CREATE OR REPLACE FUNCTION track_dashboard_usage(
  dashboard_name TEXT,
  event_type_param TEXT,
  event_data_param JSONB DEFAULT '{}'
) RETURNS VOID AS $$
BEGIN
  INSERT INTO dashboard_analytics (dashboard_id, user_id, event_type, event_data)
  VALUES (dashboard_name, auth.uid(), event_type_param, event_data_param);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
