-- Content Management System Database Schema
-- Comprehensive schema for dynamic learning materials management

-- Content categories and taxonomy
CREATE TABLE IF NOT EXISTS content_categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  parent_id UUID REFERENCES content_categories(id),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content types (lesson, video, quiz, lab, etc.)
CREATE TABLE IF NOT EXISTS content_types (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  display_name VA<PERSON>HA<PERSON>(100) NOT NULL,
  description TEXT,
  schema_definition JSONB NOT NULL, -- JSON schema for content structure
  template_config J<PERSON>NB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Main content table
CREATE TABLE IF NOT EXISTS learning_content (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  content_type_id UUID REFERENCES content_types(id) NOT NULL,
  category_id UUID REFERENCES content_categories(id),
  
  -- Content data
  content_data JSONB NOT NULL DEFAULT '{}', -- Flexible content structure
  metadata JSONB DEFAULT '{}',
  
  -- Content properties
  difficulty_level VARCHAR(20) DEFAULT 'beginner', -- beginner, intermediate, advanced, expert
  estimated_duration INTEGER, -- in minutes
  prerequisites TEXT[],
  learning_objectives TEXT[],
  tags TEXT[],
  
  -- Access control
  access_level VARCHAR(20) DEFAULT 'free', -- free, premium, business, enterprise
  is_published BOOLEAN DEFAULT false,
  publish_date TIMESTAMP WITH TIME ZONE,
  
  -- SEO and discovery
  excerpt TEXT,
  featured_image_url TEXT,
  seo_title VARCHAR(255),
  seo_description TEXT,
  
  -- Versioning
  version INTEGER DEFAULT 1,
  parent_version_id UUID REFERENCES learning_content(id),
  
  -- Authoring
  author_id UUID REFERENCES auth.users(id),
  last_modified_by UUID REFERENCES auth.users(id),
  
  -- Analytics
  view_count INTEGER DEFAULT 0,
  completion_count INTEGER DEFAULT 0,
  average_rating DECIMAL(3,2) DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content blocks for modular content structure
CREATE TABLE IF NOT EXISTS content_blocks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  content_id UUID REFERENCES learning_content(id) ON DELETE CASCADE,
  block_type VARCHAR(50) NOT NULL, -- text, video, image, code, quiz, interactive
  block_data JSONB NOT NULL DEFAULT '{}',
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Media assets management
CREATE TABLE IF NOT EXISTS media_assets (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  filename VARCHAR(255) NOT NULL,
  original_filename VARCHAR(255) NOT NULL,
  file_type VARCHAR(50) NOT NULL,
  file_size BIGINT NOT NULL,
  mime_type VARCHAR(100) NOT NULL,
  storage_path TEXT NOT NULL,
  cdn_url TEXT,
  
  -- Media metadata
  width INTEGER,
  height INTEGER,
  duration INTEGER, -- for videos/audio in seconds
  alt_text TEXT,
  caption TEXT,
  
  -- Organization
  folder_path TEXT DEFAULT '/',
  tags TEXT[],
  
  -- Access control
  is_public BOOLEAN DEFAULT false,
  access_level VARCHAR(20) DEFAULT 'free',
  
  -- Tracking
  upload_by UUID REFERENCES auth.users(id),
  download_count INTEGER DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content-media relationships
CREATE TABLE IF NOT EXISTS content_media (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  content_id UUID REFERENCES learning_content(id) ON DELETE CASCADE,
  media_id UUID REFERENCES media_assets(id) ON DELETE CASCADE,
  usage_type VARCHAR(50) NOT NULL, -- featured, inline, attachment, thumbnail
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content templates for rapid creation
CREATE TABLE IF NOT EXISTS content_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  content_type_id UUID REFERENCES content_types(id),
  template_data JSONB NOT NULL DEFAULT '{}',
  preview_image_url TEXT,
  is_public BOOLEAN DEFAULT false,
  usage_count INTEGER DEFAULT 0,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content workflows and approval process
CREATE TABLE IF NOT EXISTS content_workflows (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  content_id UUID REFERENCES learning_content(id) ON DELETE CASCADE,
  workflow_type VARCHAR(50) NOT NULL, -- draft, review, approved, published, archived
  status VARCHAR(50) NOT NULL,
  assigned_to UUID REFERENCES auth.users(id),
  reviewer_id UUID REFERENCES auth.users(id),
  comments TEXT,
  due_date TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content analytics and tracking
CREATE TABLE IF NOT EXISTS content_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  content_id UUID REFERENCES learning_content(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id),
  event_type VARCHAR(50) NOT NULL, -- view, start, complete, bookmark, share, rate
  event_data JSONB DEFAULT '{}',
  session_id VARCHAR(100),
  ip_address INET,
  user_agent TEXT,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default content types
INSERT INTO content_types (name, display_name, description, schema_definition) VALUES
('lesson', 'Text Lesson', 'Traditional text-based learning content', '{
  "type": "object",
  "properties": {
    "content": {"type": "string"},
    "summary": {"type": "string"},
    "keyPoints": {"type": "array", "items": {"type": "string"}}
  }
}'),
('video', 'Video Lesson', 'Video-based learning content', '{
  "type": "object",
  "properties": {
    "videoUrl": {"type": "string"},
    "transcript": {"type": "string"},
    "chapters": {"type": "array", "items": {
      "type": "object",
      "properties": {
        "title": {"type": "string"},
        "timestamp": {"type": "number"}
      }
    }}
  }
}'),
('interactive', 'Interactive Module', 'Hands-on interactive learning', '{
  "type": "object",
  "properties": {
    "instructions": {"type": "string"},
    "environment": {"type": "string"},
    "tasks": {"type": "array", "items": {"type": "string"}},
    "hints": {"type": "array", "items": {"type": "string"}}
  }
}'),
('quiz', 'Quiz/Assessment', 'Knowledge assessment content', '{
  "type": "object",
  "properties": {
    "questions": {"type": "array", "items": {
      "type": "object",
      "properties": {
        "question": {"type": "string"},
        "type": {"type": "string"},
        "options": {"type": "array"},
        "correctAnswer": {"type": "string"},
        "explanation": {"type": "string"}
      }
    }}
  }
}')
ON CONFLICT (name) DO NOTHING;

-- Insert default categories
INSERT INTO content_categories (name, slug, description) VALUES
('Web Security', 'web-security', 'Web application security fundamentals'),
('Network Security', 'network-security', 'Network infrastructure security'),
('Cryptography', 'cryptography', 'Cryptographic principles and applications'),
('Penetration Testing', 'penetration-testing', 'Ethical hacking and penetration testing'),
('Incident Response', 'incident-response', 'Security incident handling and response'),
('Compliance', 'compliance', 'Security compliance and governance')
ON CONFLICT (slug) DO NOTHING;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_learning_content_category ON learning_content(category_id);
CREATE INDEX IF NOT EXISTS idx_learning_content_type ON learning_content(content_type_id);
CREATE INDEX IF NOT EXISTS idx_learning_content_published ON learning_content(is_published, publish_date);
CREATE INDEX IF NOT EXISTS idx_learning_content_access ON learning_content(access_level);
CREATE INDEX IF NOT EXISTS idx_learning_content_tags ON learning_content USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_content_blocks_content ON content_blocks(content_id, sort_order);
CREATE INDEX IF NOT EXISTS idx_content_analytics_content ON content_analytics(content_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_content_analytics_user ON content_analytics(user_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_media_assets_type ON media_assets(file_type, is_public);

-- Enable RLS
ALTER TABLE content_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE media_assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Content categories are viewable by everyone" ON content_categories FOR SELECT USING (is_active = true);
CREATE POLICY "Content types are viewable by everyone" ON content_types FOR SELECT USING (is_active = true);

CREATE POLICY "Published content is viewable by everyone" ON learning_content FOR SELECT USING (
  is_published = true AND publish_date <= NOW()
);

CREATE POLICY "Content creators can manage their content" ON learning_content FOR ALL USING (
  auth.uid() = author_id OR 
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin', 'content_manager'))
);

CREATE POLICY "Content blocks follow content permissions" ON content_blocks FOR SELECT USING (
  EXISTS (SELECT 1 FROM learning_content WHERE learning_content.id = content_blocks.content_id AND learning_content.is_published = true)
);

CREATE POLICY "Public media is viewable by everyone" ON media_assets FOR SELECT USING (is_public = true);

CREATE POLICY "Users can track their own analytics" ON content_analytics FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can view their own analytics" ON content_analytics FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Admins can view all analytics" ON content_analytics FOR SELECT USING (
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin'))
);
