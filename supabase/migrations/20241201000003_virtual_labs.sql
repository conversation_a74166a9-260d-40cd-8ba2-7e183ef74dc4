-- Virtual Labs System Database Schema
-- Comprehensive schema for live virtual labs and hands-on environments

-- Lab templates and configurations
CREATE TABLE IF NOT EXISTS lab_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  category_id UUID REFERENCES content_categories(id),
  
  -- Lab configuration
  lab_type VARCHAR(50) NOT NULL, -- docker, vm, cloud, simulation
  base_image VARCHAR(255), -- Docker image or VM template
  resource_requirements JSONB DEFAULT '{}', -- CPU, memory, storage requirements
  network_config JSONB DEFAULT '{}', -- Network topology and settings
  
  -- Environment setup
  setup_scripts TEXT[], -- Initialization scripts
  environment_variables JSONB DEFAULT '{}',
  exposed_ports INTEGER[],
  volume_mounts JSONB DEFAULT '{}',
  
  -- Lab content
  instructions TEXT NOT NULL,
  learning_objectives TEXT[],
  prerequisites TEXT[],
  estimated_duration INTEGER, -- in minutes
  difficulty_level VARCHAR(20) DEFAULT 'intermediate',
  
  -- Tools and software
  installed_tools TEXT[],
  required_files JSONB DEFAULT '{}',
  
  -- Access control
  access_level VARCHAR(20) DEFAULT 'premium',
  max_concurrent_instances INTEGER DEFAULT 10,
  max_session_duration INTEGER DEFAULT 120, -- in minutes
  
  -- Validation and assessment
  validation_scripts TEXT[],
  success_criteria JSONB DEFAULT '{}',
  auto_assessment BOOLEAN DEFAULT false,
  
  -- Metadata
  tags TEXT[],
  version VARCHAR(20) DEFAULT '1.0',
  is_active BOOLEAN DEFAULT true,
  
  -- Authoring
  created_by UUID REFERENCES auth.users(id),
  last_modified_by UUID REFERENCES auth.users(id),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Active lab instances
CREATE TABLE IF NOT EXISTS lab_instances (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  template_id UUID REFERENCES lab_templates(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id),
  
  -- Instance details
  instance_name VARCHAR(100) NOT NULL,
  container_id VARCHAR(255), -- Docker container ID or VM ID
  status VARCHAR(20) DEFAULT 'starting', -- starting, running, stopping, stopped, error
  
  -- Network and access
  internal_ip INET,
  external_ip INET,
  access_url TEXT,
  vnc_url TEXT,
  ssh_port INTEGER,
  
  -- Resource usage
  cpu_limit INTEGER, -- CPU cores
  memory_limit INTEGER, -- Memory in MB
  storage_limit INTEGER, -- Storage in GB
  
  -- Session management
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  auto_destroy BOOLEAN DEFAULT true,
  
  -- Progress tracking
  progress_data JSONB DEFAULT '{}',
  completed_tasks TEXT[],
  current_step INTEGER DEFAULT 1,
  
  -- Performance metrics
  cpu_usage DECIMAL(5,2) DEFAULT 0,
  memory_usage DECIMAL(5,2) DEFAULT 0,
  network_usage BIGINT DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lab sessions and user interactions
CREATE TABLE IF NOT EXISTS lab_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  instance_id UUID REFERENCES lab_instances(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id),
  
  -- Session details
  session_token VARCHAR(255) UNIQUE NOT NULL,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ended_at TIMESTAMP WITH TIME ZONE,
  duration INTEGER, -- in seconds
  
  -- Activity tracking
  commands_executed INTEGER DEFAULT 0,
  files_created INTEGER DEFAULT 0,
  files_modified INTEGER DEFAULT 0,
  
  -- Progress and completion
  tasks_completed INTEGER DEFAULT 0,
  total_tasks INTEGER DEFAULT 0,
  completion_percentage DECIMAL(5,2) DEFAULT 0,
  final_score INTEGER,
  
  -- Session data
  activity_log JSONB DEFAULT '[]',
  screenshots TEXT[],
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lab activities and command tracking
CREATE TABLE IF NOT EXISTS lab_activities (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  session_id UUID REFERENCES lab_sessions(id) ON DELETE CASCADE,
  instance_id UUID REFERENCES lab_instances(id) ON DELETE CASCADE,
  
  -- Activity details
  activity_type VARCHAR(50) NOT NULL, -- command, file_operation, network_request, etc.
  activity_data JSONB NOT NULL DEFAULT '{}',
  
  -- Context
  working_directory TEXT,
  user_context VARCHAR(50),
  
  -- Timing
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  duration INTEGER, -- in milliseconds
  
  -- Results
  exit_code INTEGER,
  output_text TEXT,
  error_text TEXT,
  
  -- Classification
  is_successful BOOLEAN,
  is_relevant BOOLEAN DEFAULT true, -- relevant to lab objectives
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lab assessments and validation
CREATE TABLE IF NOT EXISTS lab_assessments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  template_id UUID REFERENCES lab_templates(id) ON DELETE CASCADE,
  session_id UUID REFERENCES lab_sessions(id) ON DELETE CASCADE,
  
  -- Assessment details
  assessment_type VARCHAR(50) NOT NULL, -- automatic, manual, hybrid
  validation_rules JSONB NOT NULL DEFAULT '{}',
  
  -- Results
  passed BOOLEAN DEFAULT false,
  score INTEGER DEFAULT 0,
  max_score INTEGER DEFAULT 100,
  
  -- Feedback
  feedback TEXT,
  suggestions TEXT[],
  
  -- Validation details
  validated_at TIMESTAMP WITH TIME ZONE,
  validated_by UUID REFERENCES auth.users(id),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lab resources and file management
CREATE TABLE IF NOT EXISTS lab_resources (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  template_id UUID REFERENCES lab_templates(id) ON DELETE CASCADE,
  
  -- Resource details
  resource_type VARCHAR(50) NOT NULL, -- file, script, dataset, tool
  resource_name VARCHAR(255) NOT NULL,
  file_path TEXT,
  download_url TEXT,
  
  -- Content
  content TEXT,
  file_size BIGINT,
  mime_type VARCHAR(100),
  
  -- Metadata
  description TEXT,
  is_required BOOLEAN DEFAULT true,
  install_order INTEGER DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lab analytics and performance metrics
CREATE TABLE IF NOT EXISTS lab_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  template_id UUID REFERENCES lab_templates(id) ON DELETE CASCADE,
  
  -- Usage statistics
  total_launches INTEGER DEFAULT 0,
  successful_completions INTEGER DEFAULT 0,
  average_duration INTEGER DEFAULT 0,
  average_score DECIMAL(5,2) DEFAULT 0,
  
  -- Performance metrics
  average_cpu_usage DECIMAL(5,2) DEFAULT 0,
  average_memory_usage DECIMAL(5,2) DEFAULT 0,
  peak_concurrent_instances INTEGER DEFAULT 0,
  
  -- User engagement
  unique_users INTEGER DEFAULT 0,
  repeat_users INTEGER DEFAULT 0,
  completion_rate DECIMAL(5,2) DEFAULT 0,
  
  -- Calculated at
  calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(template_id)
);

-- Insert default lab templates
INSERT INTO lab_templates (name, description, lab_type, base_image, instructions, learning_objectives, difficulty_level) VALUES
('Web Security Basics', 'Introduction to web application security testing', 'docker', 'kalilinux/kali-rolling', 
 'Learn the fundamentals of web application security by exploring common vulnerabilities in a safe environment.',
 ARRAY['Understand OWASP Top 10', 'Perform basic SQL injection', 'Identify XSS vulnerabilities'],
 'beginner'),
 
('Network Penetration Testing', 'Advanced network security assessment', 'docker', 'parrotsec/security',
 'Conduct comprehensive network penetration testing using industry-standard tools and techniques.',
 ARRAY['Network reconnaissance', 'Vulnerability scanning', 'Exploitation techniques', 'Post-exploitation'],
 'advanced'),
 
('Malware Analysis Lab', 'Safe malware analysis environment', 'vm', 'remnux-vm-template',
 'Analyze malware samples in a controlled environment using static and dynamic analysis techniques.',
 ARRAY['Static analysis techniques', 'Dynamic analysis', 'Behavioral analysis', 'Report generation'],
 'expert'),
 
('Cloud Security Assessment', 'AWS/Azure security testing', 'cloud', 'aws-security-toolkit',
 'Learn to assess cloud infrastructure security and identify misconfigurations.',
 ARRAY['Cloud architecture review', 'IAM assessment', 'Storage security', 'Network security'],
 'intermediate')
ON CONFLICT DO NOTHING;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_lab_templates_category ON lab_templates(category_id);
CREATE INDEX IF NOT EXISTS idx_lab_templates_type ON lab_templates(lab_type);
CREATE INDEX IF NOT EXISTS idx_lab_templates_active ON lab_templates(is_active, access_level);
CREATE INDEX IF NOT EXISTS idx_lab_instances_user ON lab_instances(user_id, status);
CREATE INDEX IF NOT EXISTS idx_lab_instances_template ON lab_instances(template_id, status);
CREATE INDEX IF NOT EXISTS idx_lab_instances_expires ON lab_instances(expires_at, auto_destroy);
CREATE INDEX IF NOT EXISTS idx_lab_sessions_instance ON lab_sessions(instance_id);
CREATE INDEX IF NOT EXISTS idx_lab_sessions_user ON lab_sessions(user_id, started_at);
CREATE INDEX IF NOT EXISTS idx_lab_activities_session ON lab_activities(session_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_lab_activities_type ON lab_activities(activity_type, timestamp);

-- Enable RLS
ALTER TABLE lab_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE lab_instances ENABLE ROW LEVEL SECURITY;
ALTER TABLE lab_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE lab_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE lab_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE lab_resources ENABLE ROW LEVEL SECURITY;
ALTER TABLE lab_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Active lab templates are viewable by everyone" ON lab_templates FOR SELECT USING (is_active = true);
CREATE POLICY "Lab creators can manage their templates" ON lab_templates FOR ALL USING (
  auth.uid() = created_by OR 
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin', 'instructor'))
);

CREATE POLICY "Users can view their own lab instances" ON lab_instances FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own lab instances" ON lab_instances FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own lab instances" ON lab_instances FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Admins can view all lab instances" ON lab_instances FOR SELECT USING (
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin'))
);

CREATE POLICY "Users can manage their own lab sessions" ON lab_sessions FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage their own lab activities" ON lab_activities FOR ALL USING (
  EXISTS (SELECT 1 FROM lab_sessions WHERE lab_sessions.id = lab_activities.session_id AND lab_sessions.user_id = auth.uid())
);

CREATE POLICY "Lab resources are viewable by everyone" ON lab_resources FOR SELECT USING (true);
CREATE POLICY "Instructors can manage lab resources" ON lab_resources FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'super_admin', 'instructor'))
);

-- Functions for lab management
CREATE OR REPLACE FUNCTION create_lab_instance(template_id UUID, user_id UUID)
RETURNS UUID AS $$
DECLARE
  instance_id UUID;
  template_record lab_templates%ROWTYPE;
  expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Get template details
  SELECT * INTO template_record FROM lab_templates WHERE id = template_id AND is_active = true;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Lab template not found or inactive';
  END IF;
  
  -- Calculate expiration time
  expires_at := NOW() + INTERVAL '1 minute' * template_record.max_session_duration;
  
  -- Create instance
  INSERT INTO lab_instances (
    template_id,
    user_id,
    instance_name,
    status,
    expires_at,
    cpu_limit,
    memory_limit
  ) VALUES (
    template_id,
    user_id,
    template_record.name || '-' || EXTRACT(EPOCH FROM NOW())::TEXT,
    'starting',
    expires_at,
    COALESCE((template_record.resource_requirements->>'cpu')::INTEGER, 2),
    COALESCE((template_record.resource_requirements->>'memory')::INTEGER, 2048)
  ) RETURNING id INTO instance_id;
  
  RETURN instance_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup expired instances
CREATE OR REPLACE FUNCTION cleanup_expired_instances()
RETURNS INTEGER AS $$
DECLARE
  cleanup_count INTEGER := 0;
BEGIN
  -- Update expired instances to stopped status
  UPDATE lab_instances 
  SET status = 'stopped', updated_at = NOW()
  WHERE expires_at < NOW() 
    AND status IN ('running', 'starting')
    AND auto_destroy = true;
  
  GET DIAGNOSTICS cleanup_count = ROW_COUNT;
  
  RETURN cleanup_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to track lab activity
CREATE OR REPLACE FUNCTION track_lab_activity(
  session_id UUID,
  activity_type VARCHAR(50),
  activity_data JSONB,
  working_directory TEXT DEFAULT NULL,
  exit_code INTEGER DEFAULT NULL,
  output_text TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  activity_id UUID;
  instance_id UUID;
BEGIN
  -- Get instance ID from session
  SELECT lab_sessions.instance_id INTO instance_id
  FROM lab_sessions 
  WHERE lab_sessions.id = session_id;
  
  -- Insert activity record
  INSERT INTO lab_activities (
    session_id,
    instance_id,
    activity_type,
    activity_data,
    working_directory,
    exit_code,
    output_text,
    is_successful
  ) VALUES (
    session_id,
    instance_id,
    activity_type,
    activity_data,
    working_directory,
    exit_code,
    output_text,
    COALESCE(exit_code = 0, true)
  ) RETURNING id INTO activity_id;
  
  -- Update session last activity
  UPDATE lab_sessions 
  SET updated_at = NOW(),
      commands_executed = commands_executed + CASE WHEN activity_type = 'command' THEN 1 ELSE 0 END
  WHERE id = session_id;
  
  -- Update instance last accessed
  UPDATE lab_instances 
  SET last_accessed = NOW()
  WHERE id = instance_id;
  
  RETURN activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
