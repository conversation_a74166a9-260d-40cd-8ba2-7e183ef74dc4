# XCerberus Cybersecurity Learning Platform

XCerberus is a comprehensive cybersecurity learning platform that offers challenges, learning modules, and a competitive environment for users to enhance their cybersecurity skills.

## Features

- **Dynamic Content**: Real-time data from Supabase database
- **Cyber Threat Visualization**: Interactive 3D globe showing real-time cyber threats with educational content
- **Subscription Tiers**: Free, Premium, and Business tiers with different access levels
- **Interactive Challenges**: Hands-on cybersecurity challenges across different difficulty levels
- **Learning Modules**: Comprehensive learning content for various cybersecurity topics
- **Leaderboard**: Real-time competitive leaderboard
- **User Profiles**: Detailed user profiles with progress tracking
- **Store**: In-platform store for purchasing items with coins
- **Analytics**: Detailed analytics and progress tracking
- **Dark/Light Mode**: Fully responsive UI with dark and light mode support

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Docker Desktop (for local Supabase)
- Supabase CLI (for local development)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/xcerberus.git
   cd xcerberus
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. **Choose your development environment:**

#### Option A: Local Supabase (Recommended for Development)

1. Install Supabase CLI:
   ```bash
   brew install supabase/tap/supabase
   ```

2. Set up local Supabase:
   ```bash
   npm run supabase:setup
   ```

3. Start development with local Supabase:
   ```bash
   npm run dev:local
   ```

#### Option B: Remote Supabase

1. Create a `.env` file with your remote Supabase credentials:
   ```env
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

2. Set up the remote database:
   - Go to your Supabase project
   - Navigate to the SQL Editor
   - Copy the contents of `supabase/schema.sql` and run it

3. Start the development server:
   ```bash
   npm run dev
   ```

### Running with Docker Compose

1. Build and start all services:
   ```bash
   docker-compose up --build
   ```

2. Access the application at http://localhost:3000

## Database Setup

### Local Supabase (Recommended)

The project is configured to use local Supabase for development:

1. **Automatic Setup**: Run `npm run supabase:setup` for complete setup
2. **Manual Setup**: See [Local Supabase Setup Guide](docs/LOCAL_SUPABASE_SETUP.md)

**Available Commands:**
- `npm run supabase:start` - Start local Supabase
- `npm run supabase:stop` - Stop local Supabase
- `npm run supabase:status` - Check status
- `npm run test:supabase` - Test connection

**Access Points:**
- Supabase Studio: http://localhost:54323
- API: http://127.0.0.1:54321
- Database: postgresql://postgres:postgres@localhost:54322/postgres

### Remote Supabase

For remote Supabase setup:

1. Create a new Supabase project
2. Navigate to the SQL Editor in your Supabase dashboard
3. Copy the contents of `supabase/schema.sql` and run it in the SQL Editor
4. This will create all the necessary tables, functions, and policies for the application

## Project Structure

- `src/components`: UI components
- `src/contexts`: React contexts for state management
- `src/pages`: Page components
- `src/lib`: Utility functions and API clients
- `src/styles`: CSS and styling files
- `src/services`: Backend microservices
  - `threats`: Cyber threat data processing service
  - `auth`: Authentication service
  - `learning`: Learning content service
  - `challenges`: Challenges service
- `supabase`: Supabase configuration and schema

## Subscription Tiers

### Free Tier
- Access to basic challenges
- Limited learning modules
- Basic leaderboard
- Community forum access

### Premium Tier (399 INR)
- All free features
- Access to premium challenges
- Full learning modules
- Advanced analytics
- Priority support
- Team collaboration (up to 3 members)

### Business Tier (Contact for pricing)
- All premium features
- Access to business challenges
- Custom learning paths
- Advanced team management
- Dedicated support
- Custom reporting
- API access

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [React](https://reactjs.org/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Supabase](https://supabase.io/)
- [Vite](https://vitejs.dev/)
- [Three.js](https://threejs.org/)
- [Docker](https://www.docker.com/)
- [AbuseIPDB](https://www.abuseipdb.com/)
