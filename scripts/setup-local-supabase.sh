#!/bin/bash

# Setup Local Supabase Development Environment
# This script sets up a local Supabase instance for development

set -e

echo "🚀 Setting up Local Supabase Development Environment..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "   brew install supabase/tap/supabase"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "❌ Docker is not running. Please start Docker Desktop and try again."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Navigate to project directory
cd "$(dirname "$0")/.."

# Check if Supabase is already initialized
if [ ! -f "supabase/config.toml" ]; then
    echo "🔧 Initializing Supabase project..."
    supabase init
else
    echo "✅ Supabase project already initialized"
fi

# Start Supabase local development stack
echo "🐳 Starting Supabase local development stack..."
echo "This may take a few minutes on first run as Docker images are downloaded..."

if supabase start; then
    echo "✅ Supabase started successfully!"
    
    # Get the local credentials
    echo ""
    echo "📋 Local Supabase Credentials:"
    supabase status
    
    echo ""
    echo "🔧 Updating local environment configuration..."
    
    # Create .env.local with the actual credentials from supabase status
    API_URL=$(supabase status | grep "API URL" | awk '{print $3}')
    ANON_KEY=$(supabase status | grep "anon key" | awk '{print $3}')
    
    cat > .env.local << EOF
# Local Supabase Configuration
# Generated by setup-local-supabase.sh

# Local Supabase URLs and Keys
VITE_SUPABASE_URL=${API_URL}
VITE_SUPABASE_ANON_KEY=${ANON_KEY}

# Backend service environment variables for local development
SUPABASE_URL=${API_URL}
SUPABASE_KEY=${ANON_KEY}

# Keep existing API keys
VITE_GEMINI_API_KEY=AIzaSyCjrRX2vRmhFk1OQxHLleXMq0pGJC6xXrM
VITE_ABUSEIPDB_API_KEY=********************************************************************************
VITE_OTX_API_KEY=437e960dfa3557cd103b887b8948554f86a97f7ab562a13e546311a1cc5cebe0

# Service Ports
AUTH_SERVICE_PORT=3001
LEARNING_SERVICE_PORT=3002
CHALLENGES_SERVICE_PORT=3003
AI_SERVICE_PORT=3004
THREATS_SERVICE_PORT=3005

# Frontend Configuration
REACT_APP_API_URL=http://localhost:3001

# Additional API Keys
GOOGLE_API_KEY=your_google_api_key_here
ABUSEIPDB_API_KEY=********************************************************************************
OTX_API_KEY=437e960dfa3557cd103b887b8948554f86a97f7ab562a13e546311a1cc5cebe0
EOF

    echo "✅ Created .env.local with local Supabase credentials"
    
    echo ""
    echo "🎉 Local Supabase setup complete!"
    echo ""
    echo "📖 Next steps:"
    echo "   1. Copy .env.local to .env to use local Supabase: cp .env.local .env"
    echo "   2. Run database migrations: npm run db:migrate"
    echo "   3. Start the development server: npm run dev"
    echo ""
    echo "🌐 Access points:"
    echo "   - Supabase Studio: http://localhost:54323"
    echo "   - API: ${API_URL}"
    echo "   - Database: postgresql://postgres:postgres@localhost:54322/postgres"
    
else
    echo "❌ Failed to start Supabase. Please check Docker and try again."
    echo "💡 Troubleshooting tips:"
    echo "   - Ensure Docker Desktop is running"
    echo "   - Try: docker system prune -f"
    echo "   - Restart Docker Desktop"
    exit 1
fi
