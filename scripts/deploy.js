const hre = require("hardhat");
const fs = require('fs');

async function main() {
  console.log("🚀 Deploying XCerberus Web3 Ecosystem...");
  
  // Get the ContractFactory and Signers here.
  const [deployer] = await hre.ethers.getSigners();
  
  console.log("Deploying contracts with the account:", deployer.address);
  console.log("Account balance:", (await deployer.getBalance()).toString());

  // Deploy XCyber Token
  console.log("\n📄 Deploying XCyber Token...");
  const XCyberToken = await hre.ethers.getContractFactory("XCyberToken");
  const xcyberToken = await XCyberToken.deploy();
  await xcyberToken.deployed();
  console.log("✅ XCyber Token deployed to:", xcyberToken.address);

  // Deploy XCerberus Certificates
  console.log("\n🏆 Deploying XCerberus Certificates...");
  const XCerberusCertificates = await hre.ethers.getContractFactory("XCerberusCertificates");
  const certificates = await XCerberusCertificates.deploy(xcyberToken.address);
  await certificates.deployed();
  console.log("✅ XCerberus Certificates deployed to:", certificates.address);

  // Deploy XCerberus Marketplace
  console.log("\n🛒 Deploying XCerberus Marketplace...");
  const XCerberusMarketplace = await hre.ethers.getContractFactory("XCerberusMarketplace");
  const marketplace = await XCerberusMarketplace.deploy(xcyberToken.address, certificates.address);
  await marketplace.deployed();
  console.log("✅ XCerberus Marketplace deployed to:", marketplace.address);

  // Deploy XCerberus DAO
  console.log("\n🏛️ Deploying XCerberus DAO...");
  const XCerberusDAO = await hre.ethers.getContractFactory("XCerberusDAO");
  const dao = await XCerberusDAO.deploy(xcyberToken.address, certificates.address);
  await dao.deployed();
  console.log("✅ XCerberus DAO deployed to:", dao.address);

  // Set up permissions
  console.log("\n🔧 Setting up permissions...");
  
  // Add certificates contract as minter for token rewards
  await xcyberToken.addMinter(certificates.address);
  console.log("✅ Added certificates contract as token minter");
  
  // Add marketplace contract as minter for purchase rewards
  await xcyberToken.addMinter(marketplace.address);
  console.log("✅ Added marketplace contract as token minter");
  
  // Add deployer as authorized issuer for certificates
  await certificates.addAuthorizedIssuer(deployer.address);
  console.log("✅ Added deployer as authorized certificate issuer");

  // Save deployment addresses
  const deploymentInfo = {
    network: hre.network.name,
    chainId: hre.network.config.chainId,
    deployer: deployer.address,
    contracts: {
      XCyberToken: xcyberToken.address,
      XCerberusCertificates: certificates.address,
      XCerberusMarketplace: marketplace.address,
      XCerberusDAO: dao.address
    },
    deploymentDate: new Date().toISOString()
  };

  // Write to file
  fs.writeFileSync(
    'deployment-info.json',
    JSON.stringify(deploymentInfo, null, 2)
  );

  // Create environment file for frontend
  const envContent = `# XCerberus Web3 Contract Addresses
REACT_APP_XCYBER_TOKEN_ADDRESS=${xcyberToken.address}
REACT_APP_CERTIFICATES_ADDRESS=${certificates.address}
REACT_APP_MARKETPLACE_ADDRESS=${marketplace.address}
REACT_APP_DAO_ADDRESS=${dao.address}
REACT_APP_CHAIN_ID=${hre.network.config.chainId}
REACT_APP_NETWORK_NAME=${hre.network.name}

# Add your Infura/Alchemy API key here
REACT_APP_INFURA_ID=your-infura-project-id
`;

  fs.writeFileSync('.env.contracts', envContent);

  console.log("\n🎉 Deployment Summary:");
  console.log("========================");
  console.log("Network:", hre.network.name);
  console.log("Chain ID:", hre.network.config.chainId);
  console.log("Deployer:", deployer.address);
  console.log("\n📄 Contract Addresses:");
  console.log("XCyber Token:", xcyberToken.address);
  console.log("Certificates:", certificates.address);
  console.log("Marketplace:", marketplace.address);
  console.log("DAO:", dao.address);
  
  console.log("\n📝 Files Created:");
  console.log("- deployment-info.json (deployment details)");
  console.log("- .env.contracts (environment variables for frontend)");
  
  console.log("\n🔧 Next Steps:");
  console.log("1. Copy contract addresses to your .env file");
  console.log("2. Add your Infura/Alchemy API key");
  console.log("3. Update the Web3Context with correct addresses");
  console.log("4. Test the contracts on your local network");
  
  console.log("\n✨ XCerberus Web3 Ecosystem deployed successfully!");
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
