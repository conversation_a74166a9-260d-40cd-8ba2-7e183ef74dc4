#!/usr/bin/env node

/**
 * Test Supabase Connection
 * This script tests the connection to Supabase (local or remote)
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔧 Testing Supabase Connection...\n');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Required variables:');
  console.log('  - VITE_SUPABASE_URL');
  console.log('  - VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

const isLocal = supabaseUrl.includes('127.0.0.1') || supabaseUrl.includes('localhost');

console.log('📋 Configuration:');
console.log(`  Environment: ${isLocal ? 'LOCAL' : 'REMOTE'}`);
console.log(`  URL: ${supabaseUrl}`);
console.log(`  Key: ${supabaseKey.substring(0, 20)}...`);
console.log('');

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  try {
    console.log('🔍 Testing basic connection...');
    
    // Test 1: Basic health check
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (error && error.code !== 'PGRST116') { // PGRST116 = table doesn't exist, which is OK
      console.log('⚠️  Database query test:', error.message);
    } else {
      console.log('✅ Database connection successful');
    }

    // Test 2: Auth service
    console.log('🔍 Testing auth service...');
    const { data: authData, error: authError } = await supabase.auth.getSession();
    
    if (authError) {
      console.log('⚠️  Auth service test:', authError.message);
    } else {
      console.log('✅ Auth service accessible');
    }

    // Test 3: List available tables (if any)
    console.log('🔍 Checking available tables...');
    const { data: tables, error: tablesError } = await supabase
      .rpc('get_schema_tables')
      .catch(() => null);

    if (tables) {
      console.log('✅ Available tables:', tables.length);
    } else {
      console.log('ℹ️  Could not list tables (this is normal for new setups)');
    }

    console.log('\n🎉 Connection test completed successfully!');
    
    if (isLocal) {
      console.log('\n📖 Local Development URLs:');
      console.log('  - Supabase Studio: http://localhost:54323');
      console.log('  - API: http://127.0.0.1:54321');
      console.log('  - Database: postgresql://postgres:postgres@localhost:54322/postgres');
    }

  } catch (error) {
    console.error('❌ Connection test failed:', error.message);
    
    if (isLocal) {
      console.log('\n💡 Troubleshooting for local setup:');
      console.log('  1. Ensure Supabase is running: npm run supabase:start');
      console.log('  2. Check Docker is running: docker ps');
      console.log('  3. Verify ports are not in use: lsof -i :54321');
    } else {
      console.log('\n💡 Troubleshooting for remote setup:');
      console.log('  1. Check your internet connection');
      console.log('  2. Verify Supabase project is active');
      console.log('  3. Check API keys are correct');
    }
    
    process.exit(1);
  }
}

testConnection();
